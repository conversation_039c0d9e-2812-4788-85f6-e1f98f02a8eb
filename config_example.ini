# Archivo de configuración de ejemplo para XUI Database Manager
# Copia este archivo como 'config.ini' y modifica los valores según tu configuración

[DATABASE]
# Configuración de la base de datos MariaDB
host = *************
user = tu_usuario
password = tu_contraseña
database = xui
port = 3306

[SETTINGS]
# Configuraciones de la aplicación
auto_connect = false
remember_credentials = false
log_level = INFO

[UI]
# Configuraciones de la interfaz
window_width = 1200
window_height = 800
theme = default

# NOTA: Este archivo contiene credenciales sensibles
# NO lo subas a repositorios públicos
# Agrega 'config.ini' a tu .gitignore
