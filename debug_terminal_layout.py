#!/usr/bin/env python3
"""
Debug script para verificar el layout del terminal en la GUI principal
"""

import tkinter as tk
from tkinter import ttk
import tkinter.scrolledtext as scrolledtext
import time
import threading

def debug_gui_layout():
    """Debug the GUI layout to find terminal issues"""
    print("🔍 Debugging GUI layout...")
    
    try:
        from gui import XUIManagerGUI
        
        # Create GUI instance
        app = XUIManagerGUI()
        
        def debug_widgets():
            """Debug widget hierarchy and visibility"""
            time.sleep(3)  # Wait for GUI to fully load
            
            print("\n🔍 DEBUGGING WIDGET HIERARCHY:")
            
            # Check if log_text exists
            if hasattr(app, 'log_text'):
                print("✅ app.log_text exists")
                
                # Check widget properties
                try:
                    print(f"📊 Widget class: {app.log_text.__class__.__name__}")
                    print(f"📊 Master: {app.log_text.master}")
                    print(f"📊 Master class: {app.log_text.master.__class__.__name__}")
                    print(f"📊 Visible: {app.log_text.winfo_viewable()}")
                    print(f"📊 Mapped: {app.log_text.winfo_ismapped()}")
                    print(f"📊 Width: {app.log_text.winfo_width()}")
                    print(f"📊 Height: {app.log_text.winfo_height()}")
                    print(f"📊 X: {app.log_text.winfo_x()}")
                    print(f"📊 Y: {app.log_text.winfo_y()}")
                    
                    # Try to force visibility
                    app.log_text.update()
                    app.log_text.update_idletasks()
                    
                    # Test inserting text directly
                    app.log_text.insert('end', "🧪 DIRECT TEXT INSERT TEST\n")
                    app.log_text.see('end')
                    
                    print("✅ Direct text insert successful")
                    
                except Exception as e:
                    print(f"❌ Error checking widget: {e}")
                
                # Check parent hierarchy
                try:
                    widget = app.log_text
                    level = 0
                    print("\n📊 WIDGET HIERARCHY:")
                    while widget and level < 10:
                        print(f"  Level {level}: {widget.__class__.__name__} - {widget}")
                        if hasattr(widget, 'master'):
                            widget = widget.master
                        else:
                            break
                        level += 1
                        
                except Exception as e:
                    print(f"❌ Error checking hierarchy: {e}")
                    
            else:
                print("❌ app.log_text does NOT exist!")
                
                # Check what attributes app has
                print("\n📊 APP ATTRIBUTES:")
                attrs = [attr for attr in dir(app) if not attr.startswith('_')]
                for attr in attrs:
                    if 'log' in attr.lower() or 'text' in attr.lower() or 'terminal' in attr.lower():
                        print(f"  🔍 {attr}: {getattr(app, attr, 'N/A')}")
            
            # Try to send test messages
            try:
                if hasattr(app, 'log_message'):
                    print("\n🧪 Testing log_message function...")
                    app.log_message("🧪 DEBUG TEST MESSAGE", 'nvidia_green')
                    app.log_message("📊 This is a test from debug script", 'accent')
                    print("✅ log_message function works")
                else:
                    print("❌ log_message function does not exist")
            except Exception as e:
                print(f"❌ Error testing log_message: {e}")
        
        # Start debug in thread
        threading.Thread(target=debug_widgets, daemon=True).start()
        
        # Run GUI
        print("🚀 Starting GUI for debugging...")
        app.root.mainloop()
        
    except Exception as e:
        print(f"❌ Error in debug: {e}")
        import traceback
        traceback.print_exc()

def create_minimal_test():
    """Create minimal test with same structure as GUI"""
    print("🧪 Creating minimal test...")
    
    root = tk.Tk()
    root.title("⚡ XUI Database Manager - Gaming Terminal")
    root.geometry("1200x800")
    root.configure(bg='#0d1117')
    
    # Gaming colors
    colors = {
        'bg': '#0d1117',
        'fg': '#c9d1d9',
        'nvidia_green': '#76b900',
        'rog_red': '#ff0040',
        'accent': '#58a6ff',
        'warning': '#f85149',
        'success': '#3fb950',
        'surface': '#161b22',
        'border': '#30363d'
    }
    
    # Header
    header_frame = tk.Frame(root, bg=colors['bg'], height=50)
    header_frame.pack(fill='x', padx=10, pady=5)
    header_frame.pack_propagate(False)
    
    title_label = tk.Label(header_frame,
                          text="⚡ XUI DATABASE MANAGER - GAMING TERMINAL ⚡",
                          font=('Consolas', 12, 'bold'),
                          bg=colors['bg'],
                          fg=colors['nvidia_green'])
    title_label.pack(pady=10)
    
    # Main area
    main_frame = tk.Frame(root, bg=colors['bg'])
    main_frame.pack(fill='both', expand=True, padx=10, pady=5)
    
    # Left panel (connection)
    left_frame = tk.Frame(main_frame, bg=colors['surface'], width=350)
    left_frame.pack(side='left', fill='y', padx=(0, 10))
    left_frame.pack_propagate(False)
    
    conn_label = tk.Label(left_frame,
                         text="═══ CONNECTION ═══",
                         font=('Consolas', 10, 'bold'),
                         bg=colors['surface'],
                         fg=colors['nvidia_green'])
    conn_label.pack(pady=10)
    
    # Right panel (data + terminal)
    right_frame = tk.Frame(main_frame, bg=colors['bg'])
    right_frame.pack(side='right', fill='both', expand=True)
    
    # Data view section
    data_header = tk.Frame(right_frame, bg=colors['surface'], height=30)
    data_header.pack(fill='x', pady=(0, 2))
    data_header.pack_propagate(False)
    
    tk.Label(data_header,
            text="═══ DATA VIEW ═══",
            font=('Consolas', 10, 'bold'),
            bg=colors['surface'],
            fg=colors['nvidia_green']).pack(pady=5)
    
    # Data area (fixed height)
    data_frame = tk.Frame(right_frame, bg=colors['bg'], height=300)
    data_frame.pack(fill='x', pady=(0, 5))
    data_frame.pack_propagate(False)
    
    # Placeholder for data
    data_placeholder = tk.Label(data_frame,
                               text="📊 Data view area (treeview would go here)",
                               font=('Consolas', 10),
                               bg=colors['bg'],
                               fg=colors['fg'])
    data_placeholder.pack(expand=True)
    
    # Terminal section
    terminal_header = tk.Frame(right_frame, bg=colors['surface'], height=30)
    terminal_header.pack(fill='x', pady=(5, 2))
    terminal_header.pack_propagate(False)
    
    tk.Label(terminal_header,
            text="═══ TERMINAL OUTPUT ═══",
            font=('Consolas', 10, 'bold'),
            bg=colors['surface'],
            fg=colors['accent']).pack(pady=5)
    
    # Terminal area
    terminal_space = tk.Frame(right_frame, bg=colors['bg'], height=200)
    terminal_space.pack(fill='both', expand=True, padx=5, pady=5)
    terminal_space.pack_propagate(False)
    
    log_text = scrolledtext.ScrolledText(terminal_space,
                                        font=('Consolas', 10),
                                        bg=colors['bg'],
                                        fg=colors['fg'],
                                        insertbackground=colors['nvidia_green'],
                                        selectbackground=colors['accent'],
                                        selectforeground='white',
                                        relief='flat',
                                        bd=1)
    log_text.pack(fill='both', expand=True)
    
    # Add test messages
    def add_messages():
        time.sleep(1)
        
        messages = [
            ("⚡ XUI Database Manager - Gaming Terminal Initialized", colors['nvidia_green']),
            ("🎮 Gaming colors: NVIDIA Green + ASUS ROG Red", colors['rog_red']),
            ("🚀 Ready for high-performance database operations", colors['accent']),
            ("═" * 60, colors['fg']),
            ("🎮 TERMINAL OUTPUT WORKING", colors['nvidia_green']),
            ("📊 Data view above, terminal logs below", colors['accent']),
            ("✅ Layout test successful", colors['success'])
        ]
        
        for msg, color in messages:
            timestamp = time.strftime("%H:%M:%S")
            full_msg = f"[{timestamp}] {msg}\n"
            
            # Configure color tag
            tag_name = f"color_{color}"
            log_text.tag_configure(tag_name, foreground=color)
            
            # Insert message
            log_text.insert('end', full_msg, tag_name)
            log_text.see('end')
            root.update()
            time.sleep(0.5)
    
    # Start messages
    threading.Thread(target=add_messages, daemon=True).start()
    
    print("🚀 Minimal test window opened - check if terminal is visible")
    root.mainloop()

if __name__ == "__main__":
    print("🔍 Terminal Layout Debug Tools")
    print("\nSelect option:")
    print("1. Debug full GUI layout")
    print("2. Create minimal test")
    
    choice = input("\nChoice (1-2): ").strip()
    
    if choice == "1":
        debug_gui_layout()
    elif choice == "2":
        create_minimal_test()
    else:
        print("Invalid choice, running minimal test...")
        create_minimal_test()
