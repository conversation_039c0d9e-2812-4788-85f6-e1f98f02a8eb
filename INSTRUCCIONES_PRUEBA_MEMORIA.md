# 🧪 Instrucciones para Probar el Sistema de Memoria

## ✅ **CÓMO PROBAR QUE LA MEMORIA FUNCIONA**

### 🎯 **Pasos para Verificar la Memoria:**

#### **1. Abrir Selección Manual**
```
1. Ejecutar: python main.py
2. Ir a pestaña "Limpieza Inteligente"
3. Click en botón "Selección Manual"
4. Se abre workspace unificado con dos paneles
```

#### **2. Seleccionar Primera Película**
```
1. En panel izquierdo: Click en cualquier película
2. Panel derecho se actualiza automáticamente
3. Verificar que aparece información de la película
4. Verificar que hay checkboxes (☐/☑) en la tabla
```

#### **3. Modificar Selección**
```
1. Doble click en cualquier checkbox para cambiar selección
2. Verificar que el checkbox cambia (☐ ↔ ☑)
3. Verificar que las estadísticas se actualizan
4. <PERSON><PERSON>er<PERSON> contador: "💾 Selecciones guardadas: 1"
```

#### **4. Cambiar a Segunda Película**
```
1. En panel izquierdo: Click en OTRA película
2. Panel derecho se actualiza con nueva película
3. Verificar que contador sigue: "💾 Selecciones guardadas: 1"
4. Modificar selección en esta segunda película
5. Contador debe cambiar a: "💾 Selecciones guardadas: 2"
```

#### **5. Regresar a Primera Película**
```
1. En panel izquierdo: Click en la PRIMERA película otra vez
2. Panel derecho se actualiza
3. ✅ VERIFICAR: Los checkboxes están exactamente como los dejaste
4. ✅ VERIFICAR: Aparece "💾 (Selección guardada)" en el título
5. ✅ VERIFICAR: Las estadísticas coinciden con tu selección anterior
```

#### **6. Usar Botón Debug (Opcional)**
```
1. Click en "🔍 Debug Memoria" en panel derecho
2. Verificar información mostrada:
   - TMDB actual: [número]
   - Datos actuales: [cantidad] items
   - Selecciones guardadas: [cantidad]
   - MEMORIA GUARDADA: Lista de películas con selecciones
```

---

## 🎯 **INDICADORES DE QUE FUNCIONA CORRECTAMENTE**

### ✅ **Señales de Éxito:**
- **Contador dinámico**: "💾 Selecciones guardadas: X" se actualiza
- **Título con memoria**: "🎬 [Película] | TMDB: X | Symlinks: X 💾 (Selección guardada)"
- **Checkboxes preservados**: Al regresar a una película, los ☐/☑ están como los dejaste
- **Estadísticas correctas**: Los números coinciden con tu selección anterior
- **Recomendaciones actualizadas**: "💾 Selección personalizada restaurada"

### ❌ **Señales de Problema:**
- Contador siempre en "💾 Selecciones guardadas: 0"
- Checkboxes se resetean al cambiar de película
- No aparece "💾 (Selección guardada)" en títulos
- Estadísticas se resetean a valores iniciales

---

## 🔧 **FUNCIONALIDADES ADICIONALES A PROBAR**

### **Botones de Acción Rápida:**
```
1. "Todos los Symlinks" → Selecciona todos los symlinks
2. "Mejor Calidad" → Selecciona automáticamente la mejor calidad
3. "Limpiar Selección" → Deselecciona todo
4. Verificar que cada acción se guarda automáticamente
```

### **Ejecución de Selecciones:**
```
1. "💾 Guardar Selección" → Confirmación manual
2. "🚀 Ejecutar Limpieza" → Aplica selección actual
3. "🚀 Ejecutar Todas las Selecciones" → Procesamiento masivo
4. "✅ Hecho" → Cierra workspace
```

---

## 🎮 **ESCENARIO DE PRUEBA COMPLETO**

### **Test de 5 Películas:**
```
1. Seleccionar Película A → Modificar selección → Contador: 1
2. Seleccionar Película B → Modificar selección → Contador: 2  
3. Seleccionar Película C → Modificar selección → Contador: 3
4. Regresar a Película A → Verificar selección preservada
5. Regresar a Película B → Verificar selección preservada
6. Regresar a Película C → Verificar selección preservada
7. "🚀 Ejecutar Todas las Selecciones" → Procesamiento masivo
8. Contador debe resetear a: "💾 Selecciones guardadas: 0"
```

---

## 🚨 **SOLUCIÓN DE PROBLEMAS**

### **Si la Memoria NO Funciona:**

#### **Problema 1: Contador siempre en 0**
```
Causa: Eventos de selección no se disparan
Solución: Usar click simple en lugar de doble click
```

#### **Problema 2: Checkboxes se resetean**
```
Causa: Función restore_movie_selection no funciona
Solución: Verificar que stream_id coincide entre guardado y restauración
```

#### **Problema 3: Error al cambiar películas**
```
Causa: Variables no inicializadas correctamente
Solución: Verificar que saved_selections y movie_recommendations existen
```

### **Debug Manual:**
```
1. Click en "🔍 Debug Memoria"
2. Verificar que muestra datos correctos
3. Si no hay datos, el problema está en save_current_movie_selection
4. Si hay datos pero no se restauran, el problema está en restore_movie_selection
```

---

## 📊 **RESULTADOS ESPERADOS**

### **Después de Probar 3 Películas:**
```
💾 Selecciones guardadas: 3

MEMORIA GUARDADA:
TMDB 640146 (Ant-Man Quantumania): 1/3 seleccionadas
TMDB 423108 (El Conjuro 3): 2/4 seleccionadas  
TMDB 240 (El Padrino II): 1/3 seleccionadas
```

### **Al Ejecutar Todas las Selecciones:**
```
🚀 Procesando 3 películas...
✅ Copias eliminadas: 7
✅ Copias mantenidas: 4
✅ Ejecución completada

💾 Selecciones guardadas: 0 (memoria limpiada)
```

---

## 🎉 **CONFIRMACIÓN FINAL**

**El sistema de memoria funciona correctamente si:**

✅ Puedes navegar libremente entre películas sin perder selecciones
✅ El contador se actualiza dinámicamente
✅ Los checkboxes se preservan exactamente como los dejaste
✅ Puedes ejecutar todas las selecciones de una vez
✅ La memoria se limpia después de la ejecución masiva

**¡Si todos estos puntos funcionan, el sistema de memoria está perfecto!** 🧠💾✨
