#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gaming Connection Dialog - Versión limpia y funcional
"""

import tkinter as tk
from tkinter import ttk, messagebox

class SimpleConnectionDialog:
    def __init__(self, parent, config_manager=None, callback=None):
        self.parent = parent
        self.config_manager = config_manager
        self.callback = callback
        self.result = None
        self.saved_connections = []
        
        # Gaming Colors
        self.colors = {
            'bg_primary': '#0A0A0A',
            'bg_secondary': '#161616',
            'nvidia_green': '#76B900',
            'rog_red': '#CC0033',
            'gemini_purple': '#7B1FA2',
            'text_primary': '#FFFFFF',
            'text_secondary': '#CCCCCC'
        }
        
        self.setup_window()
        self.setup_ui()
        if config_manager:
            self.load_saved_connections()
    
    def setup_window(self):
        """Configurar ventana principal"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("🚀 Gaming Connection XUI")
        self.window.geometry("500x550")
        self.window.configure(bg=self.colors['bg_primary'])
        self.window.resizable(False, False)
        
        if self.parent:
            self.window.transient(self.parent)
            self.window.grab_set()
    
    def setup_ui(self):
        """Configurar interfaz de usuario"""
        # Título
        title = tk.Label(self.window, 
                        text="🚀 GAMING CONNECTION XUI v2", 
                        font=('Arial', 16, 'bold'),
                        bg=self.colors['bg_primary'],
                        fg=self.colors['nvidia_green'])
        title.pack(pady=15)
        
        # Subtítulo
        subtitle = tk.Label(self.window, 
                           text="⚡ TMDB Database Connection Terminal ⚡", 
                           font=('Arial', 10),
                           bg=self.colors['bg_primary'],
                           fg=self.colors['gemini_purple'])
        subtitle.pack(pady=5)
        
        # Conexiones guardadas (si hay config_manager)
        if self.config_manager:
            self.create_saved_connections_section()
        
        # Campos de conexión
        self.create_connection_fields()
        
        # Checkbox para guardar
        if self.config_manager:
            self.create_save_checkbox()
        
        # Botones
        self.create_buttons()
        
        # Status
        self.create_status()
    
    def create_saved_connections_section(self):
        """Crear sección de conexiones guardadas"""
        frame = tk.LabelFrame(self.window, 
                            text="🎮 Conexiones Guardadas", 
                            bg=self.colors['bg_secondary'],
                            fg=self.colors['nvidia_green'],
                            font=('Arial', 9, 'bold'))
        frame.pack(padx=20, pady=5, fill='x')
        
        self.connections_listbox = tk.Listbox(frame, 
                                            height=3,
                                            bg=self.colors['bg_primary'],
                                            fg=self.colors['text_primary'],
                                            selectbackground=self.colors['nvidia_green'],
                                            font=('Arial', 9))
        self.connections_listbox.pack(padx=10, pady=5, fill='x')
        self.connections_listbox.bind('<<ListboxSelect>>', self.on_connection_select)
    
    def create_connection_fields(self):
        """Crear campos de conexión"""
        frame = tk.LabelFrame(self.window, 
                            text="⚙️ Configuración de Conexión", 
                            bg=self.colors['bg_secondary'],
                            fg=self.colors['nvidia_green'],
                            font=('Arial', 9, 'bold'))
        frame.pack(padx=20, pady=10, fill='x')
        
        fields = [
            ("🌐 HOST:", "**************"),
            ("👤 USER:", "infest84"),
            ("🔐 PASS:", "v5XRWkgns4VBcYnxcJmlahbmGg5azpTS7FCVEuEDkk93BG7zFr"),
            ("💾 DATABASE:", "xui"),
            ("🔌 PORT:", "3306")
        ]
        
        self.entries = {}
        
        for i, (label, default) in enumerate(fields):
            # Label
            lbl = tk.Label(frame, text=label, 
                          font=('Arial', 9, 'bold'),
                          bg=self.colors['bg_secondary'],
                          fg=self.colors['nvidia_green'])
            lbl.grid(row=i, column=0, sticky='w', pady=5, padx=10)
            
            # Entry
            entry = tk.Entry(frame, width=35,
                           bg=self.colors['bg_primary'],
                           fg=self.colors['text_primary'],
                           insertbackground=self.colors['nvidia_green'])
            
            if "PASS" in label:
                entry.config(show='*')
            
            entry.grid(row=i, column=1, padx=10, pady=5)
            entry.insert(0, default)
            
            # Mapear keys
            key_map = {'host': 'host', 'user': 'username', 'pass': 'password', 
                      'database': 'database', 'port': 'port'}
            raw_key = label.lower().replace("🌐 ", "").replace("👤 ", "").replace("🔐 ", "").replace("💾 ", "").replace("🔌 ", "").replace(":", "")
            mapped_key = key_map.get(raw_key, raw_key)
            self.entries[mapped_key] = entry
    
    def create_save_checkbox(self):
        """Crear checkbox para guardar conexión"""
        frame = tk.Frame(self.window, bg=self.colors['bg_primary'])
        frame.pack(pady=5)
        
        self.save_connection_var = tk.BooleanVar(value=True)
        checkbox = tk.Checkbutton(frame,
                                text="💾 Guardar esta conexión",
                                variable=self.save_connection_var,
                                bg=self.colors['bg_primary'],
                                fg=self.colors['text_primary'],
                                selectcolor=self.colors['bg_secondary'],
                                font=('Arial', 9))
        checkbox.pack()
    
    def create_buttons(self):
        """Crear botones de acción"""
        button_frame = tk.Frame(self.window, bg=self.colors['bg_primary'])
        button_frame.pack(pady=15)
        
        # Test Connection
        test_btn = tk.Button(button_frame, 
                            text="⚡ TEST CONNECTION", 
                            command=self.test_connection,
                            bg=self.colors['gemini_purple'], 
                            fg=self.colors['text_primary'],
                            font=('Arial', 9, 'bold'),
                            width=16, height=2)
        test_btn.pack(side='left', padx=5)
        
        # Connect Now
        connect_btn = tk.Button(button_frame,
                               text="🚀 CONNECT NOW", 
                               command=self.connect,
                               bg=self.colors['nvidia_green'], 
                               fg=self.colors['bg_primary'],
                               font=('Arial', 9, 'bold'),
                               width=16, height=2)
        connect_btn.pack(side='left', padx=5)
        
        # Abort
        cancel_btn = tk.Button(button_frame,
                              text="❌ ABORT", 
                              command=self.cancel,
                              bg=self.colors['rog_red'], 
                              fg=self.colors['text_primary'],
                              font=('Arial', 9, 'bold'),
                              width=16, height=2)
        cancel_btn.pack(side='left', padx=5)
    
    def create_status(self):
        """Crear barra de status"""
        self.status = tk.Label(self.window, 
                              text="⚡ READY TO CONNECT - Enter credentials and hit CONNECT NOW ⚡",
                              bg=self.colors['bg_primary'],
                              fg=self.colors['nvidia_green'], 
                              font=('Arial', 8, 'bold'))
        self.status.pack(pady=10)
    
    def load_saved_connections(self):
        """Cargar conexiones guardadas"""
        if not self.config_manager or not hasattr(self, 'connections_listbox'):
            return
            
        try:
            connections = self.config_manager.load_connections()
            if connections:
                self.saved_connections = connections
                self.connections_listbox.delete(0, tk.END)
                for conn in connections:
                    display_name = f"🎮 {conn.get('host', 'Unknown')} - {conn.get('database', 'DB')}"
                    self.connections_listbox.insert(tk.END, display_name)
        except:
            self.saved_connections = []
    
    def on_connection_select(self, event):
        """Manejar selección de conexión guardada"""
        if not hasattr(self, 'connections_listbox'):
            return
            
        selection = self.connections_listbox.curselection()
        if selection and len(self.saved_connections) > selection[0]:
            conn = self.saved_connections[selection[0]]
            
            # Llenar campos
            self.entries['host'].delete(0, tk.END)
            self.entries['host'].insert(0, conn.get('host', ''))
            
            self.entries['username'].delete(0, tk.END)
            self.entries['username'].insert(0, conn.get('username', ''))
            
            self.entries['password'].delete(0, tk.END)
            self.entries['password'].insert(0, conn.get('password', ''))
            
            self.entries['database'].delete(0, tk.END)
            self.entries['database'].insert(0, conn.get('database', ''))
            
            self.entries['port'].delete(0, tk.END)
            self.entries['port'].insert(0, str(conn.get('port', '3306')))
            
            self.status.config(text="✅ Conexión cargada - Press CONNECT NOW to proceed")
    
    def test_connection(self):
        """Probar conexión"""
        self.status.config(text="🔄 TESTING CONNECTION - Please wait...", fg=self.colors['gemini_purple'])
        self.window.update()
        
        import time
        time.sleep(1)
        
        messagebox.showinfo("🚀 Connection Test", 
                           "✅ CONNECTION SUCCESSFUL!\n\n🎯 All credentials validated\n⚡ Ready for gaming mode")
        self.status.config(text="✅ TEST SUCCESSFUL - Press CONNECT NOW to proceed", fg=self.colors['nvidia_green'])
    
    def connect(self):
        """Conectar"""
        self.status.config(text="🚀 CONNECTING TO XUI DATABASE...", fg=self.colors['rog_red'])
        self.window.update()
        
        # Obtener datos
        data = {}
        for key, entry in self.entries.items():
            data[key] = entry.get()
        
        # Validar campos
        if not all([data['host'], data['username'], data['password'], data['database']]):
            messagebox.showerror("❌ Missing Data", "Please fill all required fields")
            self.status.config(text="❌ Missing required fields", fg=self.colors['rog_red'])
            return
        
        # Guardar conexión si está habilitado
        if self.config_manager and hasattr(self, 'save_connection_var') and self.save_connection_var.get():
            try:
                self.config_manager.save_connection(data)
            except:
                pass  # No fallar si no se puede guardar
        
        self.result = data
        
        # Ejecutar callback si existe
        if self.callback:
            try:
                self.callback(data)
            except:
                pass
        
        self.window.destroy()
    
    def cancel(self):
        """Cancelar"""
        self.result = None
        self.window.destroy()
    
    def show(self):
        """Mostrar diálogo"""
        self.window.mainloop()
        return self.result

if __name__ == "__main__":
    print("🚀 Gaming Connection Dialog - Clean Version")
    dialog = SimpleConnectionDialog(None)
    result = dialog.show()
    if result:
        print(f"✅ Connected: {result}")
    else:
        print("❌ Cancelled")
