#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Smart TMDB XUI v2 - Nueva Generación
Aplicación mejorada con todas las funcionalidades optimizadas
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import tkinter.scrolledtext as scrolledtext
from datetime import datetime
import threading
import json
import os
from typing import Dict, List, Optional
import webbrowser

# Importar nuestros módulos
try:
    from database_manager import SmartDatabaseManager
    from tmdb_manager import SmartTMDBManager
    from config_manager import ConfigManager
    from connection_dialog import SmartConnectionDialog
except ImportError as e:
    print(f"⚠️ Error importando módulos: {e}")
    print("💡 Ejecutando sin módulos avanzados...")
    SmartDatabaseManager = None
    SmartTMDBManager = None
    ConfigManager = None
    SmartConnectionDialog = None

class SmartTMDBApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 Smart TMDB XUI v2 - Nueva Generación")
        self.root.geometry("1400x900")
        self.root.resizable(True, True)
        
        # Paleta Gaming Oscura: Mayoría Negro con acentos sutiles
        self.colors = {
            'bg': '#0A0A0A',           # Negro más profundo
            'surface': '#161616',      # Superficie muy oscura
            'surface_hover': '#202020', # Superficie hover sutil
            'fg': '#B0B0B0',           # Texto gris claro (no blanco)
            'fg_secondary': '#808080', # Texto secundario gris medio
            'accent': '#76B900',       # Verde NVIDIA solo para acentos importantes
            'success': '#5C9600',      # Verde más oscuro para éxito
            'warning': '#CC0033',      # Rojo más oscuro
            'nvidia_green': '#76B900', # Verde NVIDIA
            'rog_red': '#CC0033',      # Rojo ROG más sutil
            'gemini_purple': '#7B1FA2',# Púrpura más oscuro
            'gemini_deep': '#4A148C',  # Púrpura muy profundo
            'border': '#333333',       # Bordes grises oscuros
            'selection': '#76B90025',  # Selección muy translúcida
            'card_bg': '#121212',      # Fondo tarjetas casi negro
            'button_hover': '#5C9600', # Verde hover más sutil
            'text_muted': '#606060'    # Texto muy sutil
        }
        
        # Variables de estado
        self.config_manager = ConfigManager() if ConfigManager else None
        self.db_manager = SmartDatabaseManager() if SmartDatabaseManager else None
        self.tmdb_manager = None
        self.is_connected = False
        
        # Configurar la interfaz
        self.setup_styles()
        self.create_main_interface()
        
        # Configurar eventos
        self.setup_events()
        
        print("🚀 Smart TMDB XUI v2 iniciado")
    
    def setup_styles(self):
        """Configurar estilos Gaming Épicos: NVIDIA + ROG + Gemini"""
        self.root.configure(bg=self.colors['bg'])
        
        # Configurar estilo ttk
        style = ttk.Style()
        style.theme_use('clam')
        
        # ===== FRAMES GAMING =====
        style.configure('Gaming.TFrame', 
                       background=self.colors['bg'],
                       relief='flat')
        
        style.configure('Surface.TFrame',
                       background=self.colors['surface'],
                       relief='solid',
                       borderwidth=2,
                       bordercolor=self.colors['border'])
        
        style.configure('Card.TFrame',
                       background=self.colors['card_bg'],
                       relief='solid',
                       borderwidth=1,
                       bordercolor=self.colors['nvidia_green'])
        
        # ===== LABELS GAMING OSCUROS =====
        style.configure('Gaming.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['fg_secondary'],  # Gris medio, no blanco
                       font=('Segoe UI', 10))
        
        style.configure('Title.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['nvidia_green'],  # Solo títulos en verde
                       font=('Segoe UI', 16, 'bold'))
        
        style.configure('Subtitle.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['fg'],            # Gris claro para subtítulos
                       font=('Segoe UI', 11))
        
        style.configure('Success.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['success'],       # Verde más oscuro
                       font=('Segoe UI', 10, 'bold'))
        
        style.configure('Warning.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['warning'],       # Rojo más sutil
                       font=('Segoe UI', 10, 'bold'))
        
        style.configure('Muted.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['text_muted'],    # Texto muy sutil
                       font=('Segoe UI', 9))
        
        style.configure('Purple.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['gemini_purple'],
                       font=('Segoe UI', 11, 'bold'))
        
        # ===== BOTONES GAMING SUTILES =====
        style.configure('Gaming.TButton',
                       background=self.colors['surface'],
                       foreground=self.colors['fg'],            # Gris claro, no blanco
                       borderwidth=1,                           # Bordes más sutiles
                       relief='solid',
                       bordercolor=self.colors['border'],       # Bordes grises
                       font=('Segoe UI', 9),                    # No bold por defecto
                       padding=8)
        
        style.map('Gaming.TButton',
                 background=[('active', self.colors['surface_hover']),
                           ('pressed', self.colors['success'])],
                 foreground=[('active', self.colors['fg'])],
                 bordercolor=[('active', self.colors['nvidia_green'])])  # Verde solo en hover
        
        # Botón importante (verde sutil)
        style.configure('Important.TButton',
                       background=self.colors['success'],
                       foreground=self.colors['bg'],            # Negro sobre verde
                       borderwidth=1,
                       relief='solid',
                       bordercolor=self.colors['success'],
                       font=('Segoe UI', 9, 'bold'),
                       padding=8)
        
        # Botón ROG (rojo sutil)
        style.configure('ROG.TButton',
                       background=self.colors['surface'],
                       foreground=self.colors['warning'],       # Texto rojo, fondo oscuro
                       borderwidth=1,
                       relief='solid',
                       bordercolor=self.colors['warning'],
                       font=('Segoe UI', 9, 'bold'),
                       padding=8)
        
        # Botón Gemini (púrpura sutil)
        style.configure('Gemini.TButton',
                       background=self.colors['surface'],
                       foreground=self.colors['gemini_purple'], # Texto púrpura, fondo oscuro
                       borderwidth=1,
                       relief='solid',
                       bordercolor=self.colors['gemini_purple'],
                       font=('Segoe UI', 9, 'bold'),
                       padding=8)
        
        # ===== NOTEBOOK GAMING ÉPICO =====
        style.configure('Gaming.TNotebook',
                       background=self.colors['bg'],
                       borderwidth=0)
        
        style.configure('Gaming.TNotebook.Tab',
                       background=self.colors['surface'],
                       foreground=self.colors['fg'],
                       borderwidth=2,
                       bordercolor=self.colors['border'],
                       padding=[25, 12],                        # Pestañas más grandes
                       font=('Segoe UI', 11, 'bold'))           # Fuente más grande
        
        style.map('Gaming.TNotebook.Tab',
                 background=[('selected', self.colors['nvidia_green']),
                           ('active', self.colors['surface_hover'])],
                 foreground=[('selected', self.colors['bg']),     # Negro sobre verde
                           ('active', self.colors['nvidia_green'])], # Verde en hover
                 bordercolor=[('selected', self.colors['nvidia_green']),
                            ('active', self.colors['nvidia_green'])])
        
        # Estilo especial para pestañas importantes
        style.configure('Important.TNotebook.Tab',
                       background=self.colors['gemini_purple'],
                       foreground=self.colors['fg'],
                       borderwidth=2,
                       bordercolor=self.colors['gemini_deep'],
                       padding=[25, 12],
                       font=('Segoe UI', 11, 'bold'))
        
        style.configure('Accent.TButton',
                       background=self.colors['accent'],
                       foreground='white',
                       font=('Segoe UI', 10, 'bold'))
        
        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground='white',
                       font=('Segoe UI', 10, 'bold'))
        
        style.configure('Gaming.Treeview',
                       background=self.colors['surface'],
                       foreground=self.colors['fg'],
                       fieldbackground=self.colors['surface'],
                       borderwidth=1,
                       relief='solid')
        
        style.configure('Gaming.Treeview.Heading',
                       background=self.colors['bg'],
                       foreground=self.colors['accent'],
                       font=('Segoe UI', 10, 'bold'))
    
    def create_main_interface(self):
        """Crear la interfaz principal"""
        
        # Frame principal
        self.main_frame = ttk.Frame(self.root, style='Gaming.TFrame')
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Header
        self.create_header()
        
        # Contenedor principal con pestañas
        self.create_notebook()
        
        # Status bar
        self.create_status_bar()
        
        # Logs iniciales - después de crear todos los componentes
        self.log("🚀 Smart TMDB XUI v2 iniciado")
        self.log("💡 Conecta a la base de datos para comenzar")
    
    def create_header(self):
        """Crear el header con información y controles principales"""
        header_frame = ttk.Frame(self.main_frame, style='Surface.TFrame')
        header_frame.pack(fill='x', pady=(0, 10))
        
        # Título principal
        title_label = ttk.Label(header_frame, 
                               text="🚀 Smart TMDB XUI v2",
                               style='Title.TLabel')
        title_label.pack(side='left', padx=10, pady=10)
        
        # Información de versión
        version_label = ttk.Label(header_frame,
                                 text="Nueva Generación • Optimizado para Series y Películas",
                                 style='Gaming.TLabel')
        version_label.pack(side='left', padx=10, pady=10)
        
        # Controles de conexión
        connection_frame = ttk.Frame(header_frame, style='Gaming.TFrame')
        connection_frame.pack(side='right', padx=10, pady=10)
        
        self.connect_btn = ttk.Button(connection_frame,
                                     text="🔌 Conectar DB",
                                     style='Accent.TButton',
                                     command=self.show_connection_dialog)
        self.connect_btn.pack(side='right', padx=5)
        
        self.status_label = ttk.Label(connection_frame,
                                     text="❌ Desconectado",
                                     style='Warning.TLabel')
        self.status_label.pack(side='right', padx=10)
    
    def create_notebook(self):
        """Crear el notebook con pestañas gaming ultra modernas"""
        # Contenedor principal para pestañas custom
        tabs_container = tk.Frame(self.main_frame, bg=self.colors['bg'])
        tabs_container.pack(fill='x', pady=(0, 5))
        
        # Frame para botones de pestañas
        self.tabs_frame = tk.Frame(tabs_container, bg=self.colors['bg'])
        self.tabs_frame.pack(fill='x', padx=10)
        
        # Frame para contenido
        self.content_frame = tk.Frame(self.main_frame, bg=self.colors['bg'])
        self.content_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # Crear pestañas custom
        self.tabs_data = [
            {'name': 'dashboard', 'text': '🚀 DASHBOARD', 'color': self.colors['nvidia_green']},
            {'name': 'series', 'text': '📺 SERIES TV', 'color': self.colors['gemini_purple']},
            {'name': 'movies', 'text': '🎬 PELÍCULAS', 'color': self.colors['rog_red']},
            {'name': 'maintenance', 'text': '⚙️ MAINTENANCE', 'color': self.colors['nvidia_green']},
            {'name': 'tools', 'text': '🛠️ TOOLS', 'color': self.colors['gemini_deep']}
        ]
        
        self.tab_buttons = {}
        self.tab_frames = {}
        self.current_tab = 'dashboard'
        
        # Crear botones de pestañas épicas
        for i, tab in enumerate(self.tabs_data):
            self.create_epic_tab_button(tab, i)
        
        # Crear frames de contenido
        for tab in self.tabs_data:
            frame = tk.Frame(self.content_frame, bg=self.colors['bg'])
            self.tab_frames[tab['name']] = frame
            
        # Mostrar pestaña inicial
        self.show_tab('dashboard')
    
    def create_epic_tab_button(self, tab_data, index):
        """Crear botón de pestaña con efectos épicos"""
        name = tab_data['name']
        text = tab_data['text']
        color = tab_data['color']
        
        # Crear botón custom con efectos
        btn_frame = tk.Frame(self.tabs_frame, bg=self.colors['bg'])
        btn_frame.pack(side='left', padx=2)
        
        # Botón principal
        btn = tk.Button(btn_frame, 
                       text=text,
                       font=('Segoe UI', 10, 'bold'),
                       bg=self.colors['surface'],
                       fg=self.colors['fg'],
                       activebackground=color,
                       activeforeground=self.colors['bg'],
                       relief='flat',
                       bd=0,
                       padx=20, pady=8,
                       cursor='hand2',
                       command=lambda: self.show_tab(name))
        
        btn.pack()
        self.tab_buttons[name] = btn
        
        # Efectos hover
        def on_enter(e):
            if name != self.current_tab:
                btn.config(bg=self.colors['surface_hover'], 
                          fg=color,
                          relief='solid',
                          bd=1)
        
        def on_leave(e):
            if name != self.current_tab:
                btn.config(bg=self.colors['surface'], 
                          fg=self.colors['fg'],
                          relief='flat',
                          bd=0)
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
    
    def show_tab(self, tab_name):
        """Mostrar pestaña seleccionada con efectos"""
        # Ocultar todas las pestañas
        for name, frame in self.tab_frames.items():
            frame.pack_forget()
        
        # Resetear estilos de todos los botones
        for name, btn in self.tab_buttons.items():
            if name == tab_name:
                # Botón activo - estilo épico
                color = next(t['color'] for t in self.tabs_data if t['name'] == name)
                btn.config(bg=color,
                          fg=self.colors['bg'],
                          relief='solid',
                          bd=2)
            else:
                # Botones inactivos
                btn.config(bg=self.colors['surface'],
                          fg=self.colors['fg'],
                          relief='flat',
                          bd=0)
        
        # Mostrar pestaña seleccionada
        if tab_name in self.tab_frames:
            self.tab_frames[tab_name].pack(fill='both', expand=True)
            self.current_tab = tab_name
            
            # Crear contenido si no existe
            if not self.tab_frames[tab_name].winfo_children():
                self.create_tab_content(tab_name)
    
    def create_tab_content(self, tab_name):
        """Crear contenido de la pestaña"""
        frame = self.tab_frames[tab_name]
        
        if tab_name == 'dashboard':
            self.create_dashboard_content(frame)
        elif tab_name == 'series':
            self.create_series_content(frame)
        elif tab_name == 'movies':
            self.create_movies_content(frame)
        elif tab_name == 'maintenance':
            self.create_maintenance_content(frame)
        elif tab_name == 'tools':
            self.create_tools_content(frame)
    
    def create_dashboard_content(self, parent_frame):
        """Crear contenido del dashboard épico"""
        # Título épico
        title = tk.Label(parent_frame, 
                        text="🚀 SMART TMDB DASHBOARD",
                        font=('Segoe UI', 18, 'bold'),
                        fg=self.colors['nvidia_green'],
                        bg=self.colors['bg'])
        title.pack(pady=20)
        
        # Grid para métricas épicas
        metrics_grid = tk.Frame(parent_frame, bg=self.colors['bg'])
        metrics_grid.pack(fill='x', padx=20, pady=10)
        
        metrics = [
            ("🎬 PELÍCULAS", "28,509", self.colors['nvidia_green']),
            ("📺 EPISODIOS", "14,702", self.colors['gemini_purple']),
            ("⚡ OPTIMIZADAS", "100%", self.colors['rog_red']),
            ("🔥 ESTADO", "GAMING", self.colors['nvidia_green'])
        ]
        
        for i, (label, value, color) in enumerate(metrics):
            card = tk.Frame(metrics_grid, bg=self.colors['surface'], relief='solid', bd=2)
            card.grid(row=0, column=i, padx=10, pady=5, sticky='ew')
            metrics_grid.columnconfigure(i, weight=1)
            
            tk.Label(card, text=label, font=('Segoe UI', 10, 'bold'),
                    fg=self.colors['fg'], bg=self.colors['surface']).pack(pady=5)
            tk.Label(card, text=value, font=('Segoe UI', 14, 'bold'),
                    fg=color, bg=self.colors['surface']).pack()
            tk.Label(card, text="●●●", font=('Segoe UI', 8),
                    fg=color, bg=self.colors['surface']).pack(pady=5)
    
    def create_series_content(self, parent_frame):
        """Crear contenido de series épico"""
        title = tk.Label(parent_frame, 
                        text="📺 SERIES TV MANAGEMENT",
                        font=('Segoe UI', 18, 'bold'),
                        fg=self.colors['gemini_purple'],
                        bg=self.colors['bg'])
        title.pack(pady=20)
        
        tk.Label(parent_frame, 
                text="Gestión completa de series de TV\nOptimización TMDB avanzada",
                font=('Segoe UI', 12),
                fg=self.colors['fg'],
                bg=self.colors['bg']).pack(pady=10)
    
    def create_movies_content(self, parent_frame):
        """Crear contenido de películas épico"""
        title = tk.Label(parent_frame, 
                        text="🎬 MOVIES CONTROL CENTER",
                        font=('Segoe UI', 18, 'bold'),
                        fg=self.colors['rog_red'],
                        bg=self.colors['bg'])
        title.pack(pady=20)
        
        tk.Label(parent_frame, 
                text="Control total de películas\nBase de datos optimizada",
                font=('Segoe UI', 12),
                fg=self.colors['fg'],
                bg=self.colors['bg']).pack(pady=10)
    
    def create_maintenance_content(self, parent_frame):
        """Crear contenido de mantenimiento épico"""
        title = tk.Label(parent_frame, 
                        text="⚙️ MAINTENANCE STATION",
                        font=('Segoe UI', 18, 'bold'),
                        fg=self.colors['nvidia_green'],
                        bg=self.colors['bg'])
        title.pack(pady=20)
        
        tk.Label(parent_frame, 
                text="Herramientas de mantenimiento avanzadas\nOptimización y correcciones",
                font=('Segoe UI', 12),
                fg=self.colors['fg'],
                bg=self.colors['bg']).pack(pady=10)
    
    def create_tools_content(self, parent_frame):
        """Crear contenido de herramientas épico"""
        title = tk.Label(parent_frame, 
                        text="🛠️ ADVANCED TOOLS",
                        font=('Segoe UI', 18, 'bold'),
                        fg=self.colors['gemini_deep'],
                        bg=self.colors['bg'])
        title.pack(pady=20)
        
        tk.Label(parent_frame, 
                text="Herramientas avanzadas de desarrollo\nImportación y análisis",
                font=('Segoe UI', 12),
                fg=self.colors['fg'],
                bg=self.colors['bg']).pack(pady=10)
        
        # Las pestañas ahora se crean dinámicamente con el sistema custom
    
    def create_dashboard_tab(self):
        """Crear la pestaña de dashboard"""
        dashboard_frame = ttk.Frame(self.notebook, style='Gaming.TFrame')
        self.notebook.add(dashboard_frame, text='� DASHBOARD')
        
        # Métricas principales
        metrics_frame = ttk.LabelFrame(dashboard_frame, text="📈 Métricas Principales", style='Gaming.TFrame')
        metrics_frame.pack(fill='x', padx=10, pady=10)
        
        # Grid para métricas
        metrics_grid = ttk.Frame(metrics_frame, style='Gaming.TFrame')
        metrics_grid.pack(fill='x', padx=10, pady=10)
        
        # Métricas individuales
        self.create_metric_card(metrics_grid, "🎬 Películas", "0", "Total en base de datos", 0, 0)
        self.create_metric_card(metrics_grid, "📺 Series", "0", "Series disponibles", 0, 1)
        self.create_metric_card(metrics_grid, "🎭 Episodios", "0", "Episodios totales", 0, 2)
        self.create_metric_card(metrics_grid, "🖼️ Con Íconos", "0%", "Contenido con carátulas", 1, 0)
        self.create_metric_card(metrics_grid, "✅ Compatibles", "0%", "Compatible con apps", 1, 1)
        self.create_metric_card(metrics_grid, "⚡ Estado DB", "Desconectado", "Estado conexión", 1, 2)
        
        # Acciones rápidas
        actions_frame = ttk.LabelFrame(dashboard_frame, text="⚡ Acciones Rápidas", style='Gaming.TFrame')
        actions_frame.pack(fill='x', padx=10, pady=10)
        
        actions_grid = ttk.Frame(actions_frame, style='Gaming.TFrame')
        actions_grid.pack(fill='x', padx=10, pady=10)
        
        # Botones de acciones
        ttk.Button(actions_grid, text="🔄 Actualizar Métricas", 
                  style='Gaming.TButton', command=self.update_metrics).grid(row=0, column=0, padx=5, pady=5)
        
        ttk.Button(actions_grid, text="🎯 Asignar Íconos", 
                  style='Gaming.TButton', command=self.auto_assign_icons).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Button(actions_grid, text="📅 Corregir Fechas", 
                  style='Gaming.TButton', command=self.fix_timestamps).grid(row=0, column=2, padx=5, pady=5)
        
        ttk.Button(actions_grid, text="🔧 Optimizar DB", 
                  style='Gaming.TButton', command=self.optimize_database).grid(row=1, column=0, padx=5, pady=5)
        
        ttk.Button(actions_grid, text="📊 Generar Reporte", 
                  style='Gaming.TButton', command=self.generate_report).grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Button(actions_grid, text="💾 Backup DB", 
                  style='Gaming.TButton', command=self.backup_database).grid(row=1, column=2, padx=5, pady=5)
        
        # Log de actividades
        log_frame = ttk.LabelFrame(dashboard_frame, text="📝 Log de Actividades", style='Gaming.TFrame')
        log_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, 
                                                 height=10,
                                                 bg=self.colors['surface'],
                                                 fg=self.colors['fg'],
                                                 insertbackground=self.colors['accent'],
                                                 font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_metric_card(self, parent, title, value, description, row, col):
        """Crear una tarjeta de métrica"""
        card_frame = ttk.Frame(parent, style='Surface.TFrame')
        card_frame.grid(row=row, column=col, padx=10, pady=5, sticky='ew')
        
        parent.grid_columnconfigure(col, weight=1)
        
        # Título
        title_label = ttk.Label(card_frame, text=title, style='Gaming.TLabel', font=('Segoe UI', 11, 'bold'))
        title_label.pack(pady=5)
        
        # Valor
        value_label = ttk.Label(card_frame, text=value, style='Title.TLabel', font=('Segoe UI', 16, 'bold'))
        value_label.pack()
        
        # Descripción
        desc_label = ttk.Label(card_frame, text=description, style='Gaming.TLabel', font=('Segoe UI', 8))
        desc_label.pack(pady=5)
        
        # Guardar referencias para actualizar
        if not hasattr(self, 'metric_labels'):
            self.metric_labels = {}
        self.metric_labels[title] = value_label
    
    def create_series_tab(self):
        """Crear la pestaña de series"""
        series_frame = ttk.Frame(self.notebook, style='Gaming.TFrame')
        self.notebook.add(series_frame, text='📺 SERIES TV')
        
        # Controles superiores
        controls_frame = ttk.Frame(series_frame, style='Gaming.TFrame')
        controls_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Label(controls_frame, text="📺 Gestión de Series y Episodios", style='Title.TLabel').pack(side='left')
        
        ttk.Button(controls_frame, text="🔄 Actualizar", 
                  style='Gaming.TButton', command=self.refresh_series).pack(side='right', padx=5)
        
        ttk.Button(controls_frame, text="🎯 Asignar TMDB", 
                  style='Accent.TButton', command=self.assign_tmdb_series).pack(side='right', padx=5)
        
        # Lista de series
        series_list_frame = ttk.LabelFrame(series_frame, text="📝 Series en Base de Datos", style='Gaming.TFrame')
        series_list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Treeview para series
        columns = ('ID', 'Título', 'Episodios', 'TMDB ID', 'Estado', 'Última Actualización')
        self.series_tree = ttk.Treeview(series_list_frame, columns=columns, show='headings', style='Gaming.Treeview')
        
        for col in columns:
            self.series_tree.heading(col, text=col)
            self.series_tree.column(col, width=120)
        
        # Scrollbar
        series_scroll = ttk.Scrollbar(series_list_frame, orient='vertical', command=self.series_tree.yview)
        self.series_tree.configure(yscrollcommand=series_scroll.set)
        
        self.series_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        series_scroll.pack(side='right', fill='y', pady=10)
    
    def create_movies_tab(self):
        """Crear la pestaña de películas"""
        movies_frame = ttk.Frame(self.notebook, style='Gaming.TFrame')
        self.notebook.add(movies_frame, text='🎬 PELÍCULAS')
        
        # Controles superiores
        controls_frame = ttk.Frame(movies_frame, style='Gaming.TFrame')
        controls_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Label(controls_frame, text="🎬 Gestión de Películas", style='Title.TLabel').pack(side='left')
        
        ttk.Button(controls_frame, text="🔄 Actualizar", 
                  style='Gaming.TButton', command=self.refresh_movies).pack(side='right', padx=5)
        
        ttk.Button(controls_frame, text="🎯 Asignar TMDB", 
                  style='Accent.TButton', command=self.assign_tmdb_movies).pack(side='right', padx=5)
        
        # Lista de películas
        movies_list_frame = ttk.LabelFrame(movies_frame, text="📝 Películas en Base de Datos", style='Gaming.TFrame')
        movies_list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Treeview para películas
        columns = ('ID', 'Título', 'Año', 'TMDB ID', 'Ícono', 'Compatible', 'Fecha')
        self.movies_tree = ttk.Treeview(movies_list_frame, columns=columns, show='headings', style='Gaming.Treeview')
        
        for col in columns:
            self.movies_tree.heading(col, text=col)
            self.movies_tree.column(col, width=100)
        
        # Scrollbar
        movies_scroll = ttk.Scrollbar(movies_list_frame, orient='vertical', command=self.movies_tree.yview)
        self.movies_tree.configure(yscrollcommand=movies_scroll.set)
        
        self.movies_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        movies_scroll.pack(side='right', fill='y', pady=10)
    
    def create_maintenance_tab(self):
        """Crear la pestaña de mantenimiento"""
        maintenance_frame = ttk.Frame(self.notebook, style='Gaming.TFrame')
        self.notebook.add(maintenance_frame, text='⚙️ MAINTENANCE')
        
        ttk.Label(maintenance_frame, text="🔧 Herramientas de Mantenimiento", style='Title.TLabel').pack(pady=10)
        
        # Sección de correcciones
        corrections_frame = ttk.LabelFrame(maintenance_frame, text="🔨 Correcciones Automáticas", style='Gaming.TFrame')
        corrections_frame.pack(fill='x', padx=10, pady=10)
        
        corrections_grid = ttk.Frame(corrections_frame, style='Gaming.TFrame')
        corrections_grid.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(corrections_grid, text="📅 Corregir Timestamps", 
                  style='Success.TButton', command=self.fix_all_timestamps).grid(row=0, column=0, padx=5, pady=5)
        
        ttk.Button(corrections_grid, text="📦 Corregir Containers", 
                  style='Success.TButton', command=self.fix_containers).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Button(corrections_grid, text="🔢 Corregir Order Values", 
                  style='Success.TButton', command=self.fix_order_values).grid(row=0, column=2, padx=5, pady=5)
        
        ttk.Button(corrections_grid, text="🎭 Corregir JSON Format", 
                  style='Success.TButton', command=self.fix_json_format).grid(row=1, column=0, padx=5, pady=5)
        
        ttk.Button(corrections_grid, text="🖼️ Asignar Íconos Masivo", 
                  style='Success.TButton', command=self.mass_assign_icons).grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Button(corrections_grid, text="🧹 Limpieza Completa", 
                  style='Success.TButton', command=self.full_cleanup).grid(row=1, column=2, padx=5, pady=5)
        
        # Sección de análisis
        analysis_frame = ttk.LabelFrame(maintenance_frame, text="📊 Análisis y Diagnóstico", style='Gaming.TFrame')
        analysis_frame.pack(fill='x', padx=10, pady=10)
        
        analysis_grid = ttk.Frame(analysis_frame, style='Gaming.TFrame')
        analysis_grid.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(analysis_grid, text="🔍 Analizar Compatibilidad", 
                  style='Gaming.TButton', command=self.analyze_compatibility).grid(row=0, column=0, padx=5, pady=5)
        
        ttk.Button(analysis_grid, text="🏥 Diagnóstico Dr. House", 
                  style='Gaming.TButton', command=self.diagnose_dr_house).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Button(analysis_grid, text="📈 Reporte Completo", 
                  style='Gaming.TButton', command=self.full_report).grid(row=0, column=2, padx=5, pady=5)
    
    def create_tools_tab(self):
        """Crear la pestaña de herramientas"""
        tools_frame = ttk.Frame(self.notebook, style='Gaming.TFrame')
        self.notebook.add(tools_frame, text='🛠️ TOOLS')
        
        ttk.Label(tools_frame, text="🛠️ Herramientas Avanzadas", style='Title.TLabel').pack(pady=10)
        
        # Herramientas de importación
        import_frame = ttk.LabelFrame(tools_frame, text="📥 Importación y Exportación", style='Gaming.TFrame')
        import_frame.pack(fill='x', padx=10, pady=10)
        
        import_grid = ttk.Frame(import_frame, style='Gaming.TFrame')
        import_grid.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(import_grid, text="📄 Importar M3U", 
                  style='Gaming.TButton', command=self.import_m3u).grid(row=0, column=0, padx=5, pady=5)
        
        ttk.Button(import_grid, text="💾 Exportar Base", 
                  style='Gaming.TButton', command=self.export_database).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Button(import_grid, text="🔄 Sincronizar TMDB", 
                  style='Accent.TButton', command=self.sync_tmdb).grid(row=0, column=2, padx=5, pady=5)
        
        # Herramientas de desarrollo
        dev_frame = ttk.LabelFrame(tools_frame, text="⚙️ Herramientas de Desarrollo", style='Gaming.TFrame')
        dev_frame.pack(fill='x', padx=10, pady=10)
        
        dev_grid = ttk.Frame(dev_frame, style='Gaming.TFrame')
        dev_grid.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(dev_grid, text="🐞 Debug Mode", 
                  style='Gaming.TButton', command=self.toggle_debug).grid(row=0, column=0, padx=5, pady=5)
        
        ttk.Button(dev_grid, text="📊 SQL Console", 
                  style='Gaming.TButton', command=self.open_sql_console).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Button(dev_grid, text="🔧 Configuración", 
                  style='Gaming.TButton', command=self.open_config).grid(row=0, column=2, padx=5, pady=5)
    
    def create_status_bar(self):
        """Crear la barra de estado"""
        self.status_frame = ttk.Frame(self.main_frame, style='Surface.TFrame')
        self.status_frame.pack(fill='x', pady=(0, 0))
        
        # Status izquierdo
        self.status_left = ttk.Label(self.status_frame, 
                                    text="⚡ Smart TMDB XUI v2 - Listo",
                                    style='Gaming.TLabel')
        self.status_left.pack(side='left', padx=10, pady=5)
        
        # Status derecho
        self.status_right = ttk.Label(self.status_frame,
                                     text=f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                                     style='Gaming.TLabel')
        self.status_right.pack(side='right', padx=10, pady=5)
    
    def setup_events(self):
        """Configurar eventos de la aplicación"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Actualizar reloj cada minuto
        self.update_clock()
    
    def log(self, message: str, level: str = "INFO"):
        """Agregar mensaje al log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # Íconos por nivel
        icons = {
            "INFO": "ℹ️",
            "SUCCESS": "✅", 
            "WARNING": "⚠️",
            "ERROR": "❌",
            "DEBUG": "🐞"
        }
        
        icon = icons.get(level, "ℹ️")
        log_message = f"[{timestamp}] {icon} {message}\\n"
        
        if hasattr(self, 'log_text'):
            self.log_text.insert('end', log_message)
            self.log_text.see('end')
        
        print(f"{log_message.strip()}")
        
        # Actualizar status bar si existe
        if hasattr(self, 'status_left'):
            self.status_left.config(text=f"⚡ {message}")
    
    def update_clock(self):
        """Actualizar el reloj en la status bar"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M')
        self.status_right.config(text=f"📅 {current_time}")
        
        # Programar próxima actualización en 60 segundos
        self.root.after(60000, self.update_clock)
    
    # Métodos de funcionalidad (placeholders por ahora)
    
    def show_connection_dialog(self):
        """Mostrar diálogo de conexión a base de datos"""
        self.log("🔌 Abriendo diálogo de conexión...")
        
        dialog = SmartConnectionDialog(self.root, self.config_manager, self.on_connection_established)
        result = dialog.show()
        
        if result:
            self.log("✅ Datos de conexión obtenidos", "SUCCESS")
        else:
            self.log("❌ Conexión cancelada", "WARNING")
    
    def on_connection_established(self, connection_data: Dict):
        """Manejar conexión establecida"""
        try:
            # Intentar conectar
            success = self.db_manager.connect(
                host=connection_data['host'],
                user=connection_data['user'],
                password=connection_data['password'],
                database=connection_data['database'],
                port=connection_data['port']
            )
            
            if success:
                self.is_connected = True
                self.status_label.config(text="✅ Conectado")
                self.connect_btn.config(text="🔌 Conectado")
                self.log("🎉 Conexión exitosa a la base de datos", "SUCCESS")
                
                # Actualizar métricas automáticamente
                self.update_metrics()
                
                # Inicializar TMDB si hay API key
                tmdb_key = self.config_manager.get('tmdb.api_key')
                if tmdb_key:
                    self.tmdb_manager = SmartTMDBManager(tmdb_key)
                    self.log("🎬 TMDB Manager inicializado", "SUCCESS")
            else:
                self.log("❌ Error al conectar a la base de datos", "ERROR")
                
        except Exception as e:
            self.log(f"❌ Error de conexión: {e}", "ERROR")
    
    def update_metrics(self):
        """Actualizar métricas del dashboard"""
        if not self.is_connected or not self.db_manager:
            self.log("⚠️ No hay conexión a la base de datos", "WARNING")
            return
        
        self.log("🔄 Actualizando métricas...")
        
        try:
            # Obtener métricas de la base de datos
            metrics = self.db_manager.get_database_metrics()
            
            # Actualizar las tarjetas de métricas
            if hasattr(self, 'metric_labels'):
                # Actualizar películas
                if "🎬 Películas" in self.metric_labels:
                    self.metric_labels["🎬 Películas"].config(text=f"{metrics['movies']:,}")
                
                # Actualizar series
                if "📺 Series" in self.metric_labels:
                    self.metric_labels["📺 Series"].config(text=f"{metrics['series']:,}")
                
                # Actualizar episodios
                if "🎭 Episodios" in self.metric_labels:
                    self.metric_labels["🎭 Episodios"].config(text=f"{metrics['episodes']:,}")
                
                # Actualizar porcentaje con íconos
                total_content = metrics['movies'] + metrics['episodes']
                if total_content > 0:
                    icon_percentage = (metrics['with_icons'] / total_content) * 100
                    if "🖼️ Con Íconos" in self.metric_labels:
                        self.metric_labels["🖼️ Con Íconos"].config(text=f"{icon_percentage:.1f}%")
                
                # Actualizar compatibilidad
                if metrics['movies'] > 0:
                    compat_percentage = (metrics['compatible_movies'] / metrics['movies']) * 100
                    if "✅ Compatibles" in self.metric_labels:
                        self.metric_labels["✅ Compatibles"].config(text=f"{compat_percentage:.1f}%")
                
                # Estado de conexión
                if "⚡ Estado DB" in self.metric_labels:
                    status = "Conectado" if self.is_connected else "Desconectado"
                    self.metric_labels["⚡ Estado DB"].config(text=status)
            
            self.log(f"✅ Métricas actualizadas - {metrics['movies']:,} películas, {metrics['series']:,} series", "SUCCESS")
            
        except Exception as e:
            self.log(f"❌ Error actualizando métricas: {e}", "ERROR")
        
    def auto_assign_icons(self):
        """Asignar íconos automáticamente"""
        if not self.is_connected or not self.db_manager:
            self.log("⚠️ No hay conexión a la base de datos", "WARNING")
            return
            
        self.log("🎯 Iniciando asignación automática de íconos...")
        
        try:
            success, affected = self.db_manager.auto_assign_icons_from_json()
            
            if success:
                self.log(f"✅ Íconos asignados exitosamente: {affected:,} elementos", "SUCCESS")
                self.update_metrics()  # Actualizar métricas
            else:
                self.log("❌ Error en la asignación de íconos", "ERROR")
                
        except Exception as e:
            self.log(f"❌ Error asignando íconos: {e}", "ERROR")
    
    def fix_timestamps(self):
        """Corregir timestamps problemáticos"""
        if not self.is_connected or not self.db_manager:
            self.log("⚠️ No hay conexión a la base de datos", "WARNING")
            return
        
        self.log("📅 Corrigiendo timestamps problemáticos...")
        
        try:
            # Corregir películas
            success_movies, affected_movies = self.db_manager.fix_movie_timestamps_to_realistic()
            
            # Corregir episodios
            success_episodes, affected_episodes = self.db_manager.fix_episode_timestamps_to_realistic()
            
            if success_movies or success_episodes:
                total_fixed = affected_movies + affected_episodes
                self.log(f"✅ Timestamps corregidos: {total_fixed:,} elementos ({affected_movies:,} películas, {affected_episodes:,} episodios)", "SUCCESS")
                self.update_metrics()
            else:
                self.log("❌ Error corrigiendo timestamps", "ERROR")
                
        except Exception as e:
            self.log(f"❌ Error corrigiendo timestamps: {e}", "ERROR")
    
    def optimize_database(self):
        """Optimizar base de datos con todas las correcciones"""
        if not self.is_connected or not self.db_manager:
            self.log("⚠️ No hay conexión a la base de datos", "WARNING")
            return
        
        self.log("🔧 Iniciando optimización completa de la base de datos...")
        
        try:
            total_fixes = 0
            
            # 1. Corregir timestamps
            success, affected = self.db_manager.fix_movie_timestamps_to_realistic()
            if success:
                total_fixes += affected
                self.log(f"   ✅ Timestamps películas: {affected:,} corregidos")
            
            success, affected = self.db_manager.fix_episode_timestamps_to_realistic()
            if success:
                total_fixes += affected
                self.log(f"   ✅ Timestamps episodios: {affected:,} corregidos")
            
            # 2. Corregir containers
            success, affected = self.db_manager.fix_movie_containers()
            if success:
                total_fixes += affected
                self.log(f"   ✅ Containers películas: {affected:,} establecidos")
            
            # 3. Corregir order values
            success, affected = self.db_manager.fix_movie_order_values()
            if success:
                total_fixes += affected
                self.log(f"   ✅ Order values: {affected:,} corregidos")
            
            # 4. Asignar íconos
            success, affected = self.db_manager.auto_assign_icons_from_json()
            if success:
                total_fixes += affected
                self.log(f"   ✅ Íconos asignados: {affected:,} elementos")
            
            self.log(f"🎉 Optimización completada: {total_fixes:,} correcciones aplicadas", "SUCCESS")
            self.update_metrics()
            
        except Exception as e:
            self.log(f"❌ Error en optimización: {e}", "ERROR")
        
    def generate_report(self):
        """Generar reporte completo"""
        self.log("📊 Generando reporte...")
        
    def backup_database(self):
        """Hacer backup de la base de datos"""
        self.log("💾 Creando backup de base de datos...")
        
    def refresh_series(self):
        """Actualizar lista de series"""
        self.log("📺 Actualizando lista de series...")
        
    def assign_tmdb_series(self):
        """Asignar TMDB a series"""
        self.log("🎯 Asignando TMDB a series...")
        
    def refresh_movies(self):
        """Actualizar lista de películas"""
        self.log("🎬 Actualizando lista de películas...")
        
    def assign_tmdb_movies(self):
        """Asignar TMDB a películas"""
        self.log("🎯 Asignando TMDB a películas...")
        
    def fix_all_timestamps(self):
        """Corregir todos los timestamps"""
        self.log("📅 Corrigiendo todos los timestamps...")
        
    def fix_containers(self):
        """Corregir containers"""
        self.log("📦 Corrigiendo containers...")
        
    def fix_order_values(self):
        """Corregir valores de order"""
        self.log("🔢 Corrigiendo valores de order...")
        
    def fix_json_format(self):
        """Corregir formato JSON"""
        self.log("🎭 Corrigiendo formato JSON...")
        
    def mass_assign_icons(self):
        """Asignación masiva de íconos"""
        self.log("🖼️ Iniciando asignación masiva de íconos...")
        
    def full_cleanup(self):
        """Limpieza completa de la base de datos"""
        self.log("🧹 Iniciando limpieza completa...")
        
    def analyze_compatibility(self):
        """Analizar compatibilidad"""
        self.log("🔍 Analizando compatibilidad...")
        
    def diagnose_dr_house(self):
        """Diagnosticar problemas de Dr. House"""
        self.log("🏥 Diagnosticando problemas de Dr. House...")
        
    def full_report(self):
        """Generar reporte completo"""
        self.log("📈 Generando reporte completo...")
        
    def import_m3u(self):
        """Importar archivo M3U"""
        self.log("📄 Importando archivo M3U...")
        
    def export_database(self):
        """Exportar base de datos"""
        self.log("💾 Exportando base de datos...")
        
    def sync_tmdb(self):
        """Sincronizar con TMDB"""
        self.log("🔄 Sincronizando con TMDB...")
        
    def toggle_debug(self):
        """Activar/desactivar modo debug"""
        self.log("🐞 Alternando modo debug...")
        
    def open_sql_console(self):
        """Abrir consola SQL"""
        self.log("📊 Abriendo consola SQL...")
        
    def open_config(self):
        """Abrir configuración"""
        self.log("🔧 Abriendo configuración...")
        
    def on_closing(self):
        """Manejar cierre de la aplicación"""
        self.log("👋 Cerrando Smart TMDB XUI v2...")
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """Ejecutar la aplicación"""
        self.root.mainloop()

def main():
    """Función principal"""
    app = SmartTMDBApp()
    app.run()

if __name__ == "__main__":
    main()
