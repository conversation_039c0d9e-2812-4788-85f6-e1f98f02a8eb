#!/usr/bin/env python3
"""
Script de debug para el problema de importación M3U
"""

def analyze_m3u_import_issue():
    """Analizar el problema de importación M3U"""
    print("🔍 DEBUGGING M3U IMPORT ISSUE")
    print("=" * 50)
    
    # Simular los datos que llegan del treeview
    print("📊 Analyzing the import flow...")
    
    # El mensaje del log indica:
    # "📊 [████████████████████] 1/1 (100.0%) Importing: 11 episodes"
    # "⚠️ M3U data not found for: 11 episodes"
    
    print("\n🔍 ISSUE ANALYSIS:")
    print("1. User selected 1 item from treeview")
    print("2. That item represents '11 episodes' (probably a series group)")
    print("3. The import function is trying to import it as a single item")
    print("4. But it can't find M3U data because it's looking for individual episode data")
    
    print("\n💡 PROBABLE CAUSE:")
    print("The treeview is showing GROUPED series data, not individual episodes")
    print("When user selects a series group, we need to:")
    print("- Detect that it's a series group (not individual episode)")
    print("- Expand it to import all episodes in that group")
    print("- Or change the display to show individual episodes for selection")
    
    print("\n🔧 POSSIBLE SOLUTIONS:")
    print("1. Modify treeview to show individual episodes instead of groups")
    print("2. Detect series groups and expand them during import")
    print("3. Add special handling for grouped series in import function")
    
    print("\n📋 RECOMMENDED APPROACH:")
    print("Check how the M3U data is displayed in the treeview:")
    print("- If showing series groups: modify import to handle groups")
    print("- If showing individual episodes: fix the data mapping")
    
    return True

def suggest_fixes():
    """Sugerir correcciones específicas"""
    print("\n🛠️ SUGGESTED FIXES:")
    print("=" * 30)
    
    print("\n1. 🔍 ADD DETECTION FOR SERIES GROUPS:")
    print("""
    # In import function, detect if selected item is a series group
    if 'episodes' in search_title.lower() or search_episode == '':
        # This is a series group, need to expand it
        # Find all episodes for this series
    """)
    
    print("\n2. 🔄 MODIFY TREEVIEW DISPLAY:")
    print("""
    # Show individual episodes instead of grouped series
    # This way each selectable item corresponds to one M3U entry
    """)
    
    print("\n3. 🎯 IMPROVE DATA MAPPING:")
    print("""
    # Store mapping between treeview items and M3U data
    # Use unique identifiers to match exactly
    """)
    
    print("\n4. 📊 ADD SERIES GROUP EXPANSION:")
    print("""
    # When importing a series group:
    # 1. Find all M3U entries for that series
    # 2. Import each episode individually
    # 3. Report progress per episode, not per group
    """)

def create_test_scenario():
    """Crear escenario de prueba"""
    print("\n🧪 TEST SCENARIO:")
    print("=" * 20)
    
    # Simular datos del treeview
    treeview_data = [
        ["☑", "11 episodes", "Medium (2005)", "", "", "Netflix", "HD"]
    ]
    
    # Simular datos M3U
    m3u_data = [
        {"title": "Medium (2005) S01E01", "url": "http://example.com/1", "series": "Medium (2005)"},
        {"title": "Medium (2005) S01E02", "url": "http://example.com/2", "series": "Medium (2005)"},
        # ... más episodios
    ]
    
    print(f"📊 Treeview shows: {treeview_data[0]}")
    print(f"📁 M3U has: {len(m3u_data)} individual episodes")
    print("❌ MISMATCH: Treeview shows group, M3U has individuals")
    
    print("\n💡 SOLUTION: Need to map group to individual episodes")

def main():
    """Función principal"""
    analyze_m3u_import_issue()
    suggest_fixes()
    create_test_scenario()
    
    print("\n" + "=" * 50)
    print("🎯 NEXT STEPS:")
    print("1. Check how M3U data is displayed in treeview")
    print("2. Determine if showing groups or individual episodes")
    print("3. Implement appropriate fix based on display method")
    print("4. Test with debug logging enabled")
    print("\n🔧 The debug logging added to gui.py will help identify the exact issue!")

if __name__ == "__main__":
    main()
