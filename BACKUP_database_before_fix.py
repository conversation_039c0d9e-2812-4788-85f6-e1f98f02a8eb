import pymysql
from typing import List, Dict, Optional, Tuple
import logging

class DatabaseManager:
    def __init__(self):
        self.connection = None
        self.host = None
        self.user = None
        self.password = None
        self.database = None
        self.port = 3306
        
    def connect(self, host: str, user: str, password: str, database: str, port: int = 3306) -> bool:
        """Conectar a la base de datos MariaDB"""
        try:
            self.host = host
            self.user = user
            self.password = password
            self.database = database
            self.port = port
            
            self.connection = pymysql.connect(
                host=host,
                user=user,
                password=password,
                database=database,
                port=port,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            return True
        except Exception as e:
            logging.error(f"Error conectando a la base de datos: {e}")
            return False
    
    def disconnect(self):
        """Desconectar de la base de datos"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def is_connected(self) -> bool:
        """Verificar si está conectado"""
        try:
            if self.connection:
                self.connection.ping(reconnect=True)
                return True
        except:
            pass
        return False
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """Ejecutar una consulta SELECT"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchall()
        except Exception as e:
            logging.error(f"Error ejecutando consulta: {e}")
            return []
    
    def execute_update(self, query: str, params: tuple = None) -> bool:
        """Ejecutar una consulta INSERT/UPDATE/DELETE"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, params)
                self.connection.commit()
                return True
        except Exception as e:
            logging.error(f"Error ejecutando actualización: {e}")
            self.connection.rollback()
            return False
    
    def get_series_with_episodes(self) -> List[Dict]:
        """Obtener todas las series con sus episodios"""
        query = """
        SELECT 
            ss.id as series_id,
            ss.title as series_title,
            s.id as stream_id,
            s.type,
            st.type_name,
            COUNT(se.id) as episode_count
        FROM streams_series ss
        LEFT JOIN streams_episodes se ON ss.id = se.series_id
        LEFT JOIN streams s ON se.stream_id = s.id
        LEFT JOIN streams_types st ON s.type = st.type_id
        GROUP BY ss.id, ss.title, s.type, st.type_name
        ORDER BY ss.title
        """
        return self.execute_query(query)
    
    def get_orphaned_episodes(self) -> List[Dict]:
        """Obtener episodios huérfanos (sin serie asignada)"""
        query = """
        SELECT 
            se.id as episode_id,
            se.stream_id,
            se.series_id,
            s.id as stream_id,
            s.type,
            st.type_name
        FROM streams_episodes se
        LEFT JOIN streams_series ss ON se.series_id = ss.id
        LEFT JOIN streams s ON se.stream_id = s.id
        LEFT JOIN streams_types st ON s.type = st.type_id
        WHERE ss.id IS NULL
        ORDER BY se.id
        """
        return self.execute_query(query)
    
    def get_series_without_episodes(self) -> List[Dict]:
        """Obtener series sin episodios"""
        query = """
        SELECT 
            ss.id as series_id,
            ss.title as series_title
        FROM streams_series ss
        LEFT JOIN streams_episodes se ON ss.id = se.series_id
        WHERE se.id IS NULL
        ORDER BY ss.title
        """
        return self.execute_query(query)
    
    def get_stream_types(self) -> List[Dict]:
        """Obtener todos los tipos de stream"""
        query = "SELECT type_id, type_name FROM streams_types ORDER BY type_name"
        return self.execute_query(query)
    
    def create_series(self, title: str) -> Optional[int]:
        """Crear una nueva serie"""
        query = "INSERT INTO streams_series (title) VALUES (%s)"
        if self.execute_update(query, (title,)):
            # Obtener el ID de la serie recién creada
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT LAST_INSERT_ID()")
                result = cursor.fetchone()
                return result['LAST_INSERT_ID()'] if result else None
        return None
    
    def assign_episode_to_series(self, episode_id: int, series_id: int) -> bool:
        """Asignar un episodio a una serie"""
        query = "UPDATE streams_episodes SET series_id = %s WHERE id = %s"
        return self.execute_update(query, (series_id, episode_id))
    
    def delete_episode(self, episode_id: int) -> bool:
        """Eliminar un episodio"""
        query = "DELETE FROM streams_episodes WHERE id = %s"
        return self.execute_update(query, (episode_id,))
    
    def delete_series(self, series_id: int) -> bool:
        """Eliminar una serie"""
        query = "DELETE FROM streams_series WHERE id = %s"
        return self.execute_update(query, (series_id,))
    
    def get_duplicate_movies(self) -> List[Dict]:
        """Obtener películas duplicadas (optimizada)"""
        query = """
        SELECT
            stream_display_name as title,
            COUNT(*) as duplicate_count,
            GROUP_CONCAT(id) as stream_ids
        FROM streams
        WHERE type = 2 AND stream_display_name IS NOT NULL AND stream_display_name != ''
        GROUP BY stream_display_name
        HAVING COUNT(*) > 1
        ORDER BY duplicate_count DESC, stream_display_name
        LIMIT 100
        """
        return self.execute_query(query)

    def get_all_movies(self) -> List[Dict]:
        """Obtener todas las películas con información completa"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.type as type_id,
            st.type_name,
            CASE
                WHEN s.stream_display_name IS NULL OR s.stream_display_name = '' THEN '[Sin Título]'
                ELSE s.stream_display_name
            END as display_title
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        ORDER BY display_title
        """
        return self.execute_query(query)

    def get_movies_by_name(self, movie_name: str) -> List[Dict]:
        """Obtener todas las películas con un nombre específico"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.type as type_id,
            st.type_name,
            st.type_id as streams_type_id
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies' AND s.stream_display_name = %s
        ORDER BY s.id
        """
        return self.execute_query(query, (movie_name,))

    def search_movies_by_name(self, search_term: str) -> List[Dict]:
        """Buscar películas que contengan el término en el nombre"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.type as type_id,
            st.type_name,
            s.stream_display_name as display_title
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        AND s.stream_display_name IS NOT NULL
        AND s.stream_display_name != ''
        AND s.stream_display_name LIKE %s
        ORDER BY s.stream_display_name
        LIMIT 500
        """
        search_pattern = f"%{search_term}%"
        return self.execute_query(query, (search_pattern,))

    def get_movies_without_title(self) -> List[Dict]:
        """Obtener películas sin título (para limpieza)"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.type as type_id,
            st.type_name,
            '[Sin Título]' as display_title
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        AND (s.stream_display_name IS NULL OR s.stream_display_name = '')
        ORDER BY s.id
        LIMIT 1000
        """
        return self.execute_query(query)

    def delete_movie(self, stream_id: int) -> bool:
        """Eliminar una película (verificando que sea tipo Movies)"""
        # Primero verificar que sea una película
        verify_query = """
        SELECT s.id, st.type_name
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE s.id = %s AND st.type_name = 'Movies'
        """
        movie_check = self.execute_query(verify_query, (stream_id,))

        if not movie_check:
            return False  # No es una película o no existe

        # Eliminar la película
        query = "DELETE FROM streams WHERE id = %s"
        return self.execute_update(query, (stream_id,))

    def delete_stream(self, stream_id: int) -> bool:
        """Eliminar un stream (película, serie, etc.) sin verificar tipo"""
        try:
            query = "DELETE FROM streams WHERE id = %s"
            result = self.execute_update(query, (stream_id,))
            if result:
                logging.info(f"Stream {stream_id} eliminado exitosamente")
            else:
                logging.warning(f"No se pudo eliminar stream {stream_id} (puede que no exista)")
            return result
        except Exception as e:
            logging.error(f"Error eliminando stream {stream_id}: {e}")
            return False

    def get_movie_details(self, stream_id: int) -> Dict:
        """Obtener detalles completos de una película"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.type as type_id,
            st.type_name,
            st.type_id as streams_type_id,
            s.stream_source,
            s.stream_icon,
            s.notes
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE s.id = %s AND st.type_name = 'Movies'
        """
        result = self.execute_query(query, (stream_id,))
        return result[0] if result else {}

    def delete_multiple_movies(self, stream_ids: List[int]) -> Dict[str, int]:
        """Eliminar múltiples películas de manera eficiente"""
        if not stream_ids:
            return {"deleted": 0, "failed": 0}

        deleted_count = 0
        failed_count = 0

        # Verificar que todos sean películas antes de eliminar
        placeholders = ','.join(['%s'] * len(stream_ids))
        verify_query = f"""
        SELECT s.id
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE s.id IN ({placeholders}) AND st.type_name = 'Movies'
        """

        valid_movies = self.execute_query(verify_query, tuple(stream_ids))
        valid_ids = [movie['id'] for movie in valid_movies]

        if not valid_ids:
            return {"deleted": 0, "failed": len(stream_ids)}

        # Eliminar en lotes para mejor rendimiento
        batch_size = 100
        for i in range(0, len(valid_ids), batch_size):
            batch = valid_ids[i:i + batch_size]
            batch_placeholders = ','.join(['%s'] * len(batch))
            delete_query = f"DELETE FROM streams WHERE id IN ({batch_placeholders})"

            try:
                if self.execute_update(delete_query, tuple(batch)):
                    deleted_count += len(batch)
                else:
                    failed_count += len(batch)
            except Exception as e:
                logging.error(f"Error eliminando lote de películas: {e}")
                failed_count += len(batch)

        # Contar IDs que no eran películas válidas
        invalid_count = len(stream_ids) - len(valid_ids)
        failed_count += invalid_count

        return {"deleted": deleted_count, "failed": failed_count}

    def get_duplicate_movies_by_tmdb(self) -> List[Dict]:
        """Obtener películas duplicadas basándose en TMDB ID"""
        query = """
        SELECT
            s.tmdb_id,
            COUNT(*) as duplicate_count,
            GROUP_CONCAT(s.id) as stream_ids,
            GROUP_CONCAT(s.stream_display_name) as titles,
            MAX(s.stream_display_name) as sample_title
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        AND s.tmdb_id IS NOT NULL
        AND s.tmdb_id != 0
        GROUP BY s.tmdb_id
        HAVING COUNT(*) > 1
        ORDER BY duplicate_count DESC, sample_title
        LIMIT 100
        """
        return self.execute_query(query)

    def get_duplicate_series_by_tmdb(self) -> List[Dict]:
        """Obtener series duplicadas basándose en TMDB ID"""
        query = """
        SELECT
            tmdb_id,
            COUNT(*) as duplicate_count,
            GROUP_CONCAT(id) as series_ids,
            GROUP_CONCAT(title) as titles,
            MAX(title) as sample_title
        FROM streams_series
        WHERE tmdb_id IS NOT NULL
        AND tmdb_id != 0
        GROUP BY tmdb_id
        HAVING COUNT(*) > 1
        ORDER BY duplicate_count DESC, sample_title
        LIMIT 100
        """
        return self.execute_query(query)

    def get_movie_tmdb_details(self, stream_id: int) -> Dict:
        """Obtener detalles TMDB de una película específica"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.tmdb_id,
            s.movie_properties,
            s.stream_source,
            s.stream_icon,
            s.notes,
            s.year,
            s.rating,
            st.type_name
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE s.id = %s AND st.type_name = 'Movies'
        """
        result = self.execute_query(query, (stream_id,))
        return result[0] if result else {}

    def get_series_tmdb_details(self, series_id: int) -> Dict:
        """Obtener detalles TMDB de una serie específica"""
        query = """
        SELECT
            id as series_id,
            title,
            tmdb_id,
            genre,
            plot,
            cast,
            rating,
            director,
            release_date,
            year,
            cover,
            cover_big,
            backdrop_path,
            youtube_trailer
        FROM streams_series
        WHERE id = %s
        """
        result = self.execute_query(query, (series_id,))
        return result[0] if result else {}

    def update_movie_from_tmdb(self, stream_id: int, tmdb_data: Dict) -> bool:
        """Actualizar película con datos de TMDB"""
        try:
            query = """
            UPDATE streams
            SET
                stream_display_name = %s,
                tmdb_id = %s,
                year = %s,
                rating = %s,
                updated = CURRENT_TIMESTAMP
            WHERE id = %s
            """

            title = tmdb_data.get('title', '')
            tmdb_id = tmdb_data.get('tmdb_id', 0)
            year = tmdb_data.get('release_date', '')[:4] if tmdb_data.get('release_date') else None
            rating = tmdb_data.get('vote_average', 0)

            return self.execute_update(query, (title, tmdb_id, year, rating, stream_id))

        except Exception as e:
            logging.error(f"Error actualizando película {stream_id} con TMDB: {e}")
            return False

    def update_series_from_tmdb(self, series_id: int, tmdb_data: Dict) -> bool:
        """Actualizar serie con datos de TMDB"""
        try:
            query = """
            UPDATE streams_series
            SET
                title = %s,
                tmdb_id = %s,
                genre = %s,
                plot = %s,
                rating = %s,
                release_date = %s,
                year = %s,
                last_modified = UNIX_TIMESTAMP()
            WHERE id = %s
            """

            title = tmdb_data.get('name', '')
            tmdb_id = tmdb_data.get('tmdb_id', 0)
            genres = ', '.join(tmdb_data.get('genres', []))
            plot = tmdb_data.get('overview', '')
            rating = int(tmdb_data.get('vote_average', 0))
            release_date = tmdb_data.get('first_air_date', '')
            year = release_date[:4] if release_date else None

            return self.execute_update(query, (title, tmdb_id, genres, plot, rating, release_date, year, series_id))

        except Exception as e:
            logging.error(f"Error actualizando serie {series_id} con TMDB: {e}")
            return False

    def create_orphaned_episode(self, stream_id: int) -> bool:
        """Crear un episodio huérfano (sin serie asignada) para testing"""
        # Esto es solo para testing - crear un episodio sin series_id válido
        query = "INSERT INTO streams_episodes (season_num, episode_num, series_id, stream_id) VALUES (1, 1, 99999, %s)"
        return self.execute_update(query, (stream_id,))

    def get_duplicate_episodes(self) -> List[Dict]:
        """Obtener episodios duplicados basándose en series_id, season_num y episode_num"""
        query = """
        SELECT
            se.series_id,
            se.season_num,
            se.episode_num,
            COUNT(*) as duplicate_count,
            GROUP_CONCAT(se.id) as episode_ids,
            GROUP_CONCAT(se.stream_id) as stream_ids,
            ss.title as series_title,
            GROUP_CONCAT(s.stream_display_name) as episode_titles
        FROM streams_episodes se
        LEFT JOIN streams_series ss ON se.series_id = ss.id
        LEFT JOIN streams s ON se.stream_id = s.id
        WHERE se.series_id IS NOT NULL
        AND se.series_id != 0
        AND se.season_num IS NOT NULL
        AND se.episode_num IS NOT NULL
        GROUP BY se.series_id, se.season_num, se.episode_num
        HAVING COUNT(*) > 1
        ORDER BY duplicate_count DESC, ss.title, se.season_num, se.episode_num
        LIMIT 100
        """
        return self.execute_query(query)

    def get_episode_copies_details(self, series_id: int, season_num: int, episode_num: int) -> List[Dict]:
        """Obtener detalles de todas las copias de un episodio duplicado"""
        try:
            query = """
            SELECT
                se.id as episode_id,
                se.stream_id,
                s.stream_display_name as title,
                s.movie_symlink,
                s.direct_source,
                s.direct_proxy,
                s.stream_source,
                s.added,
                CASE
                    WHEN s.movie_symlink = 1 THEN 'Symlink (ALTA)'
                    WHEN s.direct_source = 1 AND s.direct_proxy = 1 THEN 'Direct Source/Proxy (BAJA)'
                    ELSE 'Otro'
                END as priority_type,
                CASE
                    WHEN s.movie_symlink = 1 THEN 1
                    WHEN s.direct_source = 1 AND s.direct_proxy = 1 THEN 3
                    ELSE 2
                END as priority_order,
                COALESCE(
                    CASE
                        WHEN LOWER(s.stream_display_name) LIKE '%%4k%%' OR LOWER(s.stream_display_name) LIKE '%%2160p%%' THEN '4K'
                        WHEN LOWER(s.stream_display_name) LIKE '%%fhd%%' OR LOWER(s.stream_display_name) LIKE '%%1080p%%' THEN 'FHD'
                        WHEN LOWER(s.stream_display_name) LIKE '%%hd%%' OR LOWER(s.stream_display_name) LIKE '%%720p%%' THEN 'HD'
                        WHEN LOWER(s.stream_display_name) LIKE '%%60fps%%' THEN '60FPS'
                        WHEN LOWER(s.stream_display_name) LIKE '%%extended%%' THEN 'EXTENDED'
                        ELSE 'STANDARD'
                    END, 'STANDARD'
                ) as detected_quality,
                ss.title as series_title
            FROM streams_episodes se
            JOIN streams s ON se.stream_id = s.id
            JOIN streams_series ss ON se.series_id = ss.id
            WHERE se.series_id = %s
            AND se.season_num = %s
            AND se.episode_num = %s
            ORDER BY priority_order ASC, s.id DESC
            """

            result = self.execute_query(query, (series_id, season_num, episode_num))

            if result:
                copies = []
                for row in result:
                    copies.append({
                        'episode_id': row['episode_id'],
                        'stream_id': row['stream_id'],
                        'title': row['title'] or f"Episode {row['stream_id']}",
                        'movie_symlink': row['movie_symlink'],
                        'direct_source': row['direct_source'],
                        'direct_proxy': row['direct_proxy'],
                        'stream_source': row['stream_source'],
                        'priority_type': row['priority_type'],
                        'priority_order': row['priority_order'],
                        'detected_quality': row['detected_quality'],
                        'series_title': row['series_title'],
                        'added': row['added']
                    })
                return copies

            return []

        except Exception as e:
            print(f"Error getting episode copies details: {e}")
            return []

    def get_series_episodes_detailed(self, series_id: int) -> List[Dict]:
        """Obtener todos los episodios de una serie con detalles completos"""
        query = """
        SELECT
            se.id as episode_id,
            se.season_num,
            se.episode_num,
            se.stream_id,
            s.stream_display_name as episode_title,
            s.type,
            st.type_name,
            s.movie_symlink,
            s.direct_source,
            s.direct_proxy,
            s.added,
            s.last_modified
        FROM streams_episodes se
        LEFT JOIN streams s ON se.stream_id = s.id
        LEFT JOIN streams_types st ON s.type = st.type_id
        WHERE se.series_id = %s
        ORDER BY se.season_num, se.episode_num, se.id
        """
        return self.execute_query(query, (series_id,))

    def get_orphaned_episodes_detailed(self) -> List[Dict]:
        """Obtener episodios huérfanos con información detallada"""
        query = """
        SELECT
            se.id as episode_id,
            se.stream_id,
            se.series_id,
            se.season_num,
            se.episode_num,
            s.stream_display_name as episode_title,
            s.type,
            st.type_name,
            s.added,
            CASE
                WHEN se.series_id IS NULL THEN 'NULL series_id'
                WHEN se.series_id = 0 THEN 'Zero series_id'
                WHEN ss.id IS NULL THEN 'Invalid series_id'
                ELSE 'Unknown'
            END as orphan_reason
        FROM streams_episodes se
        LEFT JOIN streams_series ss ON se.series_id = ss.id
        LEFT JOIN streams s ON se.stream_id = s.id
        LEFT JOIN streams_types st ON s.type = st.type_id
        WHERE (se.series_id IS NULL OR se.series_id = 0 OR ss.id IS NULL)
        ORDER BY se.id
        LIMIT 500
        """
        return self.execute_query(query)

    def fix_orphaned_episode(self, episode_id: int, new_series_id: int) -> bool:
        """Reparar un episodio huérfano asignándolo a una serie"""
        query = "UPDATE streams_episodes SET series_id = %s WHERE id = %s"
        return self.execute_update(query, (new_series_id, episode_id))

    def delete_duplicate_episode(self, episode_id: int) -> bool:
        """Eliminar un episodio duplicado"""
        query = "DELETE FROM streams_episodes WHERE id = %s"
        return self.execute_update(query, (episode_id,))

    def get_series_by_title_pattern(self, title_pattern: str) -> List[Dict]:
        """Buscar series por patrón de título"""
        query = """
        SELECT
            id as series_id,
            title,
            tmdb_id,
            year,
            genre,
            rating
        FROM streams_series
        WHERE title LIKE %s
        ORDER BY title
        LIMIT 50
        """
        return self.execute_query(query, (f"%{title_pattern}%",))

    def analyze_m3u_content(self, m3u_entries: List[Dict]) -> Dict:
        """Analizar contenido M3U contra la base de datos existente"""
        analysis = {
            'total_entries': len(m3u_entries),
            'existing_series': [],
            'missing_series': [],
            'existing_episodes': [],
            'missing_episodes': [],
            'potential_matches': []
        }

        for entry in m3u_entries:
            title = entry.get('title', '')

            # Detectar si es serie o película
            if self._is_series_entry(title):
                series_info = self._extract_series_info(title)

                # Buscar serie existente
                existing_series = self.get_series_by_title_pattern(series_info['series_title'])

                if existing_series:
                    analysis['existing_series'].append({
                        'entry': entry,
                        'series_info': series_info,
                        'matches': existing_series
                    })

                    # Verificar si el episodio específico existe
                    for series in existing_series:
                        episodes = self.get_series_episodes_detailed(series['series_id'])
                        episode_exists = any(
                            ep['season_num'] == series_info.get('season') and
                            ep['episode_num'] == series_info.get('episode')
                            for ep in episodes
                        )

                        if episode_exists:
                            analysis['existing_episodes'].append({
                                'entry': entry,
                                'series_info': series_info,
                                'series': series
                            })
                        else:
                            analysis['missing_episodes'].append({
                                'entry': entry,
                                'series_info': series_info,
                                'series': series
                            })
                else:
                    analysis['missing_series'].append({
                        'entry': entry,
                        'series_info': series_info
                    })

        return analysis

    def _is_series_entry(self, title: str) -> bool:
        """Detectar si un título corresponde a una serie"""
        series_patterns = [
            r'S\d+E\d+',  # S01E01
            r'Season\s+\d+',  # Season 1
            r'Temporada\s+\d+',  # Temporada 1
            r'\d+x\d+',  # 1x01
            r'Episode\s+\d+',  # Episode 1
            r'Episodio\s+\d+',  # Episodio 1
        ]

        import re
        for pattern in series_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                return True
        return False

    def _extract_series_info(self, title: str) -> Dict:
        """Extraer información de serie del título"""
        import re

        info = {
            'series_title': title,
            'season': None,
            'episode': None,
            'episode_title': None
        }

        # Patrón S01E01
        match = re.search(r'(.+?)\s*S(\d+)E(\d+)(?:\s*-?\s*(.+))?', title, re.IGNORECASE)
        if match:
            info['series_title'] = match.group(1).strip()
            info['season'] = int(match.group(2))
            info['episode'] = int(match.group(3))
            if match.group(4):
                info['episode_title'] = match.group(4).strip()
            return info

        # Patrón 1x01
        match = re.search(r'(.+?)\s*(\d+)x(\d+)(?:\s*-?\s*(.+))?', title, re.IGNORECASE)
        if match:
            info['series_title'] = match.group(1).strip()
            info['season'] = int(match.group(2))
            info['episode'] = int(match.group(3))
            if match.group(4):
                info['episode_title'] = match.group(4).strip()
            return info

        # Patrón Season/Temporada
        match = re.search(r'(.+?)\s*(?:Season|Temporada)\s+(\d+).*?(?:Episode|Episodio)\s+(\d+)(?:\s*-?\s*(.+))?', title, re.IGNORECASE)
        if match:
            info['series_title'] = match.group(1).strip()
            info['season'] = int(match.group(2))
            info['episode'] = int(match.group(3))
            if match.group(4):
                info['episode_title'] = match.group(4).strip()
            return info

        return info

    def get_series_statistics(self) -> Dict:
        """Obtener estadísticas de series y episodios"""
        stats = {}

        # Total de series
        series_count = self.execute_query("SELECT COUNT(*) as count FROM streams_series")
        stats['total_series'] = series_count[0]['count'] if series_count else 0

        # Total de episodios
        episodes_count = self.execute_query("SELECT COUNT(*) as count FROM streams_episodes")
        stats['total_episodes'] = episodes_count[0]['count'] if episodes_count else 0

        # Series sin episodios
        series_no_episodes = self.execute_query("""
            SELECT COUNT(*) as count
            FROM streams_series ss
            LEFT JOIN streams_episodes se ON ss.id = se.series_id
            WHERE se.id IS NULL
        """)
        stats['series_without_episodes'] = series_no_episodes[0]['count'] if series_no_episodes else 0

        # Episodios huérfanos
        orphaned = self.execute_query("""
            SELECT COUNT(*) as count
            FROM streams_episodes se
            LEFT JOIN streams_series ss ON se.series_id = ss.id
            WHERE se.series_id IS NULL OR se.series_id = 0 OR ss.id IS NULL
        """)
        stats['orphaned_episodes'] = orphaned[0]['count'] if orphaned else 0

        # Episodios duplicados
        duplicates = self.execute_query("""
            SELECT COUNT(*) as count FROM (
                SELECT series_id, season_num, episode_num
                FROM streams_episodes
                WHERE series_id IS NOT NULL AND series_id != 0
                GROUP BY series_id, season_num, episode_num
                HAVING COUNT(*) > 1
            ) as dup
        """)
        stats['duplicate_episodes'] = duplicates[0]['count'] if duplicates else 0

        return stats

    def get_duplicate_movies_with_priority(self) -> List[Dict]:
        """Obtener películas duplicadas con información de prioridad"""
        query = """
        SELECT
            s.tmdb_id,
            COUNT(*) as duplicate_count,
            MAX(s.stream_display_name) as sample_title,
            GROUP_CONCAT(CONCAT(s.id, ':', s.movie_symlink, ':', s.direct_source, ':', s.direct_proxy)
                        ORDER BY s.movie_symlink DESC, s.direct_source ASC, s.direct_proxy ASC) as copies_info,
            SUM(CASE WHEN s.movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_count,
            SUM(CASE WHEN s.direct_source = 1 AND s.direct_proxy = 1 THEN 1 ELSE 0 END) as direct_count,
            SUM(CASE WHEN s.movie_symlink = 0 AND s.direct_source = 0 AND s.direct_proxy = 0 THEN 1 ELSE 0 END) as other_count
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        AND s.tmdb_id IS NOT NULL
        AND s.tmdb_id != 0
        GROUP BY s.tmdb_id
        HAVING COUNT(*) > 1
        ORDER BY duplicate_count DESC, sample_title
        LIMIT 100
        """
        return self.execute_query(query)

    def get_movie_copies_details(self, tmdb_id: int) -> List[Dict]:
        """Obtener detalles de todas las copias de una película por TMDB ID"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.movie_symlink,
            s.direct_source,
            s.direct_proxy,
            s.stream_source,
            s.year,
            s.rating,
            CASE
                WHEN s.movie_symlink = 1 THEN 'Symlink (ALTA)'
                WHEN s.direct_source = 1 AND s.direct_proxy = 1 THEN 'Direct Source/Proxy (BAJA)'
                ELSE 'Otro'
            END as priority_type,
            CASE
                WHEN s.movie_symlink = 1 THEN 1
                WHEN s.direct_source = 1 AND s.direct_proxy = 1 THEN 3
                ELSE 2
            END as priority_order
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        AND s.tmdb_id = %s
        ORDER BY priority_order ASC, s.id ASC
        """
        return self.execute_query(query, (tmdb_id,))

    def get_recommended_deletions(self, tmdb_id: int) -> Dict:
        """Obtener recomendaciones de qué copias eliminar basándose en prioridades"""
        copies = self.get_movie_copies_details(tmdb_id)

        if not copies:
            return {"keep": [], "delete": [], "reason": "No se encontraron copias"}

        # Separar por tipo de prioridad
        symlinks = [c for c in copies if c['movie_symlink'] == 1]
        direct_sources = [c for c in copies if c['direct_source'] == 1 and c['direct_proxy'] == 1]
        others = [c for c in copies if c['movie_symlink'] == 0 and c['direct_source'] == 0 and c['direct_proxy'] == 0]

        keep = []
        delete = []
        reason = ""

        if symlinks:
            # Si hay symlinks, mantener el primero y eliminar el resto
            keep.append(symlinks[0])
            if len(symlinks) > 1:
                delete.extend(symlinks[1:])
            # Eliminar todos los direct sources
            delete.extend(direct_sources)
            delete.extend(others)
            reason = f"Mantener symlink (prioridad alta), eliminar {len(direct_sources)} direct sources"
        elif direct_sources:
            # Si no hay symlinks, mantener el primer direct source
            keep.append(direct_sources[0])
            if len(direct_sources) > 1:
                delete.extend(direct_sources[1:])
            delete.extend(others)
            reason = f"Mantener 1 direct source, eliminar {len(direct_sources)-1} duplicados"
        else:
            # Si solo hay "otros", mantener el primero
            if others:
                keep.append(others[0])
                if len(others) > 1:
                    delete.extend(others[1:])
                reason = "Mantener primera copia disponible"

        return {
            "keep": keep,
            "delete": delete,
            "reason": reason,
            "total_copies": len(copies),
            "symlink_count": len(symlinks),
            "direct_count": len(direct_sources),
            "other_count": len(others)
        }

    def apply_advanced_priority_cleanup(self, tmdb_id: int, auto_confirm: bool = False) -> Dict:
        """Aplicar limpieza con nuevas prioridades avanzadas:
        1. Mantener 4K symlinks
        2. Mantener 60FPS symlinks
        3. Mantener FHD/HD symlinks
        4. Para direct sources: mantener solo el ID más nuevo
        """
        try:
            # Obtener todas las copias
            copies = self.get_movie_copies_by_tmdb(tmdb_id)

            if len(copies) <= 1:
                return {"deleted": 0, "kept": len(copies), "message": "No hay duplicados para limpiar"}

            # Separar por tipos
            symlinks = [copy for copy in copies if copy['movie_symlink'] == 1]
            direct_sources = [copy for copy in copies if copy['direct_source'] == 1 or copy['direct_proxy'] == 1]
            others = [copy for copy in copies if copy['movie_symlink'] != 1 and copy['direct_source'] != 1 and copy['direct_proxy'] != 1]

            keep_ids = []
            delete_ids = []

            # LÓGICA PARA SYMLINKS (mantener según prioridades)
            if symlinks:
                # Prioridad 1: 4K symlinks
                symlinks_4k = [s for s in symlinks if s['detected_quality'] == '4K']
                if symlinks_4k:
                    keep_ids.extend([s['stream_id'] for s in symlinks_4k])
                    # Eliminar otros symlinks que no sean 4K
                    delete_ids.extend([s['stream_id'] for s in symlinks if s['detected_quality'] != '4K'])
                else:
                    # Prioridad 2: 60FPS symlinks
                    symlinks_60fps = [s for s in symlinks if s['detected_quality'] == '60FPS']
                    if symlinks_60fps:
                        keep_ids.extend([s['stream_id'] for s in symlinks_60fps])
                        delete_ids.extend([s['stream_id'] for s in symlinks if s['detected_quality'] != '60FPS'])
                    else:
                        # Prioridad 3: FHD/HD symlinks
                        symlinks_fhd_hd = [s for s in symlinks if s['detected_quality'] in ['FHD', 'HD']]
                        if symlinks_fhd_hd:
                            keep_ids.extend([s['stream_id'] for s in symlinks_fhd_hd])
                            delete_ids.extend([s['stream_id'] for s in symlinks if s['detected_quality'] not in ['FHD', 'HD']])
                        else:
                            # Mantener todos los symlinks si no hay calidades prioritarias
                            keep_ids.extend([s['stream_id'] for s in symlinks])

                # Si hay symlinks, eliminar TODOS los direct sources
                delete_ids.extend([d['stream_id'] for d in direct_sources])
            else:
                # NO HAY SYMLINKS: Para direct sources, mantener solo el ID más nuevo
                if direct_sources:
                    # Ordenar por stream_id (ID más alto = más nuevo)
                    direct_sources_sorted = sorted(direct_sources, key=lambda x: x['stream_id'], reverse=True)
                    newest_direct = direct_sources_sorted[0]
                    keep_ids.append(newest_direct['stream_id'])
                    # Eliminar el resto de direct sources
                    delete_ids.extend([d['stream_id'] for d in direct_sources_sorted[1:]])

            # Mantener otros tipos si no hay symlinks ni direct sources
            if not symlinks and not direct_sources:
                keep_ids.extend([o['stream_id'] for o in others])
            else:
                # Si hay symlinks o direct sources, eliminar otros
                delete_ids.extend([o['stream_id'] for o in others])

            if not auto_confirm:
                return {
                    "deleted": 0,
                    "kept": len(keep_ids),
                    "to_delete": len(delete_ids),
                    "message": "Vista previa de limpieza avanzada",
                    "keep_ids": keep_ids,
                    "delete_ids": delete_ids,
                    "logic_applied": self._get_cleanup_logic_description(symlinks, direct_sources, others)
                }

            # Aplicar eliminaciones
            deleted_count = 0
            for stream_id in delete_ids:
                if self.delete_stream(stream_id):
                    deleted_count += 1

            return {
                "deleted": deleted_count,
                "kept": len(keep_ids),
                "total_processed": len(copies),
                "message": f"Limpieza avanzada aplicada: {deleted_count} eliminados, {len(keep_ids)} mantenidos",
                "logic_applied": self._get_cleanup_logic_description(symlinks, direct_sources, others)
            }

        except Exception as e:
            logging.error(f"Error en limpieza avanzada para TMDB {tmdb_id}: {e}")
            return {"deleted": 0, "kept": 0, "message": f"Error: {str(e)}"}

    def _get_cleanup_logic_description(self, symlinks, direct_sources, others):
        """Obtener descripción de la lógica aplicada"""
        if symlinks:
            symlinks_4k = [s for s in symlinks if s['detected_quality'] == '4K']
            symlinks_60fps = [s for s in symlinks if s['detected_quality'] == '60FPS']
            symlinks_fhd_hd = [s for s in symlinks if s['detected_quality'] in ['FHD', 'HD']]

            if symlinks_4k:
                return "🥇 Mantenidos 4K symlinks, eliminados otros"
            elif symlinks_60fps:
                return "🥈 Mantenidos 60FPS symlinks, eliminados otros"
            elif symlinks_fhd_hd:
                return "🥉 Mantenidos FHD/HD symlinks, eliminados otros"
            else:
                return "🔗 Mantenidos todos los symlinks, eliminados direct sources"
        elif direct_sources:
            return "📡 Mantenido direct source más nuevo, eliminados otros"
        else:
            return "⚙️ Mantenidos otros tipos (sin symlinks ni direct sources)"

    def apply_smart_cleanup(self, tmdb_id: int, auto_confirm: bool = False) -> Dict:
        """Aplicar limpieza inteligente basada en prioridades"""
        recommendations = self.get_recommended_deletions(tmdb_id)

        if not recommendations["delete"]:
            return {"deleted": 0, "kept": len(recommendations["keep"]), "message": "No hay duplicados para eliminar"}

        if not auto_confirm:
            # En modo manual, solo devolver las recomendaciones
            return {
                "deleted": 0,
                "kept": len(recommendations["keep"]),
                "message": "Recomendaciones generadas (no aplicadas)",
                "recommendations": recommendations
            }

        # En modo automático, aplicar las eliminaciones
        deleted_count = 0
        delete_ids = [copy['stream_id'] for copy in recommendations["delete"]]

        for stream_id in delete_ids:
            if self.delete_movie(stream_id):
                deleted_count += 1

        return {
            "deleted": deleted_count,
            "kept": len(recommendations["keep"]),
            "message": f"Eliminados {deleted_count}/{len(delete_ids)} duplicados automáticamente",
            "recommendations": recommendations
        }

    def get_mass_cleanup_summary(self, tmdb_ids: List[int]) -> Dict:
        """Obtener resumen de limpieza masiva para múltiples TMDB IDs"""
        total_copies = 0
        total_to_keep = 0
        total_to_delete = 0
        groups_processed = 0

        summary_details = []

        for tmdb_id in tmdb_ids:
            recommendations = self.get_recommended_deletions(tmdb_id)

            if recommendations["total_copies"] > 0:
                groups_processed += 1
                total_copies += recommendations["total_copies"]
                total_to_keep += len(recommendations["keep"])
                total_to_delete += len(recommendations["delete"])

                # Obtener título para el resumen
                copies = self.get_movie_copies_details(tmdb_id)
                title = copies[0]['title'] if copies else f"TMDB {tmdb_id}"

                summary_details.append({
                    'tmdb_id': tmdb_id,
                    'title': title,
                    'total_copies': recommendations["total_copies"],
                    'to_keep': len(recommendations["keep"]),
                    'to_delete': len(recommendations["delete"]),
                    'reason': recommendations["reason"]
                })

        return {
            'groups_processed': groups_processed,
            'total_copies': total_copies,
            'total_to_keep': total_to_keep,
            'total_to_delete': total_to_delete,
            'reduction_percent': (total_to_delete / total_copies * 100) if total_copies > 0 else 0,
            'details': summary_details
        }

    def apply_mass_smart_cleanup(self, tmdb_ids: List[int]) -> Dict:
        """Aplicar limpieza inteligente masiva a múltiples TMDB IDs"""
        results = {
            'processed': 0,
            'total_deleted': 0,
            'total_kept': 0,
            'errors': 0,
            'details': []
        }

        for tmdb_id in tmdb_ids:
            try:
                result = self.apply_smart_cleanup(tmdb_id, auto_confirm=True)

                results['processed'] += 1
                results['total_deleted'] += result['deleted']
                results['total_kept'] += result['kept']

                if result['deleted'] == 0 and 'recommendations' in result:
                    if len(result['recommendations']['delete']) > 0:
                        results['errors'] += 1

                results['details'].append({
                    'tmdb_id': tmdb_id,
                    'deleted': result['deleted'],
                    'kept': result['kept'],
                    'message': result['message']
                })

            except Exception as e:
                results['errors'] += 1
                results['details'].append({
                    'tmdb_id': tmdb_id,
                    'deleted': 0,
                    'kept': 0,
                    'message': f"Error: {str(e)}"
                })
                logging.error(f"Error en limpieza masiva para TMDB {tmdb_id}: {e}")

        return results

    def get_movies_with_multiple_symlinks(self) -> List[Dict]:
        """Obtener películas que tienen múltiples symlinks (diferentes calidades)"""
        query = """
        SELECT
            s.tmdb_id,
            COUNT(*) as symlink_count,
            MAX(s.stream_display_name) as sample_title,
            GROUP_CONCAT(s.id ORDER BY s.id) as stream_ids,
            GROUP_CONCAT(s.stream_display_name ORDER BY s.id SEPARATOR '|||') as all_titles
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        AND s.movie_symlink = 1
        AND s.tmdb_id IS NOT NULL
        AND s.tmdb_id != 0
        GROUP BY s.tmdb_id
        HAVING COUNT(*) > 1
        ORDER BY symlink_count DESC, sample_title
        LIMIT 50
        """
        return self.execute_query(query)

    def get_symlink_details_for_manual_selection(self, tmdb_id: int) -> List[Dict]:
        """Obtener detalles de todos los symlinks de una película para selección manual"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.movie_symlink,
            s.direct_source,
            s.direct_proxy,
            s.stream_source,
            s.year,
            s.rating,
            s.target_container,
            s.added,
            CASE
                WHEN s.stream_display_name LIKE '%%4k%%' OR s.stream_display_name LIKE '%%4K%%' THEN '4K'
                WHEN s.stream_display_name LIKE '%%FHD%%' THEN 'FHD'
                WHEN s.stream_display_name LIKE '%%HD%%' THEN 'HD'
                WHEN s.stream_display_name LIKE '%%SD%%' THEN 'SD'
                WHEN s.stream_display_name LIKE '%%60fps%%' OR s.stream_display_name LIKE '%%60FPS%%' THEN '60FPS'
                WHEN s.stream_display_name LIKE '%%EXTENDIDA%%' OR s.stream_display_name LIKE '%%EXTENDED%%' THEN 'EXTENDED'
                ELSE 'STANDARD'
            END as detected_quality,
            CASE
                WHEN s.movie_symlink = 1 THEN 'Symlink (LOCAL)'
                WHEN s.direct_source = 1 AND s.direct_proxy = 1 THEN 'Direct Source/Proxy (REMOTO)'
                ELSE 'Otro'
            END as source_type,
            CASE
                WHEN s.stream_display_name LIKE '%%4k%%' OR s.stream_display_name LIKE '%%4K%%' THEN 1
                WHEN s.stream_display_name LIKE '%%FHD%%' THEN 2
                WHEN s.stream_display_name LIKE '%%60fps%%' OR s.stream_display_name LIKE '%%60FPS%%' THEN 3
                WHEN s.stream_display_name LIKE '%%HD%%' THEN 4
                WHEN s.stream_display_name LIKE '%%EXTENDIDA%%' OR s.stream_display_name LIKE '%%EXTENDED%%' THEN 5
                WHEN s.stream_display_name LIKE '%%SD%%' THEN 6
                ELSE 7
            END as quality_priority
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        AND s.tmdb_id = %s
        ORDER BY s.movie_symlink DESC, quality_priority ASC, s.rating DESC, s.id ASC
        """
        return self.execute_query(query, (tmdb_id,))

    def get_manual_selection_recommendations(self, tmdb_id: int) -> Dict:
        """Obtener recomendaciones inteligentes para selección manual"""
        all_copies = self.get_symlink_details_for_manual_selection(tmdb_id)

        if not all_copies:
            return {"symlinks": [], "direct_sources": [], "recommendations": "No se encontraron copias"}

        # Separar symlinks de direct sources
        symlinks = [c for c in all_copies if c['movie_symlink'] == 1]
        direct_sources = [c for c in all_copies if c['direct_source'] == 1 and c['direct_proxy'] == 1]
        others = [c for c in all_copies if c['movie_symlink'] == 0 and c['direct_source'] == 0 and c['direct_proxy'] == 0]

        recommendations = ""

        if len(symlinks) > 1:
            # Múltiples symlinks - necesita selección manual
            qualities = set(s['detected_quality'] for s in symlinks)
            recommendations = f"MÚLTIPLES SYMLINKS DETECTADOS ({len(symlinks)})\n"
            recommendations += f"Calidades encontradas: {', '.join(sorted(qualities))}\n"
            recommendations += f"Recomendación: Revisar y seleccionar manualmente las calidades deseadas\n"
            recommendations += f"Sugerencia: Mantener 4K > FHD > 60FPS > HD > EXTENDED > STANDARD"
        elif len(symlinks) == 1:
            recommendations = f"UN SYMLINK ENCONTRADO\n"
            recommendations += f"Calidad: {symlinks[0]['detected_quality']}\n"
            recommendations += f"Recomendación: Mantener symlink, eliminar {len(direct_sources)} direct sources"
        else:
            recommendations = f"SIN SYMLINKS\n"
            recommendations += f"Recomendación: Mantener mejor direct source, eliminar {len(direct_sources)-1} duplicados"

        return {
            "symlinks": symlinks,
            "direct_sources": direct_sources,
            "others": others,
            "recommendations": recommendations,
            "total_copies": len(all_copies),
            "needs_manual_selection": len(symlinks) > 1
        }

    def apply_manual_selection(self, tmdb_id: int, keep_stream_ids: List[int]) -> Dict:
        """Aplicar selección manual - mantener solo los IDs especificados"""
        # Obtener todas las copias
        all_copies = self.get_symlink_details_for_manual_selection(tmdb_id)

        if not all_copies:
            return {"deleted": 0, "kept": 0, "message": "No se encontraron copias"}

        # Identificar qué eliminar
        all_stream_ids = [copy['stream_id'] for copy in all_copies]
        delete_stream_ids = [sid for sid in all_stream_ids if sid not in keep_stream_ids]

        if not delete_stream_ids:
            return {"deleted": 0, "kept": len(keep_stream_ids), "message": "No hay elementos para eliminar"}

        # Eliminar los no seleccionados
        deleted_count = 0
        for stream_id in delete_stream_ids:
            if self.delete_movie(stream_id):
                deleted_count += 1

        return {
            "deleted": deleted_count,
            "kept": len(keep_stream_ids),
            "message": f"Selección manual aplicada: {deleted_count} eliminados, {len(keep_stream_ids)} mantenidos",
            "total_processed": len(all_stream_ids)
        }

    def get_movie_copies_by_tmdb(self, tmdb_id):
        """Obtener todas las copias de una película por TMDB ID con información detallada"""
        try:
            query = """
            SELECT
                s.id as stream_id,
                s.stream_display_name as title,
                s.movie_symlink,
                s.direct_source,
                s.direct_proxy,
                COALESCE(
                    CASE
                        WHEN LOWER(s.stream_display_name) LIKE '%%4k%%' OR LOWER(s.stream_display_name) LIKE '%%2160p%%' THEN '4K'
                        WHEN LOWER(s.stream_display_name) LIKE '%%fhd%%' OR LOWER(s.stream_display_name) LIKE '%%1080p%%' THEN 'FHD'
                        WHEN LOWER(s.stream_display_name) LIKE '%%hd%%' OR LOWER(s.stream_display_name) LIKE '%%720p%%' THEN 'HD'
                        WHEN LOWER(s.stream_display_name) LIKE '%%60fps%%' THEN '60FPS'
                        WHEN LOWER(s.stream_display_name) LIKE '%%extended%%' THEN 'EXTENDED'
                        ELSE 'STANDARD'
                    END, 'STANDARD'
                ) as detected_quality,
                s.tmdb_id as movie_tmdb_id
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies'
            AND s.tmdb_id = %s
            ORDER BY
                s.movie_symlink DESC,
                CASE
                    WHEN LOWER(s.stream_display_name) LIKE '%%4k%%' OR LOWER(s.stream_display_name) LIKE '%%2160p%%' THEN 1
                    WHEN LOWER(s.stream_display_name) LIKE '%%fhd%%' OR LOWER(s.stream_display_name) LIKE '%%1080p%%' THEN 2
                    WHEN LOWER(s.stream_display_name) LIKE '%%hd%%' OR LOWER(s.stream_display_name) LIKE '%%720p%%' THEN 3
                    WHEN LOWER(s.stream_display_name) LIKE '%%60fps%%' THEN 4
                    WHEN LOWER(s.stream_display_name) LIKE '%%extended%%' THEN 5
                    ELSE 6
                END,
                s.stream_display_name
            """

            result = self.execute_query(query, (tmdb_id,))

            if result:
                copies = []
                for row in result:
                    copies.append({
                        'stream_id': row['stream_id'],
                        'title': row['title'] or f"Stream {row['stream_id']}",
                        'movie_symlink': row['movie_symlink'],
                        'direct_source': row['direct_source'],
                        'direct_proxy': row['direct_proxy'],
                        'detected_quality': row['detected_quality'],
                        'movie_tmdb_id': row['movie_tmdb_id']
                    })
                return copies

            return []

        except Exception as e:
            print(f"Error getting movie copies by TMDB: {e}")
            return []
