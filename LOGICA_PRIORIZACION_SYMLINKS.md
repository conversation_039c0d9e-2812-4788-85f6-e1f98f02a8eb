# 🔗 LÓGICA DE PRIORIZACIÓN DE SYMLINKS IMPLEMENTADA

## 📅 Fecha: 2025-06-21
## 🎯 Estado: ✅ SYMLINKS COMO MÁXIMA PRIORIDAD IMPLEMENTADO

---

## 🚨 **REQUERIMIENTO DEL USUARIO:**

### **🔗 Symlinks = MÁXIMA PRIORIDAD**
```
"Los symlinks son nuestra mayor prioridad de mantener vivos en el sistema"
```

### **📁 Lógica de Borrado Inteligente:**
- **Symlink + Direct** → <PERSON><PERSON><PERSON> direct, mantener symlink
- **M<PERSON><PERSON>les Directs** → Mantener el más reciente (ID más alto)
- **M<PERSON><PERSON>les Symlinks** → Mantener el más reciente (ID más alto)
- **SIEMPRE mantener al menos 1 episodio**

### **🔍 Búsqueda Manual de Series:**
- Cuadro de búsqueda para series específicas
- Escaneo individual de duplicados por serie
- Aplicar misma lógica de priorización

---

## 🛠️ **IMPLEMENTACIÓN COMPLETA:**

### **🔍 1. Ventana de Búsqueda Manual de Series**

#### **Nuevo Botón Agregado:**
```python
tk.Button(series_frame, text="🔍 Search Series Duplicates",
         bg=self.colors['accent'], fg='white', font=self.font_mono,
         relief='flat', command=self.show_series_search_window).pack(fill='x', pady=2)
```

#### **Funcionalidades de la Ventana:**
- **🔍 Búsqueda por nombre:** Entry field para buscar series específicas
- **📊 Análisis detallado:** Muestra duplicados encontrados por serie
- **💡 Recomendaciones:** Sugiere acciones basadas en tipos de duplicados
- **🧹 Limpieza individual:** Botón para limpiar cada serie específica

### **⚡ 2. Función de Búsqueda Inteligente**

```python
def search_series_for_duplicates(self, series_name, search_window):
    # Buscar series que coincidan con el patrón
    matching_series = self.db.get_series_by_title_pattern(series_name)
    
    for series in matching_series:
        # Obtener episodios de esta serie
        episodes = self.db.get_series_episodes_detailed(series_id)
        
        # Analizar duplicados en esta serie
        episode_groups = {}
        for episode in episodes:
            episode_key = f"S{season_num:02d}E{episode_num:02d}"
            episode_groups[episode_key].append({
                'stream_id': episode['stream_id'],
                'is_symlink': bool(episode.get('movie_symlink')),
                'direct_source': bool(episode.get('direct_source'))
            })
        
        # Mostrar análisis y recomendaciones
        if symlinks and directs:
            results_text.insert("💡 Recommendation: Keep symlinks, delete directs")
        elif len(directs) > 1:
            results_text.insert("💡 Recommendation: Keep newest direct, delete others")
```

### **🔗 3. Lógica de Priorización de Symlinks**

#### **Función Principal:**
```python
def apply_symlink_priority_logic(self, episode_list):
    # Separar symlinks y directs
    symlinks = [ep for ep in episode_list if ep['is_symlink']]
    directs = [ep for ep in episode_list if not ep['is_symlink']]
    
    # LÓGICA DE PRIORIZACIÓN
    if symlinks:
        # Si hay symlinks, siempre tienen prioridad
        # Mantener el symlink más reciente (ID más alto)
        symlinks.sort(key=lambda x: x['stream_id'], reverse=True)
        to_keep = symlinks[0]
        
        # Eliminar todos los directs y symlinks adicionales
        to_delete = directs + symlinks[1:]
        
    else:
        # Si solo hay directs, mantener el más reciente
        directs.sort(key=lambda x: x['stream_id'], reverse=True)
        to_keep = directs[0]
        to_delete = directs[1:]
    
    return to_keep, to_delete
```

#### **🎯 Casos de Uso Cubiertos:**

| **Escenario** | **Acción** | **Resultado** |
|---------------|------------|---------------|
| 🔗 1 Symlink + 📁 2 Directs | Mantener symlink, borrar directs | ✅ Symlink preservado |
| 🔗 2 Symlinks + 📁 1 Direct | Mantener symlink más reciente | ✅ Symlink más nuevo |
| 📁 3 Directs solamente | Mantener direct más reciente | ✅ Direct más nuevo |
| 🔗 3 Symlinks solamente | Mantener symlink más reciente | ✅ Symlink más nuevo |

### **🧹 4. Limpieza de Series Específicas**

```python
def clean_series_duplicates(self, series_id, series_name, parent_window):
    # Obtener todos los episodios de la serie
    episodes = self.db.get_series_episodes_detailed(series_id)
    
    # Agrupar episodios por temporada y episodio
    episode_groups = {}
    for episode in episodes:
        episode_key = f"S{season_num:02d}E{episode_num:02d}"
        episode_groups[episode_key].append({
            'stream_id': episode['stream_id'],
            'is_symlink': bool(episode.get('movie_symlink')),
            'direct_source': bool(episode.get('direct_source'))
        })
    
    # Procesar duplicados con lógica de priorización
    for episode_key, episode_list in episode_groups.items():
        if len(episode_list) > 1:
            # Aplicar lógica de priorización
            to_keep, to_delete = self.apply_symlink_priority_logic(episode_list)
            
            # Eliminar duplicados
            for episode_to_delete in to_delete:
                self.db.delete_duplicate_episode(episode_to_delete['stream_id'])
```

### **⚡ 5. Mass Delete Mejorado con Priorización**

#### **Antes (Solo por ID más alto):**
```python
# Sort IDs and keep the highest (most recent)
episode_ids_int.sort()
keep_id = episode_ids_int[-1]  # Highest ID (most recent)
delete_ids = episode_ids_int[:-1]  # All except the highest
```

#### **Ahora (Lógica de Symlinks):**
```python
# Get detailed information for each episode copy
episode_details = []
for episode_id in episode_ids_int:
    detail_query = """
    SELECT s.id as stream_id, s.movie_symlink, s.direct_source
    FROM streams s WHERE s.id = %s
    """
    detail_result = self.db.execute_query(detail_query, (episode_id,))
    episode_details.append({
        'stream_id': detail['stream_id'],
        'is_symlink': bool(detail.get('movie_symlink')),
        'direct_source': bool(detail.get('direct_source'))
    })

# Apply symlink priority logic
to_keep, to_delete = self.apply_symlink_priority_logic(episode_details)
```

### **📊 6. Diálogo de Confirmación Mejorado**

#### **Antes:**
```
"✅ KEEPS: The most recent copy (highest ID) of each episode"
"🗑️ DELETES: All older duplicate copies"
```

#### **Ahora:**
```
"🔗 SYMLINK PRIORITY LOGIC:
   • If symlink + direct exist → Keep symlink, delete direct
   • If multiple symlinks → Keep newest symlink
   • If multiple directs only → Keep newest direct
   • Always keep at least 1 copy of each episode"
```

---

## 🎯 **BENEFICIOS DE LA IMPLEMENTACIÓN:**

### **🔗 Preservación de Symlinks:**
- **Máxima prioridad** a symlinks en todos los escenarios
- **Eliminación inteligente** de directs cuando hay symlinks disponibles
- **Preservación de calidad** manteniendo las mejores fuentes

### **🔍 Búsqueda Granular:**
- **Búsqueda por serie específica** para análisis detallado
- **Recomendaciones inteligentes** basadas en tipos de duplicados
- **Limpieza individual** para control preciso

### **⚡ Eficiencia Mejorada:**
- **Lógica unificada** para mass delete y limpieza individual
- **Análisis detallado** antes de cualquier eliminación
- **Confirmaciones claras** sobre qué se mantendrá y qué se eliminará

### **🛡️ Seguridad:**
- **Siempre mantener al menos 1 copia** de cada episodio
- **Confirmaciones detalladas** antes de cualquier borrado
- **Logging completo** de todas las acciones realizadas

---

## 📋 **FLUJO DE TRABAJO NUEVO:**

### **🔍 1. Búsqueda Manual de Series:**
1. ✅ Click en "🔍 Search Series Duplicates"
2. ✅ Escribir nombre de serie (parcial o completo)
3. ✅ Ver análisis detallado de duplicados
4. ✅ Click en "🧹 Clean" para serie específica

### **⚙️ 2. Mass Delete con Priorización:**
1. ✅ Click en "⚙️ Mass Delete Options"
2. ✅ Elegir "📺 Episodios Duplicados"
3. ✅ Seleccionar episodios a limpiar
4. ✅ Confirmar con lógica de symlinks explicada
5. ✅ Ejecución con priorización inteligente

### **📊 3. Actualización de Cache:**
1. ✅ Click en "Series Statistics"
2. ✅ Cache se actualiza con datos frescos
3. ✅ Análisis automático con nueva lógica
4. ✅ Recomendaciones basadas en symlinks

---

## 📁 **ARCHIVOS MODIFICADOS:**

### **🔧 Código Principal:** `gui.py`
- **Líneas 808-810:** Nuevo botón "Search Series Duplicates"
- **Líneas 4553-4759:** Ventana de búsqueda manual
- **Líneas 4762-4858:** Limpieza de series específicas
- **Líneas 4860-4878:** Lógica de priorización de symlinks
- **Líneas 4913-4930:** Diálogo de confirmación mejorado
- **Líneas 4955-5030:** Mass delete con lógica de symlinks

### **📋 Documentación:**
- **`LOGICA_PRIORIZACION_SYMLINKS.md`** - Este archivo

---

## 🎯 **ESTADO FINAL:**

**🔗 SYMLINKS COMO MÁXIMA PRIORIDAD IMPLEMENTADO**

**🔍 BÚSQUEDA MANUAL DE SERIES AGREGADA**

**⚡ MASS DELETE CON LÓGICA INTELIGENTE**

**🧹 LIMPIEZA INDIVIDUAL POR SERIE**

**🛡️ PRESERVACIÓN GARANTIZADA DE CONTENIDO**

---

**🎉 SYMLINKS SIEMPRE PROTEGIDOS Y PRIORIZADOS!**

**🔍 BÚSQUEDA GRANULAR PARA CONTROL PRECISO!**

**⚡ LÓGICA INTELIGENTE EN TODAS LAS OPERACIONES!**
