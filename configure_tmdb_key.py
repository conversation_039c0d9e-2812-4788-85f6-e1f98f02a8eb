#!/usr/bin/env python3
"""
Script para configurar la API key de TMDB y probar la funcionalidad
"""

import sys
import os
import json

# Agregar el directorio actual al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tmdb_manager import TMDBManager

def configure_tmdb_key():
    """Configurar y probar la API key de TMDB"""
    
    api_key = "201066b4b17391d478e55247f43eed64"
    
    print("🔧 CONFIGURANDO TMDB API KEY")
    print("=" * 50)
    print(f"🔑 API Key: {api_key[:8]}...{api_key[-8:]}")
    
    try:
        # Crear instancia de TMDBManager
        tmdb = TMDBManager(api_key)
        
        print("✅ TMDBManager creado exitosamente")
        
        # Probar la conexión con una búsqueda simple
        print("\n🧪 PROBANDO CONEXIÓN TMDB...")
        
        # Buscar una serie conocida
        search_results = tmdb.search_tv("Breaking Bad")
        
        if search_results:
            print(f"✅ Conexión exitosa! Encontradas {len(search_results)} series")
            
            # Mostrar primer resultado
            first_result = search_results[0]
            print(f"\n📺 PRIMER RESULTADO:")
            print(f"   Título: {first_result.get('name', 'N/A')}")
            print(f"   TMDB ID: {first_result.get('id', 'N/A')}")
            print(f"   Año: {first_result.get('first_air_date', 'N/A')[:4]}")
            print(f"   Rating: {first_result.get('vote_average', 'N/A')}")
            
            # Probar obtener detalles de la serie
            tmdb_id = first_result.get('id')
            if tmdb_id:
                print(f"\n🔍 OBTENIENDO DETALLES DE SERIE {tmdb_id}...")
                series_details = tmdb.get_tv_details(tmdb_id)
                
                if series_details:
                    print("✅ Detalles obtenidos exitosamente")
                    print(f"   Temporadas: {len(series_details.get('seasons', []))}")
                    print(f"   Géneros: {', '.join([g['name'] for g in series_details.get('genres', [])])}")
                    
                    # Probar obtener episodios
                    print(f"\n📺 OBTENIENDO EPISODIOS...")
                    episodes = tmdb.get_all_tv_episodes(tmdb_id)
                    
                    if episodes:
                        print(f"✅ Episodios obtenidos: {len(episodes)}")
                        
                        # Mostrar ejemplo de episodio
                        if episodes:
                            ep = episodes[0]
                            print(f"\n📺 EJEMPLO DE EPISODIO:")
                            print(f"   Nombre: {ep.get('name', 'N/A')}")
                            print(f"   Temporada: {ep.get('season_number', 'N/A')}")
                            print(f"   Episodio: {ep.get('episode_number', 'N/A')}")
                            print(f"   Duración: {ep.get('runtime', 'N/A')} min")
                            print(f"   Rating: {ep.get('vote_average', 'N/A')}")
                            
                            # Probar URL de imagen
                            still_path = ep.get('still_path')
                            if still_path:
                                episode_image = tmdb.get_image_url(still_path, 'w1280')
                                print(f"   Imagen: {episode_image}")
                            else:
                                print("   Imagen: No disponible")
                    else:
                        print("⚠️ No se pudieron obtener episodios")
                else:
                    print("⚠️ No se pudieron obtener detalles de la serie")
            
        else:
            print("❌ No se encontraron resultados - verificar API key")
            return False
        
        # Guardar configuración para la aplicación
        print(f"\n💾 GUARDANDO CONFIGURACIÓN...")
        
        # Crear archivo de configuración
        config = {
            "tmdb_api_key": api_key,
            "tmdb_configured": True,
            "tmdb_language": "es-MX"
        }
        
        with open("tmdb_config.json", "w") as f:
            json.dump(config, f, indent=2)
        
        print("✅ Configuración guardada en tmdb_config.json")
        
        return True
        
    except Exception as e:
        print(f"❌ Error configurando TMDB: {e}")
        return False

def test_episode_properties_generation():
    """Probar la generación de movie_properties como en el ejemplo"""
    
    print("\n🧪 PROBANDO GENERACIÓN DE MOVIE_PROPERTIES")
    print("=" * 60)
    
    api_key = "201066b4b17391d478e55247f43eed64"
    
    try:
        tmdb = TMDBManager(api_key)
        
        # Usar una serie conocida para probar
        test_tmdb_id = 1396  # Breaking Bad
        
        print(f"📡 Probando con TMDB ID: {test_tmdb_id}")
        
        # Obtener detalles de la serie
        series_details = tmdb.get_tv_details(test_tmdb_id)
        if not series_details:
            print("❌ No se pudieron obtener detalles de la serie")
            return False
        
        series_title = series_details.get('name', 'Unknown Series')
        print(f"✅ Serie: {series_title}")
        
        # Obtener episodios
        episodes = tmdb.get_all_tv_episodes(test_tmdb_id)
        if not episodes:
            print("❌ No se pudieron obtener episodios")
            return False
        
        print(f"✅ Episodios encontrados: {len(episodes)}")
        
        # Generar ejemplo de movie_properties para el primer episodio
        episode = episodes[0]
        
        # Simular la lógica de assign_tmdb_workspace
        series_poster_url = tmdb.get_image_url(series_details.get('poster_path', '')) if series_details.get('poster_path') else ''
        episode_still_path = episode.get('still_path', '')
        episode_image_url = tmdb.get_image_url(episode_still_path, 'w1280') if episode_still_path else ''
        
        runtime_minutes = episode.get('runtime', 0) or 0
        duration_secs = runtime_minutes * 60
        duration_formatted = f"{duration_secs // 3600:02d}:{(duration_secs % 3600) // 60:02d}:{duration_secs % 60:02d}"
        
        release_date = series_details.get('first_air_date', '')
        tmdb_language = series_details.get('original_language', 'en')
        
        # Crear movie_properties como en la función real
        episode_properties = {
            'tmdb_id': test_tmdb_id,
            'episode_tmdb_id': episode.get('id'),
            'release_date': episode.get('air_date', ''),
            'plot': episode.get('overview', ''),
            'duration_secs': duration_secs,
            'duration': duration_formatted,
            'movie_image': series_poster_url,
            'episode_image': episode_image_url,
            'series_image': series_poster_url,
            'rating': episode.get('vote_average', 0),
            'season': str(episode.get('season_number', 1)),
            'episode': str(episode.get('episode_number', 1)),
            'episode_name': episode.get('name', f'Episode {episode.get("episode_number", 1)}'),
            'series_name': series_title,
            'year': release_date[:4] if release_date else '2008',
            'tmdb_language': tmdb_language,
            'air_date': episode.get('air_date', ''),
            'runtime': runtime_minutes,
            'vote_average': episode.get('vote_average', 0),
            'season_number': episode.get('season_number', 1),
            'episode_number': episode.get('episode_number', 1),
            'still_path': episode_still_path
        }
        
        print(f"\n📊 MOVIE_PROPERTIES GENERADO:")
        print("=" * 40)
        print(json.dumps(episode_properties, indent=2))
        
        print(f"\n✅ VERIFICACIÓN:")
        print(f"   📺 Serie: {episode_properties['series_name']}")
        print(f"   📺 Episodio: S{episode_properties['season']}E{episode_properties['episode']} - {episode_properties['episode_name']}")
        print(f"   🖼️ Imagen serie: {'✅' if episode_properties['movie_image'] else '❌'}")
        print(f"   🖼️ Imagen episodio: {'✅' if episode_properties['episode_image'] else '❌'}")
        print(f"   ⏱️ Duración: {episode_properties['duration']} ({episode_properties['duration_secs']}s)")
        print(f"   ⭐ Rating: {episode_properties['rating']}")
        print(f"   📅 Fecha: {episode_properties['air_date']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Función principal"""
    
    print("🚀 CONFIGURACIÓN Y PRUEBA TMDB API KEY")
    print("=" * 70)
    
    # Configurar API key
    success1 = configure_tmdb_key()
    
    if success1:
        # Probar generación de movie_properties
        success2 = test_episode_properties_generation()
        
        print(f"\n🎉 RESUMEN:")
        print("=" * 30)
        print(f"✅ Configuración TMDB: {'EXITOSA' if success1 else 'FALLIDA'}")
        print(f"✅ Generación Properties: {'EXITOSA' if success2 else 'FALLIDA'}")
        
        if success1 and success2:
            print(f"\n🎉 ¡TODO LISTO!")
            print(f"💡 Ahora puedes usar la interfaz para asignar TMDB a series")
            print(f"💡 Los episodios tendrán movie_properties completos como en tu ejemplo")
        else:
            print(f"\n⚠️ Hay problemas que resolver antes de usar TMDB")
    else:
        print(f"\n❌ Error en configuración inicial")

if __name__ == "__main__":
    main()
