#!/usr/bin/env python3
"""
M3U Manager - Gestión de archivos M3U para XUI Database Manager
"""

import re
import logging
from typing import List, Dict, Optional
from urllib.parse import urlparse
import requests

class M3UManager:
    """Gestor de archivos M3U"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'XUI-Database-Manager/1.0'
        })
    
    def parse_m3u_file(self, file_path: str) -> List[Dict]:
        """Parsear archivo M3U local"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return self._parse_m3u_content(content)
        except Exception as e:
            logging.error(f"Error leyendo archivo M3U {file_path}: {e}")
            return []
    
    def parse_m3u_url(self, url: str) -> List[Dict]:
        """Parsear archivo M3U desde URL"""
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return self._parse_m3u_content(response.text)
        except Exception as e:
            logging.error(f"Error descargando M3U desde {url}: {e}")
            return []
    
    def _parse_m3u_content(self, content: str) -> List[Dict]:
        """Parsear contenido M3U"""
        entries = []
        lines = content.strip().split('\n')
        
        current_entry = {}
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('#EXTM3U'):
                continue
            elif line.startswith('#EXTINF:'):
                # Parsear línea EXTINF
                current_entry = self._parse_extinf_line(line)
            elif line.startswith('#EXTGRP:'):
                # Grupo
                current_entry['group'] = line.replace('#EXTGRP:', '').strip()
            elif line.startswith('#EXTVLCOPT:'):
                # Opciones VLC
                if 'vlc_options' not in current_entry:
                    current_entry['vlc_options'] = []
                current_entry['vlc_options'].append(line.replace('#EXTVLCOPT:', '').strip())
            elif line.startswith('http'):
                # URL del stream
                current_entry['url'] = line
                entries.append(current_entry.copy())
                current_entry = {}
        
        return entries
    
    def _parse_extinf_line(self, line: str) -> Dict:
        """Parsear línea EXTINF"""
        entry = {
            'duration': -1,
            'title': '',
            'attributes': {}
        }
        
        # Remover #EXTINF:
        line = line.replace('#EXTINF:', '')
        
        # Buscar duración
        duration_match = re.match(r'^(-?\d+(?:\.\d+)?)', line)
        if duration_match:
            entry['duration'] = float(duration_match.group(1))
            line = line[len(duration_match.group(1)):].strip()
        
        # Buscar atributos entre comas
        if ',' in line:
            parts = line.split(',', 1)
            attributes_part = parts[0].strip()
            title_part = parts[1].strip()
            
            # Parsear atributos
            entry['attributes'] = self._parse_attributes(attributes_part)
            entry['title'] = title_part
        else:
            entry['title'] = line
        
        return entry
    
    def _parse_attributes(self, attr_string: str) -> Dict:
        """Parsear atributos de EXTINF"""
        attributes = {}
        
        # Buscar atributos como key="value" o key=value
        attr_pattern = r'(\w+)=(?:"([^"]*)"|([^\s,]+))'
        matches = re.findall(attr_pattern, attr_string)
        
        for match in matches:
            key = match[0]
            value = match[1] if match[1] else match[2]
            attributes[key] = value
        
        return attributes
    
    def filter_series_entries(self, entries: List[Dict]) -> List[Dict]:
        """Filtrar solo entradas que parecen ser series"""
        series_entries = []
        
        for entry in entries:
            if self._is_series_entry(entry):
                series_entries.append(entry)
        
        return series_entries
    
    def filter_movie_entries(self, entries: List[Dict]) -> List[Dict]:
        """Filtrar solo entradas que parecen ser películas"""
        movie_entries = []
        
        for entry in entries:
            if not self._is_series_entry(entry):
                movie_entries.append(entry)
        
        return movie_entries
    
    def _is_series_entry(self, entry: Dict) -> bool:
        """Detectar si una entrada es una serie"""
        title = entry.get('title', '')
        group = entry.get('group', '')
        
        # Patrones que indican series
        series_patterns = [
            r'S\d+\s*E\d+',  # S01E01 o S01 E01 (con o sin espacio)
            r'Season\s+\d+',  # Season 1
            r'Temporada\s+\d+',  # Temporada 1
            r'\d+x\d+',  # 1x01
            r'Episode\s+\d+',  # Episode 1
            r'Episodio\s+\d+',  # Episodio 1
            r'Cap\.\s*\d+',  # Cap. 1
            r'Capítulo\s+\d+',  # Capítulo 1
        ]
        
        # Grupos que indican series
        series_groups = [
            'series', 'tv shows', 'television', 'tv series',
            'series de tv', 'programas', 'shows'
        ]
        
        # Verificar título
        for pattern in series_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                return True
        
        # Verificar grupo
        for series_group in series_groups:
            if series_group.lower() in group.lower():
                return True
        
        return False
    
    def extract_series_info(self, entry: Dict) -> Dict:
        """Extraer información detallada de serie"""
        title = entry.get('title', '')
        
        info = {
            'original_title': title,
            'series_title': title,
            'season': None,
            'episode': None,
            'episode_title': None,
            'year': None,
            'quality': None,
            'language': None,
            'group': entry.get('group', ''),
            'url': entry.get('url', ''),
            'attributes': entry.get('attributes', {})
        }
        
        # Extraer año
        year_match = re.search(r'\((\d{4})\)', title)
        if year_match:
            info['year'] = int(year_match.group(1))
        
        # Extraer calidad
        quality_patterns = [
            r'4K', r'2160p', r'UHD',
            r'1080p', r'FHD', r'Full HD',
            r'720p', r'HD',
            r'480p', r'SD'
        ]
        for pattern in quality_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                info['quality'] = pattern
                break
        
        # Extraer idioma
        lang_patterns = [
            r'LATINO', r'LAT', r'SPANISH', r'ESP',
            r'ENGLISH', r'ENG', r'SUBTITULADO', r'SUB'
        ]
        for pattern in lang_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                info['language'] = pattern
                break
        
        # Extraer información de temporada y episodio
        info.update(self._extract_episode_info(title))
        
        return info
    
    def _extract_episode_info(self, title: str) -> Dict:
        """Extraer información específica de episodio"""
        info = {
            'series_title': title,
            'season': None,
            'episode': None,
            'episode_title': None
        }
        
        # Patrón S01E01 o S01 E01 (con o sin espacio)
        match = re.search(r'(.+?)\s*S(\d+)\s*E(\d+)(?:\s*-?\s*(.+))?', title, re.IGNORECASE)
        if match:
            info['series_title'] = match.group(1).strip()
            info['season'] = int(match.group(2))
            info['episode'] = int(match.group(3))
            if match.group(4):
                info['episode_title'] = match.group(4).strip()
            return info
        
        # Patrón 1x01
        match = re.search(r'(.+?)\s*(\d+)x(\d+)(?:\s*-?\s*(.+))?', title, re.IGNORECASE)
        if match:
            info['series_title'] = match.group(1).strip()
            info['season'] = int(match.group(2))
            info['episode'] = int(match.group(3))
            if match.group(4):
                info['episode_title'] = match.group(4).strip()
            return info
        
        # Patrón Season/Temporada
        match = re.search(r'(.+?)\s*(?:Season|Temporada)\s+(\d+).*?(?:Episode|Episodio)\s+(\d+)(?:\s*-?\s*(.+))?', title, re.IGNORECASE)
        if match:
            info['series_title'] = match.group(1).strip()
            info['season'] = int(match.group(2))
            info['episode'] = int(match.group(3))
            if match.group(4):
                info['episode_title'] = match.group(4).strip()
            return info
        
        return info
    
    def get_m3u_statistics(self, entries: List[Dict]) -> Dict:
        """Obtener estadísticas del archivo M3U"""
        stats = {
            'total_entries': len(entries),
            'series_entries': 0,
            'movie_entries': 0,
            'groups': set(),
            'qualities': set(),
            'languages': set()
        }
        
        for entry in entries:
            if self._is_series_entry(entry):
                stats['series_entries'] += 1
            else:
                stats['movie_entries'] += 1
            
            # Recopilar grupos
            group = entry.get('group', '')
            if group:
                stats['groups'].add(group)
            
            # Extraer información adicional
            info = self.extract_series_info(entry)
            if info.get('quality'):
                stats['qualities'].add(info['quality'])
            if info.get('language'):
                stats['languages'].add(info['language'])
        
        # Convertir sets a listas para serialización
        stats['groups'] = list(stats['groups'])
        stats['qualities'] = list(stats['qualities'])
        stats['languages'] = list(stats['languages'])
        
        return stats
