#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Smart Database Manager v2
Sistema de gestión de base de datos mejorado con todas las funcionalidades optimizadas
"""

import pymysql
import json
import logging
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime, timedelta
import threading
import time

class SmartDatabaseManager:
    def __init__(self):
        self.connection = None
        self.connection_config = None
        self.is_connected = False
        self._connection_lock = threading.Lock()
        
        # Configurar logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connect(self, host: str, user: str, password: str, database: str, port: int = 3306) -> bool:
        """Conectar a la base de datos XUI con reintentos automáticos"""
        try:
            with self._connection_lock:
                self.connection_config = {
                    'host': host,
                    'user': user, 
                    'password': password,
                    'database': database,
                    'port': port,
                    'charset': 'utf8mb4',
                    'autocommit': False,
                    'connect_timeout': 10,
                    'read_timeout': 30,
                    'write_timeout': 30
                }
                
                self.connection = pymysql.connect(**self.connection_config)
                
                if self.connection.open:
                    self.is_connected = True
                    self.logger.info(f"✅ Conectado a {host}:{port}/{database}")
                    return True
                    
        except Exception as e:
            self.logger.error(f"❌ Error conectando: {e}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """Desconectar de la base de datos"""
        try:
            with self._connection_lock:
                if self.connection and self.connection.open:
                    self.connection.close()
                    self.logger.info("🔌 Desconectado de la base de datos")
                
                self.connection = None
                self.is_connected = False
                
        except Exception as e:
            self.logger.error(f"❌ Error al desconectar: {e}")
    
    def ensure_connection(self) -> bool:
        """Asegurar que la conexión esté activa"""
        try:
            if not self.connection or not self.connection.open:
                if self.connection_config:
                    return self.connect(**self.connection_config)
                return False
            
            # Ping para verificar conexión
            self.connection.ping(reconnect=True)
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Problema de conexión: {e}")
            if self.connection_config:
                return self.connect(**self.connection_config)
            return False
    
    def execute_query(self, query: str, params: Optional[Tuple] = None) -> List[Dict]:
        """Ejecutar consulta SELECT con manejo de errores"""
        if not self.ensure_connection():
            return []
        
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(query, params)
                return cursor.fetchall()
                
        except Exception as e:
            self.logger.error(f"❌ Error en consulta: {e}")
            return []
    
    def execute_update(self, query: str, params: Optional[Tuple] = None) -> Tuple[bool, int]:
        """Ejecutar consulta UPDATE/INSERT/DELETE"""
        if not self.ensure_connection():
            return False, 0
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, params)
                affected_rows = cursor.rowcount
                self.connection.commit()
                return True, affected_rows
                
        except Exception as e:
            self.logger.error(f"❌ Error en actualización: {e}")
            self.connection.rollback()
            return False, 0
    
    def execute_batch_update(self, query: str, params_list: List[Tuple]) -> Tuple[bool, int]:
        """Ejecutar múltiples actualizaciones en batch"""
        if not self.ensure_connection():
            return False, 0
        
        try:
            with self.connection.cursor() as cursor:
                cursor.executemany(query, params_list)
                affected_rows = cursor.rowcount
                self.connection.commit()
                return True, affected_rows
                
        except Exception as e:
            self.logger.error(f"❌ Error en batch update: {e}")
            self.connection.rollback()
            return False, 0
    
    # Métodos específicos para métricas y análisis
    
    def get_database_metrics(self) -> Dict[str, Any]:
        """Obtener métricas completas de la base de datos"""
        metrics = {
            'movies': 0,
            'series': 0, 
            'episodes': 0,
            'with_icons': 0,
            'compatible_movies': 0,
            'compatible_episodes': 0,
            'problematic_timestamps': 0,
            'missing_containers': 0,
            'connection_status': self.is_connected
        }
        
        try:
            # Total películas
            result = self.execute_query("SELECT COUNT(*) as count FROM streams WHERE type = 2")
            if result:
                metrics['movies'] = result[0]['count']
            
            # Total series
            result = self.execute_query("SELECT COUNT(*) as count FROM streams_series")
            if result:
                metrics['series'] = result[0]['count']
            
            # Total episodios
            result = self.execute_query("SELECT COUNT(*) as count FROM streams WHERE type = 5")
            if result:
                metrics['episodes'] = result[0]['count']
            
            # Con íconos
            result = self.execute_query("SELECT COUNT(*) as count FROM streams WHERE stream_icon IS NOT NULL AND stream_icon != ''")
            if result:
                metrics['with_icons'] = result[0]['count']
            
            # Películas compatibles (patrón optimizado)
            result = self.execute_query("""
                SELECT COUNT(*) as count FROM streams 
                WHERE type = 2 
                AND target_container IS NOT NULL 
                AND `order` > 0 
                AND added < 2147483647
            """)
            if result:
                metrics['compatible_movies'] = result[0]['count']
            
            # Episodios compatibles
            result = self.execute_query("""
                SELECT COUNT(*) as count FROM streams 
                WHERE type = 5 
                AND added < 2147483647
            """)
            if result:
                metrics['compatible_episodes'] = result[0]['count']
            
            # Timestamps problemáticos
            result = self.execute_query("SELECT COUNT(*) as count FROM streams WHERE added >= 2147483647")
            if result:
                metrics['problematic_timestamps'] = result[0]['count']
            
            # Containers faltantes en películas
            result = self.execute_query("SELECT COUNT(*) as count FROM streams WHERE type = 2 AND target_container IS NULL")
            if result:
                metrics['missing_containers'] = result[0]['count']
                
        except Exception as e:
            self.logger.error(f"❌ Error obteniendo métricas: {e}")
        
        return metrics
    
    def get_series_with_episodes(self, limit: int = 100) -> List[Dict]:
        """Obtener series con información de episodios"""
        query = """
        SELECT 
            ss.id as series_id,
            ss.title as series_title,
            ss.tmdb_id,
            ss.year,
            ss.rating,
            COUNT(se.id) as episode_count,
            MIN(s.added) as first_added,
            MAX(s.added) as last_added
        FROM streams_series ss
        LEFT JOIN streams_episodes se ON ss.id = se.series_id
        LEFT JOIN streams s ON se.stream_id = s.id
        GROUP BY ss.id, ss.title, ss.tmdb_id, ss.year, ss.rating
        ORDER BY ss.title
        LIMIT %s
        """
        return self.execute_query(query, (limit,))
    
    def get_movies_analysis(self, limit: int = 100) -> List[Dict]:
        """Obtener análisis de películas"""
        query = """
        SELECT 
            id,
            stream_display_name as title,
            target_container,
            `order`,
            stream_icon IS NOT NULL as has_icon,
            FROM_UNIXTIME(added) as added_date,
            CASE 
                WHEN target_container IS NOT NULL AND `order` > 0 AND added < 2147483647 
                THEN 'Compatible'
                ELSE 'Problemática'
            END as status
        FROM streams 
        WHERE type = 2
        ORDER BY added DESC
        LIMIT %s
        """
        return self.execute_query(query, (limit,))
    
    def get_problematic_content(self) -> Dict[str, List[Dict]]:
        """Identificar contenido con problemas"""
        problems = {
            'bad_timestamps': [],
            'missing_containers': [],
            'zero_order': [],
            'missing_icons': []
        }
        
        # Timestamps problemáticos
        problems['bad_timestamps'] = self.execute_query("""
            SELECT id, stream_display_name, type, added 
            FROM streams 
            WHERE added >= 2147483647 
            ORDER BY type, id 
            LIMIT 50
        """)
        
        # Containers faltantes en películas
        problems['missing_containers'] = self.execute_query("""
            SELECT id, stream_display_name, target_container, `order`
            FROM streams 
            WHERE type = 2 AND target_container IS NULL 
            ORDER BY id 
            LIMIT 50
        """)
        
        # Order = 0 en películas
        problems['zero_order'] = self.execute_query("""
            SELECT id, stream_display_name, target_container, `order`
            FROM streams 
            WHERE type = 2 AND `order` = 0 
            ORDER BY id 
            LIMIT 50
        """)
        
        # Sin íconos
        problems['missing_icons'] = self.execute_query("""
            SELECT id, stream_display_name, type, stream_icon
            FROM streams 
            WHERE (stream_icon IS NULL OR stream_icon = '') 
            ORDER BY type, id 
            LIMIT 50
        """)
        
        return problems
    
    # Métodos de corrección masiva
    
    def fix_movie_timestamps_to_realistic(self) -> Tuple[bool, int]:
        """Corregir timestamps de películas a fechas realistas"""
        # Timestamp para 14 de julio 2025
        target_timestamp = 1752456887  # 2025-07-14 01:34:47 UTC
        
        query = """
        UPDATE streams 
        SET added = %s 
        WHERE type = 2 AND added >= 2147483647
        """
        
        return self.execute_update(query, (target_timestamp,))
    
    def fix_episode_timestamps_to_realistic(self) -> Tuple[bool, int]:
        """Corregir timestamps de episodios a fechas realistas"""
        # Timestamp para 14 de julio 2025
        target_timestamp = 1752456887  # 2025-07-14 01:34:47 UTC
        
        query = """
        UPDATE streams 
        SET added = %s 
        WHERE type = 5 AND added >= 2147483647
        """
        
        return self.execute_update(query, (target_timestamp,))
    
    def fix_movie_containers(self) -> Tuple[bool, int]:
        """Establecer target_container = 'mkv' en películas donde sea NULL"""
        query = """
        UPDATE streams 
        SET target_container = 'mkv' 
        WHERE type = 2 AND target_container IS NULL
        """
        
        return self.execute_update(query)
    
    def fix_movie_order_values(self) -> Tuple[bool, int]:
        """Establecer order = 1 en películas donde sea 0"""
        query = """
        UPDATE streams 
        SET `order` = 1 
        WHERE type = 2 AND `order` = 0
        """
        
        return self.execute_update(query)
    
    def auto_assign_icons_from_json(self) -> Tuple[bool, int]:
        """Asignar íconos automáticamente desde movie_properties JSON"""
        query = """
        UPDATE streams 
        SET stream_icon = CASE
            WHEN JSON_VALID(movie_properties) AND JSON_EXTRACT(movie_properties, '$.movie_image') IS NOT NULL 
            THEN JSON_UNQUOTE(JSON_EXTRACT(movie_properties, '$.movie_image'))
            WHEN JSON_VALID(movie_properties) AND JSON_EXTRACT(movie_properties, '$.movie_poster') IS NOT NULL 
            THEN JSON_UNQUOTE(JSON_EXTRACT(movie_properties, '$.movie_poster'))
            WHEN JSON_VALID(movie_properties) AND JSON_EXTRACT(movie_properties, '$.poster') IS NOT NULL 
            THEN JSON_UNQUOTE(JSON_EXTRACT(movie_properties, '$.poster'))
            ELSE stream_icon
        END
        WHERE (stream_icon IS NULL OR stream_icon = '') 
        AND movie_properties IS NOT NULL 
        AND JSON_VALID(movie_properties)
        """
        
        return self.execute_update(query)
    
    def dr_house_specific_fix(self) -> Dict[str, Any]:
        """Corrección específica para problemas de Dr. House"""
        results = {}
        
        # 1. Encontrar la serie Dr. House
        series_query = """
        SELECT id, title FROM streams_series 
        WHERE title LIKE '%House%' OR title LIKE '%Dr%House%'
        ORDER BY title
        """
        dr_house_series = self.execute_query(series_query)
        results['series_found'] = dr_house_series
        
        if not dr_house_series:
            return results
        
        series_id = dr_house_series[0]['id']
        
        # 2. Obtener episodios problemáticos
        episodes_query = """
        SELECT s.id, s.stream_display_name, s.read_native, s.target_container, s.added
        FROM streams s
        JOIN streams_episodes se ON s.id = se.stream_id
        WHERE se.series_id = %s AND s.type = 5
        """
        episodes = self.execute_query(episodes_query, (series_id,))
        results['total_episodes'] = len(episodes)
        
        # 3. Corregir timestamps si es necesario
        if episodes:
            fix_timestamps_query = """
            UPDATE streams s
            JOIN streams_episodes se ON s.id = se.stream_id
            SET s.added = 1752456887
            WHERE se.series_id = %s AND s.type = 5 AND s.added >= 2147483647
            """
            success, affected = self.execute_update(fix_timestamps_query, (series_id,))
            results['timestamps_fixed'] = affected
        
        # 4. Asegurar read_native correcto
        read_native_query = """
        UPDATE streams s
        JOIN streams_episodes se ON s.id = se.stream_id
        SET s.read_native = '0'
        WHERE se.series_id = %s AND s.type = 5 AND s.read_native = '1'
        """
        success, affected = self.execute_update(read_native_query, (series_id,))
        results['read_native_fixed'] = affected
        
        return results
    
    def get_compatibility_report(self) -> Dict[str, Any]:
        """Generar reporte completo de compatibilidad"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'metrics': self.get_database_metrics(),
            'problems': self.get_problematic_content(),
            'recommendations': []
        }
        
        # Generar recomendaciones basadas en problemas encontrados
        metrics = report['metrics']
        
        if metrics['problematic_timestamps'] > 0:
            report['recommendations'].append({
                'issue': 'Timestamps problemáticos',
                'count': metrics['problematic_timestamps'],
                'action': 'Ejecutar corrección de timestamps',
                'priority': 'HIGH'
            })
        
        if metrics['missing_containers'] > 0:
            report['recommendations'].append({
                'issue': 'Containers faltantes',
                'count': metrics['missing_containers'], 
                'action': 'Establecer target_container = mkv',
                'priority': 'HIGH'
            })
        
        total_content = metrics['movies'] + metrics['episodes']
        if total_content > 0:
            icon_percentage = (metrics['with_icons'] / total_content) * 100
            if icon_percentage < 50:
                report['recommendations'].append({
                    'issue': 'Pocos íconos asignados',
                    'count': total_content - metrics['with_icons'],
                    'action': 'Ejecutar asignación masiva de íconos',
                    'priority': 'MEDIUM'
                })
        
        return report
    
    def get_all_series_with_stats(self) -> List[Dict]:
        """Obtener todas las series con estadísticas"""
        try:
            query = """
            SELECT s.id, s.title, s.total_episodes, s.category, s.has_tmdb,
                   COUNT(e.id) as episode_count
            FROM series s
            LEFT JOIN episodes e ON s.id = e.series_id
            GROUP BY s.id
            ORDER BY s.title
            """
            return self.execute_query(query)
        except Exception as e:
            self.logger.error(f"Error obteniendo series con estadísticas: {e}")
            return []
    
    def get_unprocessed_series(self) -> List[Dict]:
        """Obtener series sin procesar"""
        try:
            query = """
            SELECT id, title, total_episodes, category
            FROM series
            WHERE processed = 0 OR processed IS NULL
            ORDER BY title
            """
            return self.execute_query(query)
        except Exception as e:
            self.logger.error(f"Error obteniendo series sin procesar: {e}")
            return []
    
    def get_all_movies_with_stats(self) -> List[Dict]:
        """Obtener todas las películas con estadísticas"""
        try:
            query = """
            SELECT id, title, quality, duration, category, has_tmdb,
                   CASE WHEN has_tmdb = 1 THEN 'Con TMDB' ELSE 'Sin TMDB' END as tmdb_status
            FROM movies
            ORDER BY title
            """
            return self.execute_query(query)
        except Exception as e:
            self.logger.error(f"Error obteniendo películas con estadísticas: {e}")
            return []
    
    def get_series_without_tmdb(self) -> List[Dict]:
        """Obtener series sin TMDB"""
        try:
            query = """
            SELECT id, title, total_episodes, category
            FROM series
            WHERE has_tmdb = 0 OR has_tmdb IS NULL
            ORDER BY title
            """
            return self.execute_query(query)
        except Exception as e:
            self.logger.error(f"Error obteniendo series sin TMDB: {e}")
            return []
    
    def analyze_series_database(self) -> Dict[str, Any]:
        """Analizar base de datos de series"""
        try:
            analysis = {}
            
            # Total series
            total_query = "SELECT COUNT(*) as total FROM series"
            total_result = self.execute_query(total_query)
            analysis['total_series'] = total_result[0]['total'] if total_result else 0
            
            # Series con TMDB
            tmdb_query = "SELECT COUNT(*) as with_tmdb FROM series WHERE has_tmdb = 1"
            tmdb_result = self.execute_query(tmdb_query)
            analysis['with_tmdb'] = tmdb_result[0]['with_tmdb'] if tmdb_result else 0
            
            # Series sin procesar
            unprocessed_query = "SELECT COUNT(*) as unprocessed FROM series WHERE processed = 0 OR processed IS NULL"
            unprocessed_result = self.execute_query(unprocessed_query)
            analysis['unprocessed'] = unprocessed_result[0]['unprocessed'] if unprocessed_result else 0
            
            return analysis
        except Exception as e:
            self.logger.error(f"Error analizando series: {e}")
            return {}
    
    def find_duplicate_episodes(self) -> List[Dict]:
        """Encontrar episodios duplicados"""
        try:
            query = """
            SELECT series_id, season_num, episode_num, COUNT(*) as duplicates
            FROM episodes
            GROUP BY series_id, season_num, episode_num
            HAVING COUNT(*) > 1
            """
            return self.execute_query(query)
        except Exception as e:
            self.logger.error(f"Error encontrando episodios duplicados: {e}")
            return []
    
    def remove_duplicate_episodes(self) -> int:
        """Remover episodios duplicados"""
        try:
            # Query para mantener solo el ID más bajo de cada grupo de duplicados
            query = """
            DELETE e1 FROM episodes e1
            INNER JOIN episodes e2 
            WHERE e1.id > e2.id 
            AND e1.series_id = e2.series_id 
            AND e1.season_num = e2.season_num 
            AND e1.episode_num = e2.episode_num
            """
            success, count = self.execute_update(query)
            return count if success else 0
        except Exception as e:
            self.logger.error(f"Error removiendo episodios duplicados: {e}")
            return 0
    
    def generate_series_report(self) -> Dict[str, Any]:
        """Generar reporte de series"""
        try:
            report = {}
            
            # Métricas básicas
            report['metrics'] = self.analyze_series_database()
            
            # Series problemáticas
            report['duplicates'] = len(self.find_duplicate_episodes())
            report['without_tmdb'] = len(self.get_series_without_tmdb())
            report['unprocessed'] = len(self.get_unprocessed_series())
            
            return report
        except Exception as e:
            self.logger.error(f"Error generando reporte de series: {e}")
            return {}
    
    def get_episodes_by_series(self, series_id: int) -> List[Dict]:
        """Obtener episodios por serie"""
        try:
            query = """
            SELECT id, title, season_num, episode_num, duration
            FROM episodes
            WHERE series_id = %s
            ORDER BY season_num, episode_num
            """
            return self.execute_query(query, (series_id,))
        except Exception as e:
            self.logger.error(f"Error obteniendo episodios de serie {series_id}: {e}")
            return []
    
    def process_series(self, series_id: int) -> bool:
        """Procesar serie"""
        try:
            query = "UPDATE series SET processed = 1 WHERE id = %s"
            success, _ = self.execute_update(query, (series_id,))
            return success
        except Exception as e:
            self.logger.error(f"Error procesando serie {series_id}: {e}")
            return False
    
    def assign_tmdb_to_series(self, series_id: int, tmdb_id: int) -> bool:
        """Asignar TMDB a serie"""
        try:
            query = "UPDATE series SET has_tmdb = 1, tmdb_id = %s WHERE id = %s"
            success, _ = self.execute_update(query, (tmdb_id, series_id))
            return success
        except Exception as e:
            self.logger.error(f"Error asignando TMDB a serie {series_id}: {e}")
            return False
    
    def delete_series(self, series_id: int) -> bool:
        """Eliminar serie"""
        try:
            # Primero eliminar episodios
            episodes_query = "DELETE FROM episodes WHERE series_id = %s"
            self.execute_update(episodes_query, (series_id,))
            
            # Luego eliminar serie
            series_query = "DELETE FROM series WHERE id = %s"
            success, _ = self.execute_update(series_query, (series_id,))
            return success
        except Exception as e:
            self.logger.error(f"Error eliminando serie {series_id}: {e}")
            return False
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()
