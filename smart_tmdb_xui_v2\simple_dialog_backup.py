#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Modern Connection Dialog - Clean & Functional
Sin threading complejo, diseño moderno y limpio
"""

import tkinter as tk
from tkinter import ttk, messagebox

class SimpleConnectionDialog:
    def __init__(self, parent=None, config_manager=None, callback=None):
        self.parent = parent
        self.config_manager = config_manager
        self.callback = callback
        self.result = None
                               font=('Consolas', 9, 'bold'),
                               bg=self.colors['nvidia_green'],
                               fg='black',
                               activebackground=self.colors['nvidia_dark'],
                               activeforeground='white',
                               relief='flat',
                               bd=0,
                               padx=15,
                               pady=10,
                               cursor='hand2')
        connect_btn.pack(side='left', padx=4)
        self._add_button_effects(connect_btn, self.colors['nvidia_green'])r=None, callback=None):
        self.parent = parent
        self.config_manager = config_manager
        self.callback = callback
        self.result = None
        
        # Gaming Epic Color Scheme - Inspirado en NVIDIA/ROG/Gemini
        self.colors = {
            'bg_primary': '#0A0A0A',      # Negro más profundo
            'bg_secondary': '#161616',    # Superficie muy oscura
            'bg_card': '#1F1F1F',         # Tarjetas
            'bg_input': '#2A2A2A',        # Input background más claro
            'bg_hover': '#333333',        # Hover state
            'nvidia_green': '#76B900',    # NVIDIA Green principal
            'nvidia_dark': '#5A8A00',     # NVIDIA Green oscuro
            'rog_red': '#FF0040',         # ROG Red
            'rog_dark': '#CC0033',        # ROG Red oscuro
            'gemini_purple': '#8E44AD',   # Gemini Purple
            'gemini_dark': '#6C3483',     # Gemini Purple oscuro
            'accent_blue': '#00D4FF',     # Cyan brillante
            'accent_orange': '#FF6B35',   # Naranja gaming
            'text_primary': '#FFFFFF',    # Blanco puro
            'text_secondary': '#CCCCCC',  # Gris claro
            'text_muted': '#888888',      # Gris medio
            'border': '#444444',          # Border sutil
            'border_active': '#76B900',   # Border activo
            'success': '#00FF88',         # Verde éxito
            'warning': '#FFD700',         # Amarillo advertencia
            'error': '#FF4444'            # Rojo error
        }
        
        self.setup_window()
        self.setup_ui()
        
    def setup_window(self):
        """Configurar ventana principal con diseño gaming épico"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("🎮 XUI Database Connection Terminal")
        self.window.geometry("540x580")
        self.window.configure(bg=self.colors['bg_primary'])
        self.window.resizable(False, False)

        # Configurar icono y estilo de ventana
        try:
            # Intentar configurar icono si está disponible
            self.window.iconbitmap(default='')
        except:
            pass

        # Hacer modal si hay parent
        if self.parent:
            self.window.transient(self.parent)
            self.window.grab_set()
            # Centrar respecto al parent
            self.center_window()

        # Configurar protocolo de cierre
        self.window.protocol("WM_DELETE_WINDOW", self.cancel)
    
    def center_window(self):
        """Centrar ventana respecto al parent o pantalla"""
        if self.parent:
            # Centrar respecto al parent
            parent_x = self.parent.winfo_rootx()
            parent_y = self.parent.winfo_rooty()
            parent_width = self.parent.winfo_width()
            parent_height = self.parent.winfo_height()

            x = parent_x + (parent_width - 540) // 2
            y = parent_y + (parent_height - 580) // 2

            self.window.geometry(f"540x580+{x}+{y}")
        else:
            # Centrar en pantalla
            self.window.update_idletasks()
            screen_width = self.window.winfo_screenwidth()
            screen_height = self.window.winfo_screenheight()

            x = (screen_width - 540) // 2
            y = (screen_height - 580) // 2

            self.window.geometry(f"540x580+{x}+{y}")
    
    def setup_ui(self):
        """Configurar interfaz de usuario"""
        # Header
        self.create_header()
        
        # Connection Form
        self.create_connection_form()
        
        # Action Buttons
        self.create_action_buttons()
        
        # Status Bar
        self.create_status_bar()
    
    def create_header(self):
        """Crear header gaming épico con gradiente visual"""
        # Header principal con efecto de gradiente simulado
        header_frame = tk.Frame(self.window, bg=self.colors['bg_primary'], height=100)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)

        # Frame superior con acento NVIDIA
        accent_frame = tk.Frame(header_frame, bg=self.colors['nvidia_green'], height=4)
        accent_frame.pack(fill='x')

        # Frame principal del header
        main_header = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        main_header.pack(fill='both', expand=True, padx=2, pady=2)

        # Título principal con efecto gaming
        title_frame = tk.Frame(main_header, bg=self.colors['bg_secondary'])
        title_frame.pack(expand=True, fill='both')

        # Icono y título
        title_container = tk.Frame(title_frame, bg=self.colors['bg_secondary'])
        title_container.pack(expand=True)

        title = tk.Label(title_container,
                        text="🎮 XUI DATABASE TERMINAL",
                        font=('Consolas', 16, 'bold'),
                        bg=self.colors['bg_secondary'],
                        fg=self.colors['nvidia_green'])
        title.pack(pady=(15, 2))

        # Subtítulo con efecto cyber
        subtitle = tk.Label(title_container,
                           text="▶ SECURE CONNECTION PROTOCOL",
                           font=('Consolas', 9, 'bold'),
                           bg=self.colors['bg_secondary'],
                           fg=self.colors['accent_blue'])
        subtitle.pack()

        # Status indicator
        status_indicator = tk.Label(title_container,
                                   text="● READY FOR CONNECTION",
                                   font=('Consolas', 8),
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['warning'])
        status_indicator.pack(pady=(5, 10))
    
    def create_connection_form(self):
        """Crear formulario de conexión con diseño gaming"""
        # Frame principal del formulario con borde
        form_outer = tk.Frame(self.window, bg=self.colors['border'], height=2)
        form_outer.pack(fill='both', expand=True, padx=15, pady=(5, 0))

        form_frame = tk.Frame(form_outer, bg=self.colors['bg_card'])
        form_frame.pack(fill='both', expand=True, padx=2, pady=2)

        # Header del formulario con estilo cyber
        form_header = tk.Frame(form_frame, bg=self.colors['bg_secondary'], height=40)
        form_header.pack(fill='x', padx=5, pady=5)
        form_header.pack_propagate(False)

        form_title = tk.Label(form_header,
                             text="⚡ CONNECTION PARAMETERS",
                             font=('Consolas', 11, 'bold'),
                             bg=self.colors['bg_secondary'],
                             fg=self.colors['rog_red'])
        form_title.pack(pady=10)

        # Frame para los campos con scroll si es necesario
        fields_frame = tk.Frame(form_frame, bg=self.colors['bg_card'])
        fields_frame.pack(padx=15, pady=(0, 10), fill='both', expand=True)

        # Definir campos con iconos gaming
        fields = [
            ("🌐 HOST", "**************", "Server address"),
            ("👤 USER", "infest84", "Database username"),
            ("🔐 PASS", "v5XRWkgns4VBcYnxcJmlahbmGg5azpTS7FCVEuEDkk93BG7zFr", "Authentication key"),
            ("💾 DB", "xui", "Database name"),
            ("🔌 PORT", "3306", "Connection port")
        ]

        self.entries = {}

        for label, default, tooltip in fields:
            # Frame para cada campo con borde sutil
            field_container = tk.Frame(fields_frame, bg=self.colors['border'])
            field_container.pack(fill='x', pady=4)

            field_frame = tk.Frame(field_container, bg=self.colors['bg_secondary'])
            field_frame.pack(fill='x', padx=1, pady=1)

            # Label con estilo gaming
            label_frame = tk.Frame(field_frame, bg=self.colors['bg_secondary'])
            label_frame.pack(fill='x', padx=10, pady=(8, 2))

            lbl = tk.Label(label_frame,
                          text=label,
                          font=('Consolas', 9, 'bold'),
                          bg=self.colors['bg_secondary'],
                          fg=self.colors['nvidia_green'],
                          anchor='w')
            lbl.pack(side='left')

            # Entry con diseño gaming épico
            entry_frame = tk.Frame(field_frame, bg=self.colors['bg_input'])
            entry_frame.pack(fill='x', padx=10, pady=(0, 8))

            entry = tk.Entry(entry_frame,
                           font=('Consolas', 10),
                           bg=self.colors['bg_input'],
                           fg=self.colors['text_primary'],
                           insertbackground=self.colors['nvidia_green'],
                           relief='flat',
                           bd=0,
                           highlightthickness=1,
                           highlightcolor=self.colors['border_active'],
                           highlightbackground=self.colors['border'])

            if "PASS" in label:
                entry.config(show='●')

            entry.pack(fill='x', ipady=8, padx=5, pady=3)
            entry.insert(0, default)

            # Mapear keys para compatibilidad
            key_map = {
                'host': 'host',
                'user': 'username',
                'pass': 'password',
                'db': 'database',
                'port': 'port'
            }

            raw_key = label.lower().replace("🌐 ", "").replace("👤 ", "").replace("🔐 ", "").replace("💾 ", "").replace("🔌 ", "").strip()
            mapped_key = key_map.get(raw_key, raw_key)
            self.entries[mapped_key] = entry

            # Agregar efecto hover
            self._add_entry_effects(entry)

    def _add_entry_effects(self, entry):
        """Agregar efectos visuales a los campos de entrada"""
        def on_focus_in(event):
            entry.config(highlightbackground=self.colors['border_active'])

        def on_focus_out(event):
            entry.config(highlightbackground=self.colors['border'])

        entry.bind("<FocusIn>", on_focus_in)
        entry.bind("<FocusOut>", on_focus_out)

    def create_action_buttons(self):
        """Crear botones de acción con diseño gaming épico"""
        # Frame principal para botones con borde
        button_outer = tk.Frame(self.window, bg=self.colors['border'])
        button_outer.pack(fill='x', padx=15, pady=10)

        button_frame = tk.Frame(button_outer, bg=self.colors['bg_secondary'], height=90)
        button_frame.pack(fill='x', padx=2, pady=2)
        button_frame.pack_propagate(False)

        # Frame centrado para botones
        btn_container = tk.Frame(button_frame, bg=self.colors['bg_secondary'])
        btn_container.pack(expand=True)

        # Test Button - Estilo NVIDIA
        test_btn = tk.Button(btn_container,
                            text="⚡ TEST CONNECTION",
                            command=self.test_connection,
                            font=('Consolas', 9, 'bold'),
                            bg=self.colors['accent_orange'],
                            fg='black',
                            activebackground=self.colors['warning'],
                            activeforeground='black',
                            relief='flat',
                            bd=0,
                            padx=15,
                            pady=10,
                            cursor='hand2')
        test_btn.pack(side='left', padx=(0, 8))
        self._add_button_effects(test_btn, self.colors['accent_orange'])

        # Connect Button - Estilo ROG
        connect_btn = tk.Button(btn_container,
                               text="� CONNECT NOW",
                               command=self.connect,
                               font=('Consolas', 9, 'bold'),
                               bg=self.colors['nvidia_green'],
                               fg='black',
                               activebackground=self.colors['nvidia_dark'],
                               activeforeground='white',
                               relief='flat',
                               bd=0,
                               padx=15,
                               pady=10,
                               cursor='hand2')
        connect_btn.pack(side='left', padx=4)
        self._add_button_effects(connect_btn, self.colors['nvidia_green'])

        # Cancel Button - Estilo Gemini
        cancel_btn = tk.Button(btn_container,
                              text="❌ CANCEL",
                              command=self.cancel,
                              font=('Consolas', 9, 'bold'),
                              bg=self.colors['rog_red'],
                              fg='white',
                              activebackground=self.colors['rog_dark'],
                              activeforeground='white',
                              relief='flat',
                              bd=0,
                              padx=15,
                              pady=10,
                              cursor='hand2')
        cancel_btn.pack(side='left', padx=(8, 0))
        self._add_button_effects(cancel_btn, self.colors['rog_red'])

    def _add_button_effects(self, button, base_color):
        """Agregar efectos hover a botones"""
        def on_enter(event):
            button.config(bg=self.colors['bg_hover'])

        def on_leave(event):
            button.config(bg=base_color)

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
    
    def create_status_bar(self):
        """Crear barra de estado gaming"""
        # Barra de estado con acento inferior
        status_outer = tk.Frame(self.window, bg=self.colors['border'])
        status_outer.pack(fill='x', side='bottom', padx=15, pady=(0, 15))

        status_frame = tk.Frame(status_outer, bg=self.colors['bg_secondary'], height=45)
        status_frame.pack(fill='x', padx=2, pady=2)
        status_frame.pack_propagate(False)

        # Indicador de estado con estilo terminal
        status_container = tk.Frame(status_frame, bg=self.colors['bg_secondary'])
        status_container.pack(expand=True)

        self.status_label = tk.Label(status_container,
                                   text="▶ SYSTEM READY - AWAITING CONNECTION PARAMETERS",
                                   font=('Consolas', 8, 'bold'),
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['accent_blue'])
        self.status_label.pack(pady=12)

        # Acento inferior NVIDIA
        accent_bottom = tk.Frame(self.window, bg=self.colors['nvidia_green'], height=3)
        accent_bottom.pack(fill='x', side='bottom')
    
    def update_status(self, message, color=None):
        """Actualizar mensaje de estado con estilo gaming"""
        if color is None:
            color = self.colors['accent_blue']

        # Formatear mensaje con estilo terminal
        formatted_message = f"▶ {message.upper()}"
        self.status_label.config(text=formatted_message, fg=color)
        self.window.update()
    
    def test_connection(self):
        """Probar conexión con estilo gaming"""
        self.update_status("initializing connection test...", self.colors['warning'])

        # Simular delay sin bloquear UI
        self.window.after(1000, self._test_connection_result)
    
    def _test_connection_result(self):
        """Resultado del test de conexión con estilo gaming"""
        # Validar campos
        if not all(entry.get().strip() for entry in self.entries.values()):
            messagebox.showerror("🚨 VALIDATION ERROR",
                               "⚠️ MISSING REQUIRED FIELDS\n\n"
                               "Please fill all connection parameters before testing.")
            self.update_status("test failed - missing fields", self.colors['error'])
            return

        # Simular test exitoso con estilo gaming
        messagebox.showinfo("✅ CONNECTION TEST SUCCESSFUL",
                           "🚀 CONNECTION PARAMETERS VALIDATED!\n\n"
                           "▶ Host server is reachable\n"
                           "▶ Authentication credentials verified\n"
                           "▶ Database access confirmed\n"
                           "▶ Network latency optimal\n\n"
                           "🎮 READY FOR FULL CONNECTION!")

        self.update_status("test successful - ready for connection", self.colors['success'])
    
    def connect(self):
        """Conectar a la base de datos con estilo gaming"""
        self.update_status("establishing secure connection...", self.colors['nvidia_green'])

        # Obtener datos
        data = {}
        for key, entry in self.entries.items():
            data[key] = entry.get().strip()

        # Validar campos requeridos
        if not all(data.values()):
            messagebox.showerror("🚨 VALIDATION ERROR",
                               "⚠️ MISSING CONNECTION PARAMETERS\n\n"
                               "Please fill all required fields before connecting.")
            self.update_status("connection failed - missing parameters", self.colors['error'])
            return

        # Convertir puerto a int
        try:
            data['port'] = int(data['port'])
        except ValueError:
            messagebox.showerror("🚨 INVALID PORT",
                               "⚠️ PORT CONFIGURATION ERROR\n\n"
                               "Port must be a valid numeric value.")
            self.update_status("connection failed - invalid port", self.colors['error'])
            return

        self.result = data

        # Ejecutar callback si existe
        if self.callback:
            try:
                self.callback(data)
            except Exception as e:
                messagebox.showerror("🚨 CONNECTION ERROR",
                                   f"❌ DATABASE CONNECTION FAILED\n\n"
                                   f"Error details:\n{str(e)}\n\n"
                                   f"Please verify your connection parameters.")
                self.update_status("connection failed - see error details", self.colors['error'])
                return

        self.update_status("connection established successfully!", self.colors['success'])
        self.window.after(800, self.window.destroy)
    
    def cancel(self):
        """Cancelar conexión"""
        self.result = None
        self.window.destroy()
    
    def show(self):
        """Mostrar diálogo y retornar resultado"""
        self.window.mainloop()
        return self.result

# Función de compatibilidad
def show_simple_connection_dialog(parent=None, config_manager=None, callback=None):
    """Función de compatibilidad para mostrar diálogo"""
    dialog = SimpleConnectionDialog(parent, config_manager, callback)
    return dialog.show()

if __name__ == "__main__":
    print("🔗 Modern Simple Connection Dialog")
    print("Testing standalone mode...")
    
    dialog = SimpleConnectionDialog()
    result = dialog.show()
    
    if result:
        print(f"✅ Connection established: {result}")
    else:
        print("❌ Connection cancelled")
