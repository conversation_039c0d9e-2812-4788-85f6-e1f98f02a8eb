#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Smart TMDB XUI v2 - Nueva Generación
Aplicación mejorada con todas las funcionalidades optimizadas
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import tkinter.scrolledtext as scrolledtext
from datetime import datetime
import threading
import json
import os
from typing import Dict, List, Optional
import webbrowser

# Importar nuestros módulos
try:
    from database_manager import SmartDatabaseManager
    from tmdb_manager import SmartTMDBManager
    from config_manager import ConfigManager
    from connection_dialog import SmartConnectionDialog
except ImportError as e:
    print(f"⚠️ Error importando módulos: {e}")
    print("💡 Ejecutando sin módulos avanzados...")
    SmartDatabaseManager = None
    SmartTMDBManager = None
    ConfigManager = None
    SmartConnectionDialog = None

class SmartTMDBApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 Smart TMDB XUI v2 - Nueva Generación")
        self.root.geometry("1400x900")
        self.root.resizable(True, True)
        
        # Paleta Gaming Oscura: Mayoría Negro con acentos sutiles
        self.colors = {
            'bg': '#0A0A0A',           # Negro más profundo
            'surface': '#161616',      # Superficie muy oscura
            'surface_hover': '#202020', # Superficie hover sutil
            'fg': '#B0B0B0',           # Texto gris claro (no blanco)
            'fg_secondary': '#808080', # Texto secundario gris medio
            'accent': '#76B900',       # Verde NVIDIA solo para acentos importantes
            'success': '#5C9600',      # Verde más oscuro para éxito
            'warning': '#CC0033',      # Rojo más oscuro
            'nvidia_green': '#76B900', # Verde NVIDIA
            'rog_red': '#CC0033',      # Rojo ROG más sutil
            'gemini_purple': '#7B1FA2',# Púrpura más oscuro
            'gemini_deep': '#4A148C',  # Púrpura muy profundo
            'border': '#333333',       # Bordes grises oscuros
            'selection': '#76B90025',  # Selección muy translúcida
            'card_bg': '#121212',      # Fondo tarjetas casi negro
            'button_hover': '#5C9600', # Verde hover más sutil
            'text_muted': '#606060'    # Texto muy sutil
        }
        
        # Variables de estado
        self.config_manager = ConfigManager() if ConfigManager else None
        self.db_manager = SmartDatabaseManager() if SmartDatabaseManager else None
        self.tmdb_manager = None
        self.is_connected = False
        
        # Configurar la interfaz
        self.setup_styles()
        self.create_main_interface()
        
        # Configurar eventos
        self.setup_events()
        
        print("🚀 Smart TMDB XUI v2 iniciado")
    
    def setup_styles(self):
        """Configurar estilos Gaming Épicos: NVIDIA + ROG + Gemini"""
        self.root.configure(bg=self.colors['bg'])
        
        # Configurar estilo ttk
        style = ttk.Style()
        style.theme_use('clam')
        
        # ===== FRAMES GAMING =====
        style.configure('Gaming.TFrame', 
                       background=self.colors['bg'],
                       relief='flat')
        
        style.configure('Surface.TFrame',
                       background=self.colors['surface'],
                       relief='solid',
                       borderwidth=2,
                       bordercolor=self.colors['border'])
        
        style.configure('Card.TFrame',
                       background=self.colors['card_bg'],
                       relief='solid',
                       borderwidth=1,
                       bordercolor=self.colors['nvidia_green'])
        
        # ===== LABELS GAMING OSCUROS =====
        style.configure('Gaming.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['fg_secondary'],  # Gris medio, no blanco
                       font=('Segoe UI', 10))
        
        style.configure('Title.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['nvidia_green'],  # Solo títulos en verde
                       font=('Segoe UI', 16, 'bold'))
        
        style.configure('Subtitle.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['fg'],            # Gris claro para subtítulos
                       font=('Segoe UI', 11))
        
        style.configure('Success.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['success'],       # Verde más oscuro
                       font=('Segoe UI', 10, 'bold'))
        
        style.configure('Warning.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['warning'],       # Rojo más sutil
                       font=('Segoe UI', 10, 'bold'))
        
        style.configure('Muted.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['text_muted'],    # Texto muy sutil
                       font=('Segoe UI', 9))
        
        style.configure('Purple.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['gemini_purple'],
                       font=('Segoe UI', 11, 'bold'))
        
        # ===== BOTONES GAMING SUTILES =====
        style.configure('Gaming.TButton',
                       background=self.colors['surface'],
                       foreground=self.colors['fg'],            # Gris claro, no blanco
                       borderwidth=1,                           # Bordes más sutiles
                       relief='solid',
                       bordercolor=self.colors['border'],       # Bordes grises
                       font=('Segoe UI', 9),                    # No bold por defecto
                       padding=8)
        
        style.map('Gaming.TButton',
                 background=[('active', self.colors['surface_hover']),
                           ('pressed', self.colors['success'])],
                 foreground=[('active', self.colors['fg'])],
                 bordercolor=[('active', self.colors['nvidia_green'])])  # Verde solo en hover
        
        # Botón importante (verde sutil)
        style.configure('Important.TButton',
                       background=self.colors['success'],
                       foreground=self.colors['bg'],            # Negro sobre verde
                       borderwidth=1,
                       relief='solid',
                       bordercolor=self.colors['success'],
                       font=('Segoe UI', 9, 'bold'),
                       padding=8)
        
        # Botón ROG (rojo sutil)
        style.configure('ROG.TButton',
                       background=self.colors['surface'],
                       foreground=self.colors['warning'],       # Texto rojo, fondo oscuro
                       borderwidth=1,
                       relief='solid',
                       bordercolor=self.colors['warning'],
                       font=('Segoe UI', 9, 'bold'),
                       padding=8)
        
        # Botón Gemini (púrpura sutil)
        style.configure('Gemini.TButton',
                       background=self.colors['surface'],
                       foreground=self.colors['gemini_purple'], # Texto púrpura, fondo oscuro
                       borderwidth=1,
                       relief='solid',
                       bordercolor=self.colors['gemini_purple'],
                       font=('Segoe UI', 9, 'bold'),
                       padding=8)
        
        # ===== NOTEBOOK GAMING ÉPICO =====
        style.configure('Gaming.TNotebook',
                       background=self.colors['bg'],
                       borderwidth=0)
        
        style.configure('Gaming.TNotebook.Tab',
                       background=self.colors['surface'],
                       foreground=self.colors['fg'],
                       borderwidth=2,
                       bordercolor=self.colors['border'],
                       padding=[25, 12],                        # Pestañas más grandes
                       font=('Segoe UI', 11, 'bold'))           # Fuente más grande
        
        style.map('Gaming.TNotebook.Tab',
                 background=[('selected', self.colors['nvidia_green']),
                           ('active', self.colors['surface_hover'])],
                 foreground=[('selected', self.colors['bg']),     # Negro sobre verde
                           ('active', self.colors['nvidia_green'])], # Verde en hover
                 bordercolor=[('selected', self.colors['nvidia_green']),
                            ('active', self.colors['nvidia_green'])])
        
        # Estilo especial para pestañas importantes
        style.configure('Important.TNotebook.Tab',
                       background=self.colors['gemini_purple'],
                       foreground=self.colors['fg'],
                       borderwidth=2,
                       bordercolor=self.colors['gemini_deep'],
                       padding=[25, 12],
                       font=('Segoe UI', 11, 'bold'))
        
        style.configure('Accent.TButton',
                       background=self.colors['accent'],
                       foreground='white',
                       font=('Segoe UI', 10, 'bold'))
        
        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground='white',
                       font=('Segoe UI', 10, 'bold'))
        
        style.configure('Gaming.Treeview',
                       background=self.colors['surface'],
                       foreground=self.colors['fg'],
                       fieldbackground=self.colors['surface'],
                       borderwidth=1,
                       relief='solid')
        
        style.configure('Gaming.Treeview.Heading',
                       background=self.colors['bg'],
                       foreground=self.colors['accent'],
                       font=('Segoe UI', 10, 'bold'))
    
    def create_main_interface(self):
        """Crear la interfaz principal"""
        
        # Frame principal
        self.main_frame = ttk.Frame(self.root, style='Gaming.TFrame')
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Header
        self.create_header()
        
        # Contenedor principal con pestañas
        self.create_notebook()
        
        # Status bar
        self.create_status_bar()
        
        # Logs iniciales - después de crear todos los componentes
        self.log("🚀 Smart TMDB XUI v2 iniciado")
        self.log("💡 Conecta a la base de datos para comenzar")
    
    def create_header(self):
        """Crear el header con información y controles principales"""
        header_frame = ttk.Frame(self.main_frame, style='Surface.TFrame')
        header_frame.pack(fill='x', pady=(0, 10))
        
        # Título principal
        title_label = ttk.Label(header_frame, 
                               text="🚀 Smart TMDB XUI v2",
                               style='Title.TLabel')
        title_label.pack(side='left', padx=10, pady=10)
        
        # Información de versión
        version_label = ttk.Label(header_frame,
                                 text="Nueva Generación • Optimizado para Series y Películas",
                                 style='Gaming.TLabel')
        version_label.pack(side='left', padx=10, pady=10)
        
        # Controles de conexión
        connection_frame = ttk.Frame(header_frame, style='Gaming.TFrame')
        connection_frame.pack(side='right', padx=10, pady=10)
        
        self.connect_btn = ttk.Button(connection_frame,
                                     text="🔌 Conectar DB",
                                     style='Accent.TButton',
                                     command=self.show_connection_dialog)
        self.connect_btn.pack(side='right', padx=5)
        
        self.status_label = ttk.Label(connection_frame,
                                     text="❌ Desconectado",
                                     style='Warning.TLabel')
        self.status_label.pack(side='right', padx=10)
    
    def create_notebook(self):
        """Crear el notebook con pestañas gaming ultra modernas"""
        # Contenedor principal para pestañas custom
        tabs_container = tk.Frame(self.main_frame, bg=self.colors['bg'])
        tabs_container.pack(fill='x', pady=(0, 5))
        
        # Frame para botones de pestañas
        self.tabs_frame = tk.Frame(tabs_container, bg=self.colors['bg'])
        self.tabs_frame.pack(fill='x', padx=10)
        
        # Frame para contenido
        self.content_frame = tk.Frame(self.main_frame, bg=self.colors['bg'])
        self.content_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # Crear pestañas custom
        self.tabs_data = [
            {'name': 'dashboard', 'text': '🚀 DASHBOARD', 'color': self.colors['nvidia_green']},
            {'name': 'series', 'text': '📺 SERIES TV', 'color': self.colors['gemini_purple']},
            {'name': 'movies', 'text': '🎬 PELÍCULAS', 'color': self.colors['rog_red']},
            {'name': 'maintenance', 'text': '⚙️ MAINTENANCE', 'color': self.colors['nvidia_green']},
            {'name': 'tools', 'text': '🛠️ TOOLS', 'color': self.colors['gemini_deep']}
        ]
        
        self.tab_buttons = {}
        self.tab_frames = {}
        self.current_tab = 'dashboard'
        
        # Crear botones de pestañas épicas
        for i, tab in enumerate(self.tabs_data):
            self.create_epic_tab_button(tab, i)
        
        # Crear frames de contenido
        for tab in self.tabs_data:
            frame = tk.Frame(self.content_frame, bg=self.colors['bg'])
            self.tab_frames[tab['name']] = frame
            
        # Mostrar pestaña inicial
        self.show_tab('dashboard')
    
    def create_epic_tab_button(self, tab_data, index):
        """Crear botón de pestaña con efectos épicos"""
        name = tab_data['name']
        text = tab_data['text']
        color = tab_data['color']
        
        # Crear botón custom con efectos
        btn_frame = tk.Frame(self.tabs_frame, bg=self.colors['bg'])
        btn_frame.pack(side='left', padx=2)
        
        # Botón principal
        btn = tk.Button(btn_frame, 
                       text=text,
                       font=('Segoe UI', 10, 'bold'),
                       bg=self.colors['surface'],
                       fg=self.colors['fg'],
                       activebackground=color,
                       activeforeground=self.colors['bg'],
                       relief='flat',
                       bd=0,
                       padx=20, pady=8,
                       cursor='hand2',
                       command=lambda: self.show_tab(name))
        
        btn.pack()
        self.tab_buttons[name] = btn
        
        # Efectos hover
        def on_enter(e):
            if name != self.current_tab:
                btn.config(bg=self.colors['surface_hover'], 
                          fg=color,
                          relief='solid',
                          bd=1)
        
        def on_leave(e):
            if name != self.current_tab:
                btn.config(bg=self.colors['surface'], 
                          fg=self.colors['fg'],
                          relief='flat',
                          bd=0)
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
    
    def show_tab(self, tab_name):
        """Mostrar pestaña seleccionada con efectos"""
        # Ocultar todas las pestañas
        for name, frame in self.tab_frames.items():
            frame.pack_forget()
        
        # Resetear estilos de todos los botones
        for name, btn in self.tab_buttons.items():
            if name == tab_name:
                # Botón activo - estilo épico
                color = next(t['color'] for t in self.tabs_data if t['name'] == name)
                btn.config(bg=color,
                          fg=self.colors['bg'],
                          relief='solid',
                          bd=2)
            else:
                # Botones inactivos
                btn.config(bg=self.colors['surface'],
                          fg=self.colors['fg'],
                          relief='flat',
                          bd=0)
        
        # Mostrar pestaña seleccionada
        if tab_name in self.tab_frames:
            self.tab_frames[tab_name].pack(fill='both', expand=True)
            self.current_tab = tab_name
            
            # Crear contenido si no existe
            if not self.tab_frames[tab_name].winfo_children():
                self.create_tab_content(tab_name)
    
    def create_tab_content(self, tab_name):
        """Crear contenido de la pestaña"""
        frame = self.tab_frames[tab_name]
        
        if tab_name == 'dashboard':
            self.create_dashboard_content(frame)
        elif tab_name == 'series':
            self.create_series_content(frame)
        elif tab_name == 'movies':
            self.create_movies_content(frame)
        elif tab_name == 'maintenance':
            self.create_maintenance_content(frame)
        elif tab_name == 'tools':
            self.create_tools_content(frame)
    
    def create_dashboard_content(self, parent_frame):
        """Crear contenido del dashboard épico con funcionalidad completa"""
        # Título épico
        title = tk.Label(parent_frame, 
                        text="🚀 SMART TMDB DASHBOARD",
                        font=('Segoe UI', 18, 'bold'),
                        fg=self.colors['nvidia_green'],
                        bg=self.colors['bg'])
        title.pack(pady=20)
        
        # Grid para métricas épicas
        metrics_grid = tk.Frame(parent_frame, bg=self.colors['bg'])
        metrics_grid.pack(fill='x', padx=20, pady=10)
        
        # Métricas dinámicas - se actualizarán con datos reales
        self.metric_labels = {}
        metrics = [
            ("🎬 PELÍCULAS", "0", self.colors['nvidia_green']),
            ("📺 EPISODIOS", "0", self.colors['gemini_purple']),
            ("⚡ OPTIMIZADAS", "0%", self.colors['rog_red']),
            ("🔥 ESTADO", "DESCONECTADO", self.colors['warning'])
        ]
        
        for i, (label, value, color) in enumerate(metrics):
            card = tk.Frame(metrics_grid, bg=self.colors['surface'], relief='solid', bd=2)
            card.grid(row=0, column=i, padx=10, pady=5, sticky='ew')
            metrics_grid.columnconfigure(i, weight=1)
            
            tk.Label(card, text=label, font=('Segoe UI', 10, 'bold'),
                    fg=self.colors['fg'], bg=self.colors['surface']).pack(pady=5)
            
            value_label = tk.Label(card, text=value, font=('Segoe UI', 14, 'bold'),
                                  fg=color, bg=self.colors['surface'])
            value_label.pack()
            
            # Guardar referencia para actualizar valores
            self.metric_labels[label] = value_label
            
            tk.Label(card, text="●●●", font=('Segoe UI', 8),
                    fg=color, bg=self.colors['surface']).pack(pady=5)
        
        # Botones de acción rápida
        actions_frame = tk.Frame(parent_frame, bg=self.colors['bg'])
        actions_frame.pack(fill='x', padx=20, pady=20)
        
        actions_title = tk.Label(actions_frame, text="⚡ ACCIONES RÁPIDAS", 
                               font=('Segoe UI', 14, 'bold'),
                               fg=self.colors['nvidia_green'],
                               bg=self.colors['bg'])
        actions_title.pack(pady=(0, 10))
        
        # Grid de botones
        buttons_grid = tk.Frame(actions_frame, bg=self.colors['bg'])
        buttons_grid.pack()
        
        buttons = [
            ("🔄 Actualizar Métricas", self.update_metrics, self.colors['nvidia_green']),
            ("🎯 Asignar Íconos", self.auto_assign_icons, self.colors['gemini_purple']),
            ("📅 Corregir Fechas", self.fix_timestamps, self.colors['rog_red']),
            ("🔧 Optimizar DB", self.optimize_database, self.colors['success'])
        ]
        
        for i, (text, command, color) in enumerate(buttons):
            btn = tk.Button(buttons_grid,
                           text=text,
                           font=('Segoe UI', 10, 'bold'),
                           bg=self.colors['surface'],
                           fg=color,
                           activebackground=color,
                           activeforeground=self.colors['bg'],
                           relief='solid',
                           bd=1,
                           padx=15, pady=8,
                           cursor='hand2',
                           command=command)
            btn.grid(row=i//2, column=i%2, padx=10, pady=5, sticky='ew')
            buttons_grid.columnconfigure(i%2, weight=1)
        
        # Log de actividades
        log_frame = tk.Frame(parent_frame, bg=self.colors['surface'], relief='solid', bd=2)
        log_frame.pack(fill='both', expand=True, padx=20, pady=(10, 0))
        
        log_title = tk.Label(log_frame, text="📝 LOG DE ACTIVIDADES", 
                           font=('Segoe UI', 12, 'bold'),
                           fg=self.colors['nvidia_green'],
                           bg=self.colors['surface'])
        log_title.pack(pady=5)
        
        # ScrolledText para los logs
        self.log_text = scrolledtext.ScrolledText(log_frame,
                                                 height=8,
                                                 bg=self.colors['bg'],
                                                 fg=self.colors['fg'],
                                                 insertbackground=self.colors['accent'],
                                                 font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_series_content(self, parent_frame):
        """Crear contenido de series épico"""
        title = tk.Label(parent_frame, 
                        text="📺 SERIES TV MANAGEMENT",
                        font=('Segoe UI', 18, 'bold'),
                        fg=self.colors['gemini_purple'],
                        bg=self.colors['bg'])
        title.pack(pady=20)
        
        desc = tk.Label(parent_frame, 
                       text="Gestión completa de series de TV\nOptimización TMDB avanzada\nControl total de episodios",
                       font=('Segoe UI', 12),
                       fg=self.colors['fg'],
                       bg=self.colors['bg'],
                       justify='center')
        desc.pack(pady=10)
        
        # Botones de gestión de series
        buttons_frame = tk.Frame(parent_frame, bg=self.colors['bg'])
        buttons_frame.pack(pady=20)
        
        series_buttons = [
            ("🔄 Actualizar Series", self.refresh_series),
            ("🎯 Asignar TMDB", self.assign_tmdb_series),
            ("📊 Analizar Episodios", self.analyze_episodes)
        ]
        
        for i, (text, command) in enumerate(series_buttons):
            btn = tk.Button(buttons_frame,
                           text=text,
                           font=('Segoe UI', 10, 'bold'),
                           bg=self.colors['surface'],
                           fg=self.colors['gemini_purple'],
                           activebackground=self.colors['gemini_purple'],
                           activeforeground=self.colors['bg'],
                           relief='solid',
                           bd=1,
                           padx=15, pady=8,
                           cursor='hand2',
                           command=command)
            btn.grid(row=0, column=i, padx=10, pady=5)
    
    def create_movies_content(self, parent_frame):
        """Crear contenido de películas épico"""
        title = tk.Label(parent_frame, 
                        text="🎬 MOVIES CONTROL CENTER",
                        font=('Segoe UI', 18, 'bold'),
                        fg=self.colors['rog_red'],
                        bg=self.colors['bg'])
        title.pack(pady=20)
        
        desc = tk.Label(parent_frame, 
                       text="Control total de películas\nBase de datos optimizada\nAsignación automática de metadatos",
                       font=('Segoe UI', 12),
                       fg=self.colors['fg'],
                       bg=self.colors['bg'],
                       justify='center')
        desc.pack(pady=10)
        
        # Botones de gestión de películas
        buttons_frame = tk.Frame(parent_frame, bg=self.colors['bg'])
        buttons_frame.pack(pady=20)
        
        movie_buttons = [
            ("🔄 Actualizar Películas", self.refresh_movies),
            ("🎯 Asignar TMDB", self.assign_tmdb_movies),
            ("📈 Generar Reporte", self.generate_report)
        ]
        
        for i, (text, command) in enumerate(movie_buttons):
            btn = tk.Button(buttons_frame,
                           text=text,
                           font=('Segoe UI', 10, 'bold'),
                           bg=self.colors['surface'],
                           fg=self.colors['rog_red'],
                           activebackground=self.colors['rog_red'],
                           activeforeground=self.colors['bg'],
                           relief='solid',
                           bd=1,
                           padx=15, pady=8,
                           cursor='hand2',
                           command=command)
            btn.grid(row=0, column=i, padx=10, pady=5)
    
    def create_maintenance_content(self, parent_frame):
        """Crear contenido de mantenimiento épico"""
        title = tk.Label(parent_frame, 
                        text="⚙️ MAINTENANCE STATION",
                        font=('Segoe UI', 18, 'bold'),
                        fg=self.colors['nvidia_green'],
                        bg=self.colors['bg'])
        title.pack(pady=20)
        
        desc = tk.Label(parent_frame, 
                       text="Herramientas de mantenimiento avanzadas\nOptimización y correcciones automáticas\nDiagnóstico completo del sistema",
                       font=('Segoe UI', 12),
                       fg=self.colors['fg'],
                       bg=self.colors['bg'],
                       justify='center')
        desc.pack(pady=10)
        
        # Grid de herramientas de mantenimiento
        tools_grid = tk.Frame(parent_frame, bg=self.colors['bg'])
        tools_grid.pack(pady=20)
        
        maintenance_tools = [
            ("📅 Corregir Timestamps", self.fix_all_timestamps, self.colors['nvidia_green']),
            ("📦 Corregir Containers", self.fix_containers, self.colors['gemini_purple']),
            ("🔢 Corregir Order Values", self.fix_order_values, self.colors['rog_red']),
            ("🖼️ Asignación Masiva Íconos", self.mass_assign_icons, self.colors['nvidia_green']),
            ("🧹 Limpieza Completa", self.full_cleanup, self.colors['warning']),
            ("🏥 Diagnóstico Dr. House", self.diagnose_dr_house, self.colors['gemini_purple'])
        ]
        
        for i, (text, command, color) in enumerate(maintenance_tools):
            btn = tk.Button(tools_grid,
                           text=text,
                           font=('Segoe UI', 10, 'bold'),
                           bg=self.colors['surface'],
                           fg=color,
                           activebackground=color,
                           activeforeground=self.colors['bg'],
                           relief='solid',
                           bd=1,
                           padx=15, pady=8,
                           cursor='hand2',
                           command=command)
            btn.grid(row=i//3, column=i%3, padx=10, pady=5, sticky='ew')
            tools_grid.columnconfigure(i%3, weight=1)
    
    def create_tools_content(self, parent_frame):
        """Crear contenido de herramientas épico"""
        title = tk.Label(parent_frame, 
                        text="🛠️ ADVANCED TOOLS",
                        font=('Segoe UI', 18, 'bold'),
                        fg=self.colors['gemini_deep'],
                        bg=self.colors['bg'])
        title.pack(pady=20)
        
        desc = tk.Label(parent_frame, 
                       text="Herramientas avanzadas de desarrollo\nImportación y análisis de datos\nConfiguraciones avanzadas",
                       font=('Segoe UI', 12),
                       fg=self.colors['fg'],
                       bg=self.colors['bg'],
                       justify='center')
        desc.pack(pady=10)
        
        # Grid de herramientas avanzadas
        tools_grid = tk.Frame(parent_frame, bg=self.colors['bg'])
        tools_grid.pack(pady=20)
        
        advanced_tools = [
            ("📄 Importar M3U", self.import_m3u, self.colors['nvidia_green']),
            ("💾 Exportar Base", self.export_database, self.colors['gemini_purple']),
            ("🔄 Sincronizar TMDB", self.sync_tmdb, self.colors['rog_red']),
            ("🐞 Debug Mode", self.toggle_debug, self.colors['nvidia_green']),
            ("📊 SQL Console", self.open_sql_console, self.colors['gemini_deep']),
            ("🔧 Configuración", self.open_config, self.colors['warning'])
        ]
        
        for i, (text, command, color) in enumerate(advanced_tools):
            btn = tk.Button(tools_grid,
                           text=text,
                           font=('Segoe UI', 10, 'bold'),
                           bg=self.colors['surface'],
                           fg=color,
                           activebackground=color,
                           activeforeground=self.colors['bg'],
                           relief='solid',
                           bd=1,
                           padx=15, pady=8,
                           cursor='hand2',
                           command=command)
            btn.grid(row=i//3, column=i%3, padx=10, pady=5, sticky='ew')
            tools_grid.columnconfigure(i%3, weight=1)
    
    def create_status_bar(self):
        """Crear la barra de estado"""
        self.status_frame = ttk.Frame(self.main_frame, style='Surface.TFrame')
        self.status_frame.pack(fill='x', pady=(0, 0))
        
        # Status izquierdo
        self.status_left = ttk.Label(self.status_frame, 
                                    text="⚡ Smart TMDB XUI v2 - Listo",
                                    style='Gaming.TLabel')
        self.status_left.pack(side='left', padx=10, pady=5)
        
        # Status derecho
        self.status_right = ttk.Label(self.status_frame,
                                     text=f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                                     style='Gaming.TLabel')
        self.status_right.pack(side='right', padx=10, pady=5)
    
    def setup_events(self):
        """Configurar eventos de la aplicación"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Actualizar reloj cada minuto
        self.update_clock()
    
    def log(self, message: str, level: str = "INFO"):
        """Agregar mensaje al log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # Íconos por nivel
        icons = {
            "INFO": "ℹ️",
            "SUCCESS": "✅", 
            "WARNING": "⚠️",
            "ERROR": "❌",
            "DEBUG": "🐞"
        }
        
        icon = icons.get(level, "ℹ️")
        log_message = f"[{timestamp}] {icon} {message}\n"
        
        if hasattr(self, 'log_text'):
            self.log_text.insert('end', log_message)
            self.log_text.see('end')
        
        print(f"{log_message.strip()}")
        
        # Actualizar status bar si existe
        if hasattr(self, 'status_left'):
            self.status_left.config(text=f"⚡ {message}")
    
    def update_clock(self):
        """Actualizar el reloj en la status bar"""
        if hasattr(self, 'status_right'):
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M')
            self.status_right.config(text=f"📅 {current_time}")
        
        # Programar próxima actualización en 60 segundos
        self.root.after(60000, self.update_clock)
    
    # ===== MÉTODOS DE FUNCIONALIDAD =====
    
    def show_connection_dialog(self):
        """Mostrar diálogo de conexión a base de datos"""
        self.log("🔌 Abriendo diálogo de conexión...")
        
        if SmartConnectionDialog:
            dialog = SmartConnectionDialog(self.root, self.config_manager, self.on_connection_established)
            result = dialog.show()
            
            if result:
                self.log("✅ Datos de conexión obtenidos", "SUCCESS")
            else:
                self.log("❌ Conexión cancelada", "WARNING")
        else:
            self.log("⚠️ Módulo de conexión no disponible", "WARNING")
    
    def on_connection_established(self, connection_data: Dict):
        """Manejar conexión establecida"""
        try:
            if self.db_manager:
                # Intentar conectar
                success = self.db_manager.connect(
                    host=connection_data['host'],
                    user=connection_data['user'],
                    password=connection_data['password'],
                    database=connection_data['database'],
                    port=connection_data['port']
                )
                
                if success:
                    self.is_connected = True
                    self.status_label.config(text="✅ Conectado")
                    self.connect_btn.config(text="🔌 Conectado")
                    self.log("🎉 Conexión exitosa a la base de datos", "SUCCESS")
                    
                    # Actualizar métricas automáticamente
                    self.update_metrics()
                    
                    # Actualizar estado en dashboard
                    if "🔥 ESTADO" in self.metric_labels:
                        self.metric_labels["🔥 ESTADO"].config(
                            text="CONECTADO",
                            fg=self.colors['nvidia_green']
                        )
                    
                    # Inicializar TMDB si hay API key
                    if self.config_manager:
                        tmdb_key = self.config_manager.get('tmdb.api_key')
                        if tmdb_key and SmartTMDBManager:
                            self.tmdb_manager = SmartTMDBManager(tmdb_key)
                            self.log("🎬 TMDB Manager inicializado", "SUCCESS")
                else:
                    self.log("❌ Error al conectar a la base de datos", "ERROR")
            else:
                self.log("⚠️ Database manager no disponible", "WARNING")
                
        except Exception as e:
            self.log(f"❌ Error de conexión: {e}", "ERROR")
    
    def update_metrics(self):
        """Actualizar métricas del dashboard"""
        if not self.is_connected or not self.db_manager:
            self.log("⚠️ No hay conexión a la base de datos", "WARNING")
            return
        
        self.log("🔄 Actualizando métricas...")
        
        try:
            # Obtener métricas de la base de datos
            metrics = self.db_manager.get_database_metrics()
            
            # Actualizar las tarjetas de métricas en el dashboard
            if hasattr(self, 'metric_labels'):
                # Actualizar películas
                if "🎬 PELÍCULAS" in self.metric_labels:
                    self.metric_labels["🎬 PELÍCULAS"].config(text=f"{metrics['movies']:,}")
                
                # Actualizar episodios
                if "📺 EPISODIOS" in self.metric_labels:
                    self.metric_labels["📺 EPISODIOS"].config(text=f"{metrics['episodes']:,}")
                
                # Actualizar porcentaje optimizado
                total_content = metrics['movies'] + metrics['episodes']
                if total_content > 0:
                    optimized_percentage = (metrics['with_icons'] / total_content) * 100
                    if "⚡ OPTIMIZADAS" in self.metric_labels:
                        self.metric_labels["⚡ OPTIMIZADAS"].config(text=f"{optimized_percentage:.1f}%")
                
                # Estado de conexión
                if "🔥 ESTADO" in self.metric_labels:
                    self.metric_labels["🔥 ESTADO"].config(
                        text="GAMING",
                        fg=self.colors['nvidia_green']
                    )
            
            self.log(f"✅ Métricas actualizadas - {metrics['movies']:,} películas, {metrics['episodes']:,} episodios", "SUCCESS")
            
        except Exception as e:
            self.log(f"❌ Error actualizando métricas: {e}", "ERROR")
    
    def auto_assign_icons(self):
        """Asignar íconos automáticamente"""
        if not self.is_connected or not self.db_manager:
            self.log("⚠️ No hay conexión a la base de datos", "WARNING")
            return
            
        self.log("🎯 Iniciando asignación automática de íconos...")
        
        try:
            success, affected = self.db_manager.auto_assign_icons_from_json()
            
            if success:
                self.log(f"✅ Íconos asignados exitosamente: {affected:,} elementos", "SUCCESS")
                self.update_metrics()  # Actualizar métricas
            else:
                self.log("❌ Error en la asignación de íconos", "ERROR")
                
        except Exception as e:
            self.log(f"❌ Error asignando íconos: {e}", "ERROR")
    
    def fix_timestamps(self):
        """Corregir timestamps problemáticos"""
        if not self.is_connected or not self.db_manager:
            self.log("⚠️ No hay conexión a la base de datos", "WARNING")
            return
        
        self.log("📅 Corrigiendo timestamps problemáticos...")
        
        try:
            # Corregir películas
            success_movies, affected_movies = self.db_manager.fix_movie_timestamps_to_realistic()
            
            # Corregir episodios
            success_episodes, affected_episodes = self.db_manager.fix_episode_timestamps_to_realistic()
            
            if success_movies or success_episodes:
                total_fixed = affected_movies + affected_episodes
                self.log(f"✅ Timestamps corregidos: {total_fixed:,} elementos ({affected_movies:,} películas, {affected_episodes:,} episodios)", "SUCCESS")
                self.update_metrics()
            else:
                self.log("❌ Error corrigiendo timestamps", "ERROR")
                
        except Exception as e:
            self.log(f"❌ Error corrigiendo timestamps: {e}", "ERROR")
    
    def optimize_database(self):
        """Optimizar base de datos con todas las correcciones"""
        if not self.is_connected or not self.db_manager:
            self.log("⚠️ No hay conexión a la base de datos", "WARNING")
            return
        
        self.log("🔧 Iniciando optimización completa de la base de datos...")
        
        try:
            total_fixes = 0
            
            # 1. Corregir timestamps
            success, affected = self.db_manager.fix_movie_timestamps_to_realistic()
            if success:
                total_fixes += affected
                self.log(f"   ✅ Timestamps películas: {affected:,} corregidos")
            
            success, affected = self.db_manager.fix_episode_timestamps_to_realistic()
            if success:
                total_fixes += affected
                self.log(f"   ✅ Timestamps episodios: {affected:,} corregidos")
            
            # 2. Corregir containers
            success, affected = self.db_manager.fix_movie_containers()
            if success:
                total_fixes += affected
                self.log(f"   ✅ Containers películas: {affected:,} establecidos")
            
            # 3. Corregir order values
            success, affected = self.db_manager.fix_movie_order_values()
            if success:
                total_fixes += affected
                self.log(f"   ✅ Order values: {affected:,} corregidos")
            
            # 4. Asignar íconos
            success, affected = self.db_manager.auto_assign_icons_from_json()
            if success:
                total_fixes += affected
                self.log(f"   ✅ Íconos asignados: {affected:,} elementos")
            
            self.log(f"🎉 Optimización completada: {total_fixes:,} correcciones aplicadas", "SUCCESS")
            self.update_metrics()
            
        except Exception as e:
            self.log(f"❌ Error en optimización: {e}", "ERROR")
    
    # ===== MÉTODOS PLACEHOLDER (FUNCIONALIDADES ADICIONALES) =====
    
    def generate_report(self):
        """Generar reporte completo"""
        self.log("📊 Generando reporte completo...")
        
    def backup_database(self):
        """Hacer backup de la base de datos"""
        self.log("💾 Creando backup de base de datos...")
        
    def refresh_series(self):
        """Actualizar lista de series"""
        self.log("📺 Actualizando lista de series...")
        
    def assign_tmdb_series(self):
        """Asignar TMDB a series"""
        self.log("🎯 Asignando TMDB a series...")
        
    def analyze_episodes(self):
        """Analizar episodios"""
        self.log("📊 Analizando episodios...")
        
    def refresh_movies(self):
        """Actualizar lista de películas"""
        self.log("🎬 Actualizando lista de películas...")
        
    def assign_tmdb_movies(self):
        """Asignar TMDB a películas"""
        self.log("🎯 Asignando TMDB a películas...")
        
    def fix_all_timestamps(self):
        """Corregir todos los timestamps"""
        self.log("📅 Corrigiendo todos los timestamps...")
        self.fix_timestamps()
        
    def fix_containers(self):
        """Corregir containers"""
        if not self.is_connected or not self.db_manager:
            self.log("⚠️ No hay conexión a la base de datos", "WARNING")
            return
        
        self.log("📦 Corrigiendo containers...")
        try:
            success, affected = self.db_manager.fix_movie_containers()
            if success:
                self.log(f"✅ Containers corregidos: {affected:,} películas", "SUCCESS")
            else:
                self.log("❌ Error corrigiendo containers", "ERROR")
        except Exception as e:
            self.log(f"❌ Error corrigiendo containers: {e}", "ERROR")
        
    def fix_order_values(self):
        """Corregir valores de order"""
        if not self.is_connected or not self.db_manager:
            self.log("⚠️ No hay conexión a la base de datos", "WARNING")
            return
            
        self.log("🔢 Corrigiendo valores de order...")
        try:
            success, affected = self.db_manager.fix_movie_order_values()
            if success:
                self.log(f"✅ Order values corregidos: {affected:,} elementos", "SUCCESS")
            else:
                self.log("❌ Error corrigiendo order values", "ERROR")
        except Exception as e:
            self.log(f"❌ Error corrigiendo order values: {e}", "ERROR")
        
    def fix_json_format(self):
        """Corregir formato JSON"""
        self.log("🎭 Corrigiendo formato JSON...")
        
    def mass_assign_icons(self):
        """Asignación masiva de íconos"""
        self.log("🖼️ Iniciando asignación masiva de íconos...")
        self.auto_assign_icons()
        
    def full_cleanup(self):
        """Limpieza completa de la base de datos"""
        self.log("🧹 Iniciando limpieza completa...")
        self.optimize_database()
        
    def analyze_compatibility(self):
        """Analizar compatibilidad"""
        self.log("🔍 Analizando compatibilidad...")
        
    def diagnose_dr_house(self):
        """Diagnosticar problemas de Dr. House"""
        self.log("🏥 Ejecutando diagnóstico Dr. House...")
        
    def full_report(self):
        """Generar reporte completo"""
        self.log("📈 Generando reporte completo...")
        
    def import_m3u(self):
        """Importar archivo M3U"""
        self.log("📄 Importando archivo M3U...")
        
    def export_database(self):
        """Exportar base de datos"""
        self.log("💾 Exportando base de datos...")
        
    def sync_tmdb(self):
        """Sincronizar con TMDB"""
        self.log("🔄 Sincronizando con TMDB...")
        
    def toggle_debug(self):
        """Activar/desactivar modo debug"""
        self.log("🐞 Alternando modo debug...")
        
    def open_sql_console(self):
        """Abrir consola SQL"""
        self.log("📊 Abriendo consola SQL...")
        
    def open_config(self):
        """Abrir configuración"""
        self.log("🔧 Abriendo configuración...")
        
    def on_closing(self):
        """Manejar cierre de la aplicación"""
        self.log("👋 Cerrando Smart TMDB XUI v2...")
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """Ejecutar la aplicación"""
        self.root.mainloop()

def main():
    """Función principal"""
    app = SmartTMDBApp()
    app.run()

if __name__ == "__main__":
    main()
