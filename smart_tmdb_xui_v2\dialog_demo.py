#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo de ambos diálogos de conexión
Muestra Gaming Dialog y Modern Dialog funcionando
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Agregar el directorio actual al path para importar los diálogos
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from gaming_dialog import SimpleConnectionDialog as GamingDialog
    GAMING_AVAILABLE = True
except ImportError:
    GAMING_AVAILABLE = False

try:
    from simple_dialog import SimpleConnectionDialog as ModernDialog  
    MODERN_AVAILABLE = True
except ImportError:
    MODERN_AVAILABLE = False

class DialogDemo:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔌 Connection Dialog Demo")
        self.root.geometry("400x300")
        self.root.configure(bg='#2D2D2D')
        
        self.setup_ui()
        
    def setup_ui(self):
        # Título
        title = tk.Label(self.root, 
                        text="🔌 Connection Dialog Demo", 
                        font=('Segoe UI', 16, 'bold'),
                        bg='#2D2D2D', 
                        fg='white')
        title.pack(pady=20)
        
        # Descripción
        desc = tk.Label(self.root, 
                       text="Test both Gaming and Modern connection dialogs", 
                       font=('Segoe UI', 10),
                       bg='#2D2D2D', 
                       fg='#CCCCCC')
        desc.pack(pady=10)
        
        # Frame para botones
        btn_frame = tk.Frame(self.root, bg='#2D2D2D')
        btn_frame.pack(pady=30)
        
        # Botón Gaming Dialog
        if GAMING_AVAILABLE:
            gaming_btn = tk.Button(btn_frame,
                                  text="🎮 Gaming Dialog",
                                  command=self.show_gaming_dialog,
                                  font=('Segoe UI', 12, 'bold'),
                                  bg='#76B900',  # NVIDIA green
                                  fg='white',
                                  padx=20,
                                  pady=10,
                                  cursor='hand2')
            gaming_btn.pack(pady=10)
        
        # Botón Modern Dialog
        if MODERN_AVAILABLE:
            modern_btn = tk.Button(btn_frame,
                                  text="🔗 Modern Dialog",
                                  command=self.show_modern_dialog,
                                  font=('Segoe UI', 12, 'bold'),
                                  bg='#007ACC',  # VS Code blue
                                  fg='white',
                                  padx=20,
                                  pady=10,
                                  cursor='hand2')
            modern_btn.pack(pady=10)
        
        # Status
        status_text = []
        if GAMING_AVAILABLE:
            status_text.append("✅ Gaming Dialog Available")
        if MODERN_AVAILABLE:
            status_text.append("✅ Modern Dialog Available")
        
        if not status_text:
            status_text.append("❌ No dialogs available")
            
        status = tk.Label(self.root,
                         text="\n".join(status_text),
                         font=('Segoe UI', 9),
                         bg='#2D2D2D',
                         fg='#4CAF50' if status_text[0].startswith('✅') else '#F44336')
        status.pack(pady=20)
        
        # Resultado
        self.result_label = tk.Label(self.root,
                                   text="No connection tested yet",
                                   font=('Consolas', 8),
                                   bg='#2D2D2D',
                                   fg='#888888')
        self.result_label.pack(pady=10)
    
    def show_result(self, result, dialog_type):
        """Mostrar resultado de la conexión"""
        if result:
            self.result_label.config(
                text=f"✅ {dialog_type} Success: {result['host']}:{result['port']}",
                fg='#4CAF50'
            )
        else:
            self.result_label.config(
                text=f"❌ {dialog_type} Cancelled",
                fg='#F44336'
            )
    
    def show_gaming_dialog(self):
        """Mostrar Gaming Dialog"""
        if not GAMING_AVAILABLE:
            messagebox.showerror("Error", "Gaming Dialog not available")
            return
            
        try:
            dialog = GamingDialog(parent=self.root)
            result = dialog.show()
            self.show_result(result, "Gaming Dialog")
        except Exception as e:
            messagebox.showerror("Error", f"Gaming Dialog error:\n{str(e)}")
    
    def show_modern_dialog(self):
        """Mostrar Modern Dialog"""
        if not MODERN_AVAILABLE:
            messagebox.showerror("Error", "Modern Dialog not available")
            return
            
        try:
            dialog = ModernDialog(parent=self.root)
            result = dialog.show()
            self.show_result(result, "Modern Dialog")
        except Exception as e:
            messagebox.showerror("Error", f"Modern Dialog error:\n{str(e)}")
    
    def run(self):
        """Ejecutar demo"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🔌 Connection Dialog Demo")
    print(f"Gaming Available: {GAMING_AVAILABLE}")
    print(f"Modern Available: {MODERN_AVAILABLE}")
    
    demo = DialogDemo()
    demo.run()
