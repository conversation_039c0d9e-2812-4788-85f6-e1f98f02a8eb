#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de colores gaming: NVIDIA + ROG + Gemini
Prueba visual de la nueva paleta de colores épica
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Agregar directorio al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from main import SmartTMDBApp
    print("✅ Módulo principal importado")
except ImportError as e:
    print(f"❌ Error importando: {e}")
    sys.exit(1)

def test_gaming_colors():
    """Probar los nuevos colores gaming"""
    print("🎮 Iniciando test de colores gaming...")
    print("🎨 Paleta: NVIDIA Verde + ROG Rojo + Gemini Púrpura")
    
    # Crear aplicación con nuevos colores
    app = SmartTMDBApp()
    
    print("🚀 Aplicación iniciada con colores gaming épicos!")
    print("👀 Observa los colores:")
    print("   🟢 Verde NVIDIA para acentos principales")
    print("   🔴 Rojo ROG para advertencias")
    print("   🟣 Púrpura Gemini para elementos especiales")
    print("   ⚫ Negro profundo ASUS para fondos")
    
    app.root.mainloop()

if __name__ == "__main__":
    test_gaming_colors()
