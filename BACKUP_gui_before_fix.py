import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import tkinter.scrolledtext as scrolledtext
from database import DatabaseManager
from tmdb_manager import TMDBManager
import threading
import time
from typing import List, Dict

# Configuración de temas modernos MEJORADOS
class ModernTheme:
    """Temas modernos ultra profesionales con mejor contraste"""

    # Tema VS Code Dark MEJORADO
    VSCODE_DARK = {
        'bg': '#1e1e1e',           # Fondo principal
        'fg': '#d4d4d4',           # Texto principal
        'select_bg': '#264f78',     # Selección
        'select_fg': '#ffffff',     # Texto seleccionado
        'accent': '#0078d4',        # Azul más brillante y visible
        'accent_hover': '#106ebe',  # Hover más contrastado
        'accent_light': '#4fc3f7',  # Azul claro para acentos
        'sidebar_bg': '#252526',    # Barra lateral
        'tab_bg': '#2d2d30',       # Pesta<PERSON>s
        'tab_active': '#1e1e1e',   # Pestaña activa
        'border': '#464647',       # Bordes más visibles
        'button_bg': '#0078d4',    # Botones más brillantes
        'button_hover': '#106ebe', # Hover más visible
        'button_text': '#ffffff',  # Texto botones siempre blanco
        'success': '#16c60c',      # Verde más brillante
        'warning': '#ffb900',      # Amarillo más visible
        'error': '#e74856',        # Rojo más contrastado
        'info': '#0078d4',         # Azul información
        'surface': '#2d2d30',      # Superficies elevadas
        'surface_hover': '#3e3e42' # Superficies hover
    }

    # Tema Windows 11 Light MEJORADO
    WIN11_LIGHT = {
        'bg': '#f9f9f9',           # Fondo más suave
        'fg': '#323130',           # Texto principal
        'select_bg': '#0078d4',     # Selección más brillante
        'select_fg': '#ffffff',     # Texto seleccionado
        'accent': '#0078d4',        # Azul Windows
        'accent_hover': '#106ebe',  # Hover contrastado
        'accent_light': '#4fc3f7',  # Azul claro
        'sidebar_bg': '#f3f3f3',    # Barra lateral
        'tab_bg': '#e6e6e6',       # Pestañas más definidas
        'tab_active': '#ffffff',   # Pestaña activa
        'border': '#d1d1d1',       # Bordes más visibles
        'button_bg': '#0078d4',    # Botones
        'button_hover': '#106ebe', # Hover
        'button_text': '#ffffff',  # Texto botones blanco
        'success': '#107c10',      # Verde
        'warning': '#ff8c00',      # Naranja
        'error': '#d13438',        # Rojo
        'info': '#0078d4',         # Azul
        'surface': '#ffffff',      # Superficies
        'surface_hover': '#f3f3f3' # Superficies hover
    }

    # Tema Windows 11 Dark MEJORADO
    WIN11_DARK = {
        'bg': '#1f1f1f',           # Fondo más profundo
        'fg': '#ffffff',           # Texto principal
        'select_bg': '#0078d4',     # Selección
        'select_fg': '#ffffff',     # Texto seleccionado
        'accent': '#0078d4',        # Azul consistente
        'accent_hover': '#106ebe',  # Hover
        'accent_light': '#4fc3f7',  # Azul claro
        'sidebar_bg': '#2a2a2a',    # Barra lateral más clara
        'tab_bg': '#333333',       # Pestañas más definidas
        'tab_active': '#1f1f1f',   # Pestaña activa
        'border': '#484848',       # Bordes más visibles
        'button_bg': '#0078d4',    # Botones
        'button_hover': '#106ebe', # Hover
        'button_text': '#ffffff',  # Texto botones blanco
        'success': '#16c60c',      # Verde brillante
        'warning': '#ffb900',      # Amarillo brillante
        'error': '#e74856',        # Rojo brillante
        'info': '#0078d4',         # Azul
        'surface': '#2a2a2a',      # Superficies
        'surface_hover': '#333333' # Superficies hover
    }

class XUIManagerGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("⚡ XUI Database Manager - Gaming Terminal")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)

        # Colores gaming terminal
        self.colors = {
            'bg': '#0d1117',           # Fondo negro GitHub
            'fg': '#c9d1d9',           # Texto gris claro
            'nvidia_green': '#76b900', # Verde NVIDIA
            'rog_red': '#ff0040',      # Rojo ROG
            'accent': '#58a6ff',       # Azul GitHub
            'warning': '#f85149',      # Rojo advertencia
            'success': '#3fb950',      # Verde éxito
            'surface': '#161b22',      # Superficie elevada
            'border': '#30363d'        # Bordes
        }

        # Configurar estilo gaming terminal
        self.setup_gaming_style()

        self.db = DatabaseManager()
        self.tmdb = TMDBManager("201066b4b17391d478e55247f43eed64")
        self.is_connected = False

        # Auto-refresh
        self.auto_refresh_enabled = False
        self.refresh_interval = 30  # segundos

        # Variables para selección manual y memoria
        self.current_selection_data = {}
        self.current_tmdb_id = None
        self.saved_selections = {}
        self.movie_recommendations = {}

        # Variables para la pestaña unificada
        self.unified_duplicates_data = {}
        self.unified_selection_count = 0

        self.setup_ui()

    def setup_gaming_style(self):
        """Configurar estilo gaming terminal simple"""
        # Configurar ventana principal
        self.root.configure(bg=self.colors['bg'])

        # Fuentes gaming
        self.font_mono = ('Consolas', 10)
        self.font_mono_bold = ('Consolas', 10, 'bold')
        self.font_mono_large = ('Consolas', 12, 'bold')
        self.font_main = ('Consolas', 9)

        # No usar ttk styles complicados, solo tkinter básico

    def log_message(self, message, color='fg'):
        """Función para logging con colores gaming - ULTRA MEJORADA"""
        try:
            if hasattr(self, 'log_text') and self.log_text:
                timestamp = time.strftime("%H:%M:%S")
                color_code = self.colors.get(color, self.colors['fg'])

                # Configurar tag de color con más propiedades
                tag_name = f"color_{color}"
                self.log_text.tag_configure(tag_name,
                                          foreground=color_code,
                                          font=self.font_mono)

                # Insertar mensaje con mejor formato
                full_message = f"[{timestamp}] {message}\n"
                self.log_text.insert('end', full_message, tag_name)
                self.log_text.see('end')

                # Forzar actualización inmediata múltiple para garantizar visibilidad
                try:
                    self.log_text.update_idletasks()
                    self.log_text.update()
                    self.root.update_idletasks()
                    self.root.update()
                except:
                    pass

                # Mantener solo las últimas 1000 líneas para rendimiento
                try:
                    lines = self.log_text.get('1.0', 'end').count('\n')
                    if lines > 1000:
                        self.log_text.delete('1.0', '100.0')
                except:
                    pass
            else:
                # Fallback: print to console if widget not available
                print(f"[LOG] {message}")
        except Exception as e:
            # Ultimate fallback
            print(f"[LOG ERROR] {message} (Error: {e})")

    def log_operation_start(self, operation_name):
        """Iniciar logging de operación con separador visual"""
        separator = "═" * 50
        self.log_message(separator, 'accent')
        self.log_message(f"🚀 INICIANDO: {operation_name}", 'nvidia_green')
        self.log_message(separator, 'accent')

    def log_operation_end(self, operation_name, success=True):
        """Finalizar logging de operación"""
        separator = "═" * 50
        if success:
            self.log_message(f"✅ COMPLETADO: {operation_name}", 'success')
        else:
            self.log_message(f"❌ FALLIDO: {operation_name}", 'warning')
        self.log_message(separator, 'accent')

    def log_progress(self, current, total, item_name=""):
        """Mostrar progreso en tiempo real"""
        percentage = (current / total * 100) if total > 0 else 0
        progress_bar = "█" * int(percentage / 5) + "░" * (20 - int(percentage / 5))
        self.log_message(f"📊 [{progress_bar}] {current}/{total} ({percentage:.1f}%) {item_name}", 'accent')

    def setup_menu(self):
        """Configurar menú de la aplicación"""
        menubar = tk.Menu(self.root, bg=self.current_theme['bg'], fg=self.current_theme['fg'])
        self.root.config(menu=menubar)

        # Menú de temas
        theme_menu = tk.Menu(menubar, tearoff=0, bg=self.current_theme['bg'], fg=self.current_theme['fg'])
        menubar.add_cascade(label="🎨 Temas", menu=theme_menu)

        theme_menu.add_command(label="🌙 VS Code Dark", command=lambda: self.change_theme("VS Code Dark"))
        theme_menu.add_command(label="☀️ Windows 11 Light", command=lambda: self.change_theme("Windows 11 Light"))
        theme_menu.add_command(label="🌃 Windows 11 Dark", command=lambda: self.change_theme("Windows 11 Dark"))

        # Menú de ayuda
        help_menu = tk.Menu(menubar, tearoff=0, bg=self.current_theme['bg'], fg=self.current_theme['fg'])
        menubar.add_cascade(label="❓ Ayuda", menu=help_menu)

        help_menu.add_command(label="📖 Acerca de", command=self.show_about)

    def show_about(self):
        """Mostrar información sobre la aplicación"""
        about_text = """🎬 XUI Database Manager - Modern Edition

Versión: 2.0 (Modern Theme)
Desarrollado para gestión avanzada de bases de datos XUI

Características:
• 🎨 Temas modernos (VS Code, Windows 11)
• 🎬 Gestión unificada de duplicados
• ⭐ Prioridades avanzadas (4K > 60FPS > FHD/HD > Direct)
• 🧠 Selección inteligente automática
• 💾 Sistema de memoria integrado
• 📊 Estadísticas en tiempo real

Temas disponibles:
🌙 VS Code Dark - Inspirado en Visual Studio Code
☀️ Windows 11 Light - Estilo Windows 11 claro
🌃 Windows 11 Dark - Estilo Windows 11 oscuro

¡Disfruta de la gestión de duplicados más avanzada!"""

        messagebox.showinfo("Acerca de XUI Database Manager", about_text)

    def setup_modern_theme(self):
        """Configurar tema moderno para la aplicación"""
        # Configurar el estilo de la ventana principal
        self.root.configure(bg=self.current_theme['bg'])

        # Crear y configurar el estilo ttk
        self.style = ttk.Style()

        # Usar tema base más moderno
        try:
            available_themes = self.style.theme_names()
            print(f"Temas disponibles: {available_themes}")

            # Preferir temas más modernos
            if 'vista' in available_themes:
                self.style.theme_use('vista')
            elif 'winnative' in available_themes:
                self.style.theme_use('winnative')
            elif 'clam' in available_themes:
                self.style.theme_use('clam')
            else:
                self.style.theme_use('default')

            print(f"Tema base seleccionado: {self.style.theme_use()}")
        except Exception as e:
            print(f"Error configurando tema base: {e}")

        # Configurar estilos personalizados
        self.configure_modern_styles()

        # Configurar fuentes modernas
        self.setup_modern_fonts()

        # Aplicar configuraciones adicionales
        self.apply_modern_overrides()

    def configure_modern_styles(self):
        """Configurar estilos modernos para widgets ttk - VERSIÓN SIMPLIFICADA"""
        theme = self.current_theme

        # Frame moderno
        self.style.configure('Modern.TFrame',
                           background=theme['bg'],
                           relief='flat',
                           borderwidth=0)

        # Label moderno
        self.style.configure('Modern.TLabel',
                           background=theme['bg'],
                           foreground=theme['fg'],
                           font=('Segoe UI', 10))

        # LabelFrame moderno
        self.style.configure('Modern.TLabelframe',
                           background=theme['bg'],
                           foreground=theme['fg'],
                           borderwidth=2,
                           relief='flat',
                           bordercolor=theme['accent'],
                           padding=[10, 10])

        self.style.configure('Modern.TLabelframe.Label',
                           background=theme['bg'],
                           foreground=theme['accent'],
                           font=('Segoe UI', 11, 'bold'))

        # Botón moderno estándar - MEJORADO
        self.style.configure('Modern.TButton',
                           background=theme['surface'],
                           foreground=theme['fg'],
                           font=('Segoe UI', 10, 'normal'),
                           relief='flat',
                           borderwidth=1,
                           bordercolor=theme['border'],
                           focuscolor='none',
                           padding=[12, 8])

        self.style.map('Modern.TButton',
                      background=[('active', theme['surface_hover']),
                                ('pressed', theme['accent']),
                                ('disabled', theme['bg'])],
                      foreground=[('active', theme['fg']),
                                ('pressed', theme['button_text']),
                                ('disabled', theme['border'])],
                      bordercolor=[('active', theme['accent']),
                                 ('pressed', theme['accent'])])

        # Botón de acento principal - ULTRA MODERNO
        self.style.configure('Accent.TButton',
                           background=theme['accent'],
                           foreground=theme['button_text'],
                           font=('Segoe UI', 10, 'bold'),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none',
                           padding=[15, 10])

        self.style.map('Accent.TButton',
                      background=[('active', theme['accent_hover']),
                                ('pressed', theme['accent_light']),
                                ('disabled', theme['border'])],
                      foreground=[('active', theme['button_text']),
                                ('pressed', theme['button_text']),
                                ('disabled', theme['fg'])])

        # Botón de éxito
        self.style.configure('Success.TButton',
                           background=theme['success'],
                           foreground=theme['button_text'],
                           font=('Segoe UI', 10, 'bold'),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none',
                           padding=[12, 8])

        self.style.map('Success.TButton',
                      background=[('active', '#0d7a07'),
                                ('pressed', '#0a5d05')])

        # Botón de peligro
        self.style.configure('Danger.TButton',
                           background=theme['error'],
                           foreground=theme['button_text'],
                           font=('Segoe UI', 10, 'bold'),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none',
                           padding=[12, 8])

        self.style.map('Danger.TButton',
                      background=[('active', '#c33c4a'),
                                ('pressed', '#a02d3a')])

        # Botón secundario
        self.style.configure('Secondary.TButton',
                           background=theme['bg'],
                           foreground=theme['accent'],
                           font=('Segoe UI', 10, 'normal'),
                           relief='flat',
                           borderwidth=1,
                           bordercolor=theme['accent'],
                           focuscolor='none',
                           padding=[12, 8])

        self.style.map('Secondary.TButton',
                      background=[('active', theme['accent']),
                                ('pressed', theme['accent_hover'])],
                      foreground=[('active', theme['button_text']),
                                ('pressed', theme['button_text'])])

        # Entry moderno
        self.style.configure('Modern.TEntry',
                           fieldbackground=theme['surface'],
                           foreground=theme['fg'],
                           bordercolor=theme['border'],
                           insertcolor=theme['accent'],
                           relief='flat',
                           borderwidth=2,
                           padding=[8, 6])

        self.style.map('Modern.TEntry',
                      bordercolor=[('focus', theme['accent']),
                                 ('active', theme['accent_light'])],
                      fieldbackground=[('focus', theme['bg'])])

        # Combobox moderno
        self.style.configure('Modern.TCombobox',
                           fieldbackground=theme['surface'],
                           foreground=theme['fg'],
                           bordercolor=theme['border'],
                           relief='flat',
                           borderwidth=2,
                           padding=[8, 6],
                           arrowcolor=theme['accent'])

        self.style.map('Modern.TCombobox',
                      bordercolor=[('focus', theme['accent']),
                                 ('active', theme['accent_light'])],
                      fieldbackground=[('focus', theme['bg'])],
                      arrowcolor=[('active', theme['accent_hover'])])

        # Notebook ultra moderno
        self.style.configure('Modern.TNotebook',
                           background=theme['bg'],
                           borderwidth=0,
                           tabmargins=[2, 5, 2, 0])

        self.style.configure('Modern.TNotebook.Tab',
                           background=theme['surface'],
                           foreground=theme['fg'],
                           padding=[20, 12],
                           borderwidth=0,
                           focuscolor='none',
                           relief='flat',
                           font=('Segoe UI', 10, 'normal'))

        self.style.map('Modern.TNotebook.Tab',
                      background=[('selected', theme['accent']),
                                ('active', theme['surface_hover']),
                                ('!active', theme['surface'])],
                      foreground=[('selected', theme['button_text']),
                                ('active', theme['fg']),
                                ('!active', theme['fg'])])

        # Treeview ultra moderno
        self.style.configure('Modern.Treeview',
                           background=theme['bg'],
                           foreground=theme['fg'],
                           fieldbackground=theme['bg'],
                           borderwidth=0,
                           relief='flat',
                           font=('Segoe UI', 9),
                           rowheight=28)

        self.style.configure('Modern.Treeview.Heading',
                           background=theme['surface'],
                           foreground=theme['fg'],
                           relief='flat',
                           borderwidth=0,
                           font=('Segoe UI', 10, 'bold'),
                           padding=[8, 8])

        self.style.map('Modern.Treeview',
                      background=[('selected', theme['accent']),
                                ('focus', theme['accent'])],
                      foreground=[('selected', theme['button_text']),
                                ('focus', theme['button_text'])])

        self.style.map('Modern.Treeview.Heading',
                      background=[('active', theme['accent_light']),
                                ('pressed', theme['accent'])],
                      foreground=[('active', theme['button_text']),
                                ('pressed', theme['button_text'])])

        # Scrollbar oscuro
        self.style.configure('Dark.Vertical.TScrollbar',
                           background=theme['sidebar_bg'],
                           borderwidth=0,
                           arrowcolor=theme['fg'],
                           troughcolor=theme['bg'])

        self.style.configure('Dark.Horizontal.TScrollbar',
                           background=theme['sidebar_bg'],
                           borderwidth=0,
                           arrowcolor=theme['fg'],
                           troughcolor=theme['bg'])

        # Estilo para Frame
        self.style.configure('Modern.TFrame',
                           background=theme['bg'],
                           borderwidth=0)

        # Estilo para LabelFrame
        self.style.configure('Modern.TLabelframe',
                           background=theme['bg'],
                           foreground=theme['fg'],
                           borderwidth=1,
                           relief='solid',
                           bordercolor=theme['border'])

        self.style.configure('Modern.TLabelframe.Label',
                           background=theme['bg'],
                           foreground=theme['accent'],
                           font=('Segoe UI', 10, 'bold'))

        # Estilo para Button
        self.style.configure('Modern.TButton',
                           background=theme['button_bg'],
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none',
                           padding=[15, 8])

        self.style.map('Modern.TButton',
                      background=[('active', theme['button_hover']),
                                ('pressed', theme['accent'])])

        # Estilo para Button de acento
        self.style.configure('Accent.TButton',
                           background=theme['accent'],
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none',
                           padding=[15, 8])

        self.style.map('Accent.TButton',
                      background=[('active', theme['accent_hover']),
                                ('pressed', theme['button_bg'])])

        # Estilo para Treeview
        self.style.configure('Modern.Treeview',
                           background=theme['bg'],
                           foreground=theme['fg'],
                           fieldbackground=theme['bg'],
                           borderwidth=1,
                           relief='solid',
                           bordercolor=theme['border'])

        self.style.configure('Modern.Treeview.Heading',
                           background=theme['sidebar_bg'],
                           foreground=theme['fg'],
                           borderwidth=1,
                           relief='solid',
                           bordercolor=theme['border'])

        self.style.map('Modern.Treeview',
                      background=[('selected', theme['select_bg'])],
                      foreground=[('selected', theme['select_fg'])])

        self.style.map('Modern.Treeview.Heading',
                      background=[('active', theme['accent_hover'])])

    def setup_modern_fonts(self):
        """Configurar fuentes modernas"""
        # Fuentes principales
        self.font_main = ('Segoe UI', 10)
        self.font_heading = ('Segoe UI', 12, 'bold')
        self.font_small = ('Segoe UI', 9)
        self.font_code = ('Consolas', 10)

        # Configurar fuentes por defecto
        self.root.option_add('*Font', self.font_main)

    def apply_modern_overrides(self):
        """Aplicar configuraciones adicionales para forzar el tema moderno"""
        theme = self.current_theme

        # Configurar opciones globales de tkinter
        self.root.option_add('*Background', theme['bg'])
        self.root.option_add('*Foreground', theme['fg'])
        self.root.option_add('*selectBackground', theme['select_bg'])
        self.root.option_add('*selectForeground', theme['select_fg'])
        self.root.option_add('*insertBackground', theme['fg'])

        # Configurar estilos base de ttk más agresivamente
        base_widgets = ['TFrame', 'TLabel', 'TButton', 'TEntry', 'TCombobox', 'TCheckbutton', 'TRadiobutton']

        for widget in base_widgets:
            try:
                if widget == 'TFrame':
                    self.style.configure(widget, background=theme['bg'], relief='flat', borderwidth=0)
                elif widget == 'TLabel':
                    self.style.configure(widget, background=theme['bg'], foreground=theme['fg'])
                elif widget == 'TButton':
                    self.style.configure(widget,
                                       background=theme['button_bg'],
                                       foreground='white',
                                       relief='flat',
                                       borderwidth=0,
                                       focuscolor='none')
                    self.style.map(widget,
                                  background=[('active', theme['button_hover']),
                                            ('pressed', theme['accent'])])
                elif widget == 'TEntry':
                    self.style.configure(widget,
                                       fieldbackground=theme['bg'],
                                       foreground=theme['fg'],
                                       bordercolor=theme['border'],
                                       insertcolor=theme['fg'],
                                       relief='solid',
                                       borderwidth=1)
                    self.style.map(widget, bordercolor=[('focus', theme['accent'])])
                elif widget == 'TCombobox':
                    self.style.configure(widget,
                                       fieldbackground=theme['bg'],
                                       foreground=theme['fg'],
                                       bordercolor=theme['border'],
                                       relief='solid',
                                       borderwidth=1)
                    self.style.map(widget, bordercolor=[('focus', theme['accent'])])
            except Exception as e:
                print(f"Error configurando {widget}: {e}")

    def change_theme(self, theme_name):
        """Cambiar el tema de la aplicación"""
        if theme_name == "VS Code Dark":
            self.current_theme = ModernTheme.VSCODE_DARK
        elif theme_name == "Windows 11 Light":
            self.current_theme = ModernTheme.WIN11_LIGHT
        elif theme_name == "Windows 11 Dark":
            self.current_theme = ModernTheme.WIN11_DARK

        # Reconfigurar tema
        self.setup_modern_theme()

        # Actualizar widgets existentes
        self.update_existing_widgets()

    def update_existing_widgets(self):
        """Actualizar widgets existentes con el nuevo tema"""
        try:
            # Actualizar ventana principal
            self.root.configure(bg=self.current_theme['bg'])

            # Actualizar status label
            if hasattr(self, 'status_label'):
                if self.is_connected:
                    self.status_label.configure(foreground=self.current_theme['success'])
                else:
                    self.status_label.configure(foreground=self.current_theme['error'])

            # Actualizar Text widgets
            text_widgets = []
            if hasattr(self, 'unified_stats_text'):
                text_widgets.append(self.unified_stats_text)

            for text_widget in text_widgets:
                text_widget.configure(
                    bg=self.current_theme['bg'],
                    fg=self.current_theme['fg'],
                    insertbackground=self.current_theme['fg'],
                    selectbackground=self.current_theme['select_bg'],
                    selectforeground=self.current_theme['select_fg']
                )
        except Exception as e:
            print(f"Error actualizando widgets: {e}")

    def setup_ui(self):
        """Configurar interfaz gaming terminal simple"""
        # Header gaming
        header_frame = tk.Frame(self.root, bg=self.colors['bg'], height=50)
        header_frame.pack(fill='x', padx=10, pady=5)
        header_frame.pack_propagate(False)

        # Título gaming
        title_label = tk.Label(header_frame,
                              text="⚡ XUI DATABASE MANAGER - GAMING TERMINAL ⚡",
                              font=self.font_mono_large,
                              bg=self.colors['bg'],
                              fg=self.colors['nvidia_green'])
        title_label.pack(pady=10)

        # Status bar
        status_frame = tk.Frame(self.root, bg=self.colors['surface'], height=25)
        status_frame.pack(fill='x', padx=10, pady=(0, 5))
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(status_frame,
                                   text="[DISCONNECTED] Ready to connect...",
                                   font=self.font_mono,
                                   bg=self.colors['surface'],
                                   fg=self.colors['rog_red'])
        self.status_label.pack(side='left', padx=10, pady=2)

        # Main area
        main_frame = tk.Frame(self.root, bg=self.colors['bg'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Setup panels gaming style
        self.setup_gaming_connection(main_frame)
        self.setup_gaming_operations(main_frame)

    def setup_gaming_connection(self, parent):
        """Panel de conexión estilo gaming terminal"""
        # Left panel - Connection
        left_frame = tk.Frame(parent, bg=self.colors['surface'], width=350)
        left_frame.pack(side='left', fill='y', padx=(0, 10))
        left_frame.pack_propagate(False)

        # Connection header
        conn_label = tk.Label(left_frame,
                            text="═══ CONNECTION ═══",
                            font=self.font_mono_bold,
                            bg=self.colors['surface'],
                            fg=self.colors['nvidia_green'])
        conn_label.pack(pady=(10, 5))

        # Connection fields - simple and direct
        fields = [
            ("Host:", "**************"),
            ("User:", "infest84"),
            ("Pass:", "GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP"),
            ("DB:", "xui"),
            ("Port:", "3306")
        ]

        self.connection_vars = {}
        for label, default in fields:
            frame = tk.Frame(left_frame, bg=self.colors['surface'])
            frame.pack(fill='x', padx=10, pady=2)

            tk.Label(frame, text=label, font=self.font_mono,
                    bg=self.colors['surface'], fg=self.colors['fg'],
                    width=6, anchor='w').pack(side='left')

            var = tk.StringVar(value=default)
            entry = tk.Entry(frame, textvariable=var, font=self.font_mono,
                           bg=self.colors['bg'], fg=self.colors['fg'],
                           insertbackground=self.colors['nvidia_green'],
                           relief='flat', bd=1)
            entry.pack(side='right', fill='x', expand=True)
            self.connection_vars[label.rstrip(':')] = var

        # Connect button - gaming style
        self.connect_btn = tk.Button(left_frame,
                                   text="⚡ CONNECT",
                                   font=self.font_mono_bold,
                                   bg=self.colors['nvidia_green'],
                                   fg='black',
                                   relief='flat',
                                   command=self.connect_database_gaming)
        self.connect_btn.pack(fill='x', padx=10, pady=10)

        # Operations section
        ops_label = tk.Label(left_frame,
                           text="═══ OPERATIONS ═══",
                           font=self.font_mono_bold,
                           bg=self.colors['surface'],
                           fg=self.colors['rog_red'])
        ops_label.pack(pady=(20, 5))

        # Quick operation buttons
        operations = [
            ("🧪 Test Database", self.test_database_gaming, self.colors['accent']),
            ("🌐 Network Diagnostic", self.network_diagnostic_gaming, self.colors['accent']),
            ("🎬 Load TMDB Duplicates", self.load_unified_tmdb_duplicates_gaming, self.colors['accent']),
            ("🔗 Load Symlink Movies", self.load_symlink_movies_gaming, self.colors['success']),
            ("🎯 Smart Selection", self.smart_select_duplicates_gaming, self.colors['success']),
            ("👁️ Preview Cleanup", self.preview_cleanup_gaming, self.colors['accent']),
            ("⭐ Advanced Cleanup", self.execute_advanced_cleanup_gaming, self.colors['nvidia_green']),
            ("🔥 Mass Cleanup", self.mass_cleanup_gaming, self.colors['rog_red']),
            ("📊 Show Statistics", self.show_statistics_gaming, self.colors['accent']),
            ("🔄 Refresh Data", self.refresh_data_gaming, self.colors['success'])
        ]

        for text, command, color in operations:
            btn = tk.Button(left_frame,
                          text=text,
                          font=self.font_mono,
                          bg=color,
                          fg='black' if color == self.colors['nvidia_green'] else 'white',
                          relief='flat',
                          command=command)
            btn.pack(fill='x', padx=10, pady=2)

        # Series Management Section
        series_frame = tk.Frame(left_frame, bg=self.colors['surface'])
        series_frame.pack(fill='x', pady=10)

        tk.Label(series_frame,
                text="═══ SERIES MANAGEMENT ═══",
                font=self.font_mono_bold,
                bg=self.colors['surface'],
                fg=self.colors['nvidia_green']).pack(pady=5)

        # Series buttons
        tk.Button(series_frame, text="📺 Load Series & Episodes",
                 bg=self.colors['accent'], fg='white', font=self.font_mono,
                 relief='flat', command=self.load_series_episodes).pack(fill='x', pady=2)

        tk.Button(series_frame, text="🔍 Find Duplicate Episodes",
                 bg=self.colors['warning'], fg='white', font=self.font_mono,
                 relief='flat', command=self.find_duplicate_episodes).pack(fill='x', pady=2)

        tk.Button(series_frame, text="🗑️ Mass Delete Episodes",
                 bg=self.colors['rog_red'], fg='white', font=self.font_mono,
                 relief='flat', command=self.mass_delete_episodes).pack(fill='x', pady=2)

        tk.Button(series_frame, text="👻 Find Orphaned Episodes",
                 bg=self.colors['rog_red'], fg='white', font=self.font_mono,
                 relief='flat', command=self.find_orphaned_episodes).pack(fill='x', pady=2)

        tk.Button(series_frame, text="📊 Series Statistics",
                 bg=self.colors['nvidia_green'], fg='white', font=self.font_mono,
                 relief='flat', command=self.show_series_statistics).pack(fill='x', pady=2)

        # M3U Management Section
        m3u_frame = tk.Frame(left_frame, bg=self.colors['surface'])
        m3u_frame.pack(fill='x', pady=10)

        tk.Label(m3u_frame,
                text="═══ M3U MANAGEMENT ═══",
                font=self.font_mono_bold,
                bg=self.colors['surface'],
                fg=self.colors['accent']).pack(pady=5)

        # M3U buttons
        tk.Button(m3u_frame, text="📁 Load M3U File",
                 bg=self.colors['accent'], fg='white', font=self.font_mono,
                 relief='flat', command=self.load_m3u_file).pack(fill='x', pady=2)

        tk.Button(m3u_frame, text="🌐 Load M3U URL",
                 bg=self.colors['accent'], fg='white', font=self.font_mono,
                 relief='flat', command=self.load_m3u_url).pack(fill='x', pady=2)

        tk.Button(m3u_frame, text="🔍 Analyze M3U vs DB",
                 bg=self.colors['nvidia_green'], fg='white', font=self.font_mono,
                 relief='flat', command=self.analyze_m3u_database).pack(fill='x', pady=2)

    def setup_gaming_operations(self, parent):
        """Panel de operaciones estilo gaming terminal"""
        # Right panel - Split between data view and terminal
        right_frame = tk.Frame(parent, bg=self.colors['bg'])
        right_frame.pack(side='right', fill='both', expand=True)

        # Data view section (top half)
        data_header = tk.Frame(right_frame, bg=self.colors['surface'], height=30)
        data_header.pack(fill='x', pady=(0, 2))
        data_header.pack_propagate(False)

        tk.Label(data_header,
                text="═══ DATA VIEW ═══",
                font=self.font_mono_bold,
                bg=self.colors['surface'],
                fg=self.colors['nvidia_green']).pack(pady=5)

        # Data display area - FIXED HEIGHT for data view
        self.data_frame = tk.Frame(right_frame, bg=self.colors['bg'], height=300)
        self.data_frame.pack(fill='x', pady=(0, 5))
        self.data_frame.pack_propagate(False)

        # Create Treeview for data management with GAMING COLORS
        columns = ("Sel", "TMDB ID", "Título", "Total", "4K", "60FPS", "FHD", "HD", "SD", "Symlinks", "Direct", "Otros", "Recomendación")

        # Create style for gaming treeview
        style = ttk.Style()
        style.theme_use('clam')

        # Configure gaming treeview style
        style.configure("Gaming.Treeview",
                       background=self.colors['bg'],           # Fondo negro como terminal
                       foreground=self.colors['fg'],           # Texto gris claro
                       fieldbackground=self.colors['bg'],      # Fondo de campos negro
                       borderwidth=1,
                       relief="flat")

        style.configure("Gaming.Treeview.Heading",
                       background=self.colors['surface'],      # Header gris oscuro
                       foreground=self.colors['nvidia_green'], # Texto verde NVIDIA
                       borderwidth=1,
                       relief="flat",
                       font=self.font_mono_bold)

        # Configure selection colors
        style.map("Gaming.Treeview",
                 background=[('selected', self.colors['accent'])],      # Selección azul
                 foreground=[('selected', 'white')])                   # Texto blanco al seleccionar

        style.map("Gaming.Treeview.Heading",
                 background=[('active', self.colors['nvidia_green'])],  # Header activo verde
                 foreground=[('active', 'black')])                     # Texto negro en header activo

        self.unified_duplicates_tree = ttk.Treeview(self.data_frame,
                                                   columns=columns,
                                                   show="headings",
                                                   height=12,
                                                   selectmode="extended",
                                                   style="Gaming.Treeview")

        # Configure columns like before
        for col in columns:
            self.unified_duplicates_tree.heading(col, text=col)
            if col == "Sel":
                self.unified_duplicates_tree.column(col, width=40, anchor="center")
            elif col == "TMDB ID":
                self.unified_duplicates_tree.column(col, width=80, anchor="center")
            elif col == "Título":
                self.unified_duplicates_tree.column(col, width=200, anchor="w")
            elif col in ["Total", "4K", "60FPS", "FHD", "HD", "SD", "Symlinks", "Direct", "Otros"]:
                self.unified_duplicates_tree.column(col, width=60, anchor="center")
            else:
                self.unified_duplicates_tree.column(col, width=150, anchor="w")

        # Gaming scrollbars
        style.configure("Gaming.Vertical.TScrollbar",
                       background=self.colors['surface'],
                       troughcolor=self.colors['bg'],
                       borderwidth=1,
                       arrowcolor=self.colors['nvidia_green'],
                       darkcolor=self.colors['surface'],
                       lightcolor=self.colors['accent'])

        style.configure("Gaming.Horizontal.TScrollbar",
                       background=self.colors['surface'],
                       troughcolor=self.colors['bg'],
                       borderwidth=1,
                       arrowcolor=self.colors['nvidia_green'],
                       darkcolor=self.colors['surface'],
                       lightcolor=self.colors['accent'])

        v_scrollbar = ttk.Scrollbar(self.data_frame, orient="vertical",
                                   command=self.unified_duplicates_tree.yview,
                                   style="Gaming.Vertical.TScrollbar")
        h_scrollbar = ttk.Scrollbar(self.data_frame, orient="horizontal",
                                   command=self.unified_duplicates_tree.xview,
                                   style="Gaming.Horizontal.TScrollbar")
        self.unified_duplicates_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.unified_duplicates_tree.pack(side="left", fill="both", expand=True, padx=(5, 0), pady=5)
        v_scrollbar.pack(side="right", fill="y", pady=5)
        h_scrollbar.pack(side="bottom", fill="x", padx=5)

        # Configure gaming tags for different data types
        self.unified_duplicates_tree.tag_configure("selected_item",
                                                  background=self.colors['nvidia_green'],
                                                  foreground='black')

        self.unified_duplicates_tree.tag_configure("high_quality",
                                                  background=self.colors['bg'],
                                                  foreground=self.colors['nvidia_green'])

        self.unified_duplicates_tree.tag_configure("medium_quality",
                                                  background=self.colors['bg'],
                                                  foreground=self.colors['accent'])

        self.unified_duplicates_tree.tag_configure("low_quality",
                                                  background=self.colors['bg'],
                                                  foreground=self.colors['fg'])

        self.unified_duplicates_tree.tag_configure("warning_item",
                                                  background=self.colors['bg'],
                                                  foreground=self.colors['warning'])

        # Bind click events for manual selection (like before)
        self.unified_duplicates_tree.bind("<Button-1>", self.on_treeview_click)
        self.unified_duplicates_tree.bind("<Double-1>", self.on_treeview_double_click)

        # Store selected items for manual management
        self.selected_items = set()

        # Control buttons for data management
        control_frame = tk.Frame(self.data_frame, bg=self.colors['bg'])
        control_frame.pack(fill='x', padx=5, pady=2)

        # Selection buttons
        tk.Button(control_frame, text="☑ Select All",
                 bg=self.colors['success'], fg='white', font=self.font_mono,
                 relief='flat', command=self.select_all_unified_duplicates).pack(side='left', padx=2)

        tk.Button(control_frame, text="☐ Deselect All",
                 bg=self.colors['accent'], fg='white', font=self.font_mono,
                 relief='flat', command=self.deselect_all_unified_duplicates).pack(side='left', padx=2)

        tk.Button(control_frame, text="🔍 Show Details",
                 bg=self.colors['nvidia_green'], fg='black', font=self.font_mono,
                 relief='flat', command=self.show_unified_details).pack(side='left', padx=2)

        tk.Button(control_frame, text="⚙️ Manual Selection",
                 bg=self.colors['rog_red'], fg='white', font=self.font_mono,
                 relief='flat', command=self.manual_selection).pack(side='left', padx=2)



        # Terminal section (bottom half) - MOVED HERE FROM WRONG LOCATION
        terminal_header = tk.Frame(right_frame, bg=self.colors['surface'], height=30)
        terminal_header.pack(fill='x', pady=(5, 2))
        terminal_header.pack_propagate(False)

        tk.Label(terminal_header,
                text="═══ TERMINAL OUTPUT ═══",
                font=self.font_mono_bold,
                bg=self.colors['surface'],
                fg=self.colors['accent']).pack(pady=5)

        # Terminal text area - GUARANTEED SPACE AND VISIBILITY
        terminal_space = tk.Frame(right_frame, bg=self.colors['bg'], height=250)
        terminal_space.pack(fill='both', expand=True, padx=5, pady=5)
        terminal_space.pack_propagate(False)

        self.log_text = scrolledtext.ScrolledText(terminal_space,
                                                font=self.font_mono,
                                                bg=self.colors['bg'],
                                                fg=self.colors['fg'],
                                                insertbackground=self.colors['nvidia_green'],
                                                selectbackground=self.colors['accent'],
                                                selectforeground='white',
                                                relief='solid',
                                                bd=2,
                                                borderwidth=2,
                                                highlightthickness=2,
                                                highlightcolor=self.colors['nvidia_green'],
                                                highlightbackground=self.colors['border'])
        self.log_text.pack(fill='both', expand=True, padx=2, pady=2)

        # Force widget update before sending messages
        self.root.update_idletasks()
        self.root.update()

        # Welcome messages - ENHANCED VISIBILITY
        self.log_message("═" * 70, 'nvidia_green')
        self.log_message("⚡ XUI DATABASE MANAGER - GAMING TERMINAL INITIALIZED ⚡", 'nvidia_green')
        self.log_message("═" * 70, 'nvidia_green')
        self.log_message("🎮 Gaming colors: NVIDIA Green + ASUS ROG Red", 'rog_red')
        self.log_message("🚀 Ready for high-performance database operations", 'accent')
        self.log_message("", 'fg')
        self.log_message("🎮 TERMINAL OUTPUT IS WORKING AND VISIBLE!", 'nvidia_green')
        self.log_message("📊 Data view above, terminal logs below", 'accent')
        self.log_message("✅ Layout initialized successfully", 'success')
        self.log_message("", 'fg')
        self.log_message("💡 Use the buttons on the left to perform operations", 'accent')
        self.log_message("📝 All operations will show real-time logs here", 'accent')
        self.log_message("═" * 70, 'fg')

        # Initialize data view
        self.show_data_welcome()

        # Store duplicates data for management (like before)
        self.current_duplicates = []
        self.selected_for_deletion = []
        self.unified_duplicates_data = []  # Store full data for treeview

        # Force final update
        self.root.update_idletasks()
        self.root.update()

    def check_database_connection(self):
        """Check database connection status with multiple methods"""
        try:
            if not self.db or not self.db.connection:
                return False

            # Method 1: is_connected (if available)
            if hasattr(self.db.connection, 'is_connected'):
                try:
                    return self.db.connection.is_connected()
                except:
                    pass

            # Method 2: ping (if available)
            if hasattr(self.db.connection, 'ping'):
                try:
                    self.db.connection.ping(reconnect=False)
                    return True
                except:
                    pass

            # Method 3: simple query test
            try:
                cursor = self.db.connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                return True
            except:
                pass

            return False

        except Exception:
            return False

    # Treeview management functions (like before)
    def on_treeview_click(self, event):
        """Handle treeview click for selection toggle with gaming colors"""
        item = self.unified_duplicates_tree.identify('item', event.x, event.y)
        column = self.unified_duplicates_tree.identify('column', event.x, event.y)

        if item and column == '#1':  # Selection column
            current_values = list(self.unified_duplicates_tree.item(item, 'values'))
            current_tags = self.unified_duplicates_tree.item(item, 'tags')

            if current_values[0] == "☐":
                current_values[0] = "☑"
                self.selected_items.add(item)
                # Apply selected gaming color
                self.unified_duplicates_tree.item(item, values=current_values, tags=("selected_item",))
                self.log_message(f"✅ Selected: {current_values[2]}", 'success')
            else:
                current_values[0] = "☐"
                self.selected_items.discard(item)
                # Restore original gaming color
                original_tag = self.get_gaming_tag_for_item(current_values)
                self.unified_duplicates_tree.item(item, values=current_values, tags=(original_tag,))
                self.log_message(f"❌ Deselected: {current_values[2]}", 'accent')

    def get_gaming_tag_for_item(self, values):
        """Get appropriate gaming color tag for item based on its data"""
        try:
            quality_4k = int(values[4]) if values[4] else 0
            quality_60fps = int(values[5]) if values[5] else 0
            quality_fhd = int(values[6]) if values[6] else 0
            total_copies = int(values[3]) if values[3] else 0

            if quality_4k > 0:
                return "high_quality"  # Verde NVIDIA para 4K
            elif quality_60fps > 0 or quality_fhd > 0:
                return "medium_quality"  # Azul para calidad media
            elif total_copies > 10:
                return "warning_item"  # Amarillo para muchas copias
            else:
                return "low_quality"  # Gris normal
        except:
            return "low_quality"

    def on_treeview_double_click(self, event):
        """Handle double click to open manual selection for specific movie or episode"""
        item = self.unified_duplicates_tree.identify('item', event.x, event.y)
        if item:
            values = self.unified_duplicates_tree.item(item, 'values')
            columns = self.unified_duplicates_tree['columns']

            # Detect if this is an episode or movie based on column structure
            if 'series_title' in columns and 'season' in columns and 'episode' in columns:
                # This is an episode duplicate
                # Check if we have a select column (newer format) or not (older format)
                if 'select' in columns:
                    # New format with select column: (select, series_title, season, episode, count, episode_ids)
                    series_title = values[1]
                    season_str = values[2]  # Format: "S01"
                    episode_str = values[3]  # Format: "E01"
                    total_copies = values[4]
                    episode_ids = values[5]
                else:
                    # Old format without select column: (series_title, season, episode, count, episode_ids)
                    series_title = values[0]
                    season_str = values[1]  # Format: "S01"
                    episode_str = values[2]  # Format: "E01"
                    total_copies = values[3]
                    episode_ids = values[4]

                # Extract season and episode numbers
                try:
                    season_num = int(season_str[1:]) if season_str.startswith('S') else 0
                    episode_num = int(episode_str[1:]) if episode_str.startswith('E') else 0
                except:
                    season_num = 0
                    episode_num = 0

                self.log_message(f"🎬 Opening manual selection for: {episode_str} ({series_title})", 'nvidia_green')
                self.log_message(f"📊 Episode has {total_copies} copies to manage", 'accent')

                # Get series_id from episode_ids to avoid title search
                # Extract first episode_id and get its series_id
                episode_ids_list = episode_ids.split(',')
                if episode_ids_list:
                    first_episode_id = episode_ids_list[0].strip()
                    # Get series_id from the duplicate episodes data
                    series_id = self.get_series_id_from_episode_data(series_title, season_num, episode_num)

                    # Open gaming manual selection window for this specific episode
                    self.open_gaming_episode_selection(series_id, series_title, season_num, episode_num, episode_str)
                else:
                    self.log_message(f"⚠️ No episode IDs found for {episode_str}", 'warning')

            else:
                # This is a movie duplicate (original logic)
                tmdb_id = values[1]
                title = values[2]
                total_copies = values[3]

                self.log_message(f"🎬 Opening manual selection for: {title} (TMDB {tmdb_id})", 'nvidia_green')
                self.log_message(f"📊 Movie has {total_copies} copies to manage", 'accent')

                # Open gaming manual selection window for this specific movie
                self.open_gaming_manual_selection(tmdb_id, title)

    def select_all_unified_duplicates(self):
        """Select all items in treeview"""
        count = 0
        for item in self.unified_duplicates_tree.get_children():
            current_values = list(self.unified_duplicates_tree.item(item, 'values'))
            current_values[0] = "☑"
            self.unified_duplicates_tree.item(item, values=current_values)
            self.selected_items.add(item)
            count += 1

        self.log_message(f"☑ Selected all {count} items", 'success')

    def deselect_all_unified_duplicates(self):
        """Deselect all items in treeview"""
        count = 0
        for item in self.unified_duplicates_tree.get_children():
            current_values = list(self.unified_duplicates_tree.item(item, 'values'))
            current_values[0] = "☐"
            self.unified_duplicates_tree.item(item, values=current_values)
            count += 1

        self.selected_items.clear()
        self.log_message(f"☐ Deselected all {count} items", 'accent')

    def show_unified_details(self):
        """Show details for selected items"""
        if not self.selected_items:
            self.log_message("❌ No items selected!", 'warning')
            return

        self.log_message(f"🔍 Showing details for {len(self.selected_items)} selected items", 'nvidia_green')

        for item in self.selected_items:
            values = self.unified_duplicates_tree.item(item, 'values')
            tmdb_id = values[1]
            title = values[2]
            total = values[3]
            self.log_message(f"  📽️ {title} (TMDB {tmdb_id}) - {total} copies", 'accent')

    def manual_selection(self):
        """Open manual selection window (like before)"""
        if not self.selected_items:
            self.log_message("❌ No items selected for manual selection!", 'warning')
            return

        self.log_message(f"⚙️ Opening manual selection for {len(self.selected_items)} items", 'rog_red')
        # Here you would open the manual selection window like before
        # For now, just log the action
        for item in self.selected_items:
            values = self.unified_duplicates_tree.item(item, 'values')
            self.log_message(f"  ⚙️ Manual selection available for: {values[2]}", 'accent')

    def show_data_welcome(self):
        """Mostrar mensaje de bienvenida en data view"""
        # Clear treeview and show welcome message in terminal
        for item in self.unified_duplicates_tree.get_children():
            self.unified_duplicates_tree.delete(item)

        self.log_message("🎮 GAMING DATA VIEW READY", 'nvidia_green')
        self.log_message("📊 This treeview will show your data for management:", 'accent')
        self.log_message("  🎬 TMDB Duplicates → Movie groups with multiple copies", 'fg')
        self.log_message("  🎯 Smart Selection → Recommended items for deletion", 'fg')
        self.log_message("  ⭐ Advanced Preview → What will be deleted/kept", 'fg')
        self.log_message("💡 Load data using the buttons on the left panel", 'success')

    def show_data_table(self, title, headers, data, color_scheme='default'):
        """Log table data in terminal (data is now in treeview)"""
        self.log_message(f"📊 {title}", 'nvidia_green' if color_scheme == 'nvidia' else 'rog_red' if color_scheme == 'rog' else 'accent')

        if not data:
            self.log_message("║ No data available", 'warning')
            return

        self.log_message(f"📈 Total items: {len(data)}", 'success')
        self.log_message("🎮 Data loaded in treeview above for management", 'accent')

    # Gaming style functions
    def connect_database_gaming(self):
        """Conectar base de datos estilo gaming"""
        self.log_message("⚡ Initiating database connection...", 'nvidia_green')

        host = self.connection_vars['Host'].get()
        user = self.connection_vars['User'].get()
        password = self.connection_vars['Pass'].get()
        database = self.connection_vars['DB'].get()
        port = int(self.connection_vars['Port'].get())

        self.log_message(f"🔗 Connecting to {host}:{port}/{database} as {user}", 'accent')

        def connect_thread():
            try:
                self.log_message("🔄 Attempting database connection...", 'accent')

                try:
                    # Close any existing connection first
                    if hasattr(self.db, 'connection') and self.db.connection:
                        try:
                            self.db.connection.close()
                            self.log_message("🔄 Closed existing connection", 'accent')
                        except:
                            pass

                    connection_result = self.db.connect(host, user, password, database, port)
                    self.log_message(f"🔍 Connection result: {connection_result}", 'accent')

                    if connection_result:
                        self.is_connected = True
                        self.log_message("🎮 CONNECTION ESTABLISHED!", 'success')
                        self.log_message(f"🔗 Connected to {host}:{port}/{database}", 'accent')

                        # Test the connection with a simple query
                        self.log_message("🧪 Testing connection with simple query...", 'accent')
                        test_result = self.db.execute_query("SELECT 1 as test")
                        if test_result:
                            self.log_message("✅ Database connection verified!", 'success')

                            # Test streams table access
                            self.log_message("🧪 Testing streams table access...", 'accent')
                            streams_test = self.db.execute_query("SELECT COUNT(*) as total FROM streams LIMIT 1")
                            if streams_test:
                                total_streams = streams_test[0].get('total', 0)
                                self.log_message(f"✅ Streams table accessible: {total_streams} total streams", 'success')
                            else:
                                self.log_message("⚠️ Cannot access streams table", 'warning')
                        else:
                            self.log_message("⚠️ Connection established but query test failed", 'warning')
                            self.log_message("🔍 This may indicate permission or table issues", 'warning')

                        self.status_label.configure(text="[CONNECTED] Database online",
                                                  fg=self.colors['success'])
                        self.connect_btn.configure(text="🔌 DISCONNECT",
                                                 bg=self.colors['rog_red'],
                                                 fg='white',
                                                 command=self.disconnect_database_gaming)
                    else:
                        self.log_message("❌ CONNECTION FAILED!", 'warning')
                        self.log_message("🔍 Check credentials and network connectivity", 'warning')
                        self.log_message("💡 Common issues:", 'accent')
                        self.log_message("  • Wrong host/port/credentials", 'accent')
                        self.log_message("  • MySQL server not running", 'accent')
                        self.log_message("  • Firewall blocking connection", 'accent')
                        self.log_message("  • Database doesn't exist", 'accent')
                        self.status_label.configure(text="[ERROR] Connection failed",
                                                  fg=self.colors['warning'])

                except Exception as connection_error:
                    self.log_message(f"💥 CONNECTION EXCEPTION: {str(connection_error)}", 'warning')
                    self.log_message("🔍 Detailed error analysis:", 'accent')

                    error_str = str(connection_error).lower()
                    if "access denied" in error_str:
                        self.log_message("  ❌ Access denied - check username/password", 'warning')
                    elif "unknown database" in error_str:
                        self.log_message("  ❌ Database doesn't exist - check database name", 'warning')
                    elif "can't connect" in error_str or "connection refused" in error_str:
                        self.log_message("  ❌ Can't reach server - check host/port", 'warning')
                    elif "timeout" in error_str:
                        self.log_message("  ❌ Connection timeout - check network/firewall", 'warning')
                    else:
                        self.log_message(f"  ❌ Unknown error: {connection_error}", 'warning')

                    self.status_label.configure(text="[ERROR] Connection exception",
                                              fg=self.colors['warning'])
            except Exception as e:
                self.log_message(f"💥 CONNECTION ERROR: {str(e)}", 'warning')
                self.log_message("🔍 Check if database server is running", 'warning')

        threading.Thread(target=connect_thread, daemon=True).start()

    def load_series_episodes(self):
        """Cargar series con sus episodios"""
        if not self.is_connected:
            self.log_message("❌ Database not connected!", 'warning')
            return

        self.log_operation_start("LOAD SERIES & EPISODES")

        def load_thread():
            try:
                # Obtener estadísticas primero
                stats = self.db.get_series_statistics()
                self.log_message(f"📊 Database Statistics:", 'accent')
                self.log_message(f"   📺 Total Series: {stats['total_series']}", 'fg')
                self.log_message(f"   🎬 Total Episodes: {stats['total_episodes']}", 'fg')
                self.log_message(f"   👻 Orphaned Episodes: {stats['orphaned_episodes']}", 'warning')
                self.log_message(f"   🔄 Duplicate Episodes: {stats['duplicate_episodes']}", 'warning')
                self.log_message(f"   📭 Series without Episodes: {stats['series_without_episodes']}", 'accent')

                # Obtener todas las series
                self.log_message("🔍 Loading all series...", 'accent')
                series_list = self.db.execute_query("""
                    SELECT
                        ss.id as series_id,
                        ss.title,
                        ss.tmdb_id,
                        ss.year,
                        COUNT(se.id) as episode_count
                    FROM streams_series ss
                    LEFT JOIN streams_episodes se ON ss.id = se.series_id
                    GROUP BY ss.id, ss.title, ss.tmdb_id, ss.year
                    ORDER BY ss.title
                    LIMIT 100
                """)

                if not series_list:
                    self.log_message("❌ No series found in database", 'warning')
                    self.log_operation_end("LOAD SERIES & EPISODES", False)
                    return

                # Limpiar treeview
                for item in self.unified_duplicates_tree.get_children():
                    self.unified_duplicates_tree.delete(item)

                # Configurar columnas para series
                self.unified_duplicates_tree['columns'] = ('series_id', 'title', 'tmdb_id', 'year', 'episodes')
                self.unified_duplicates_tree.heading('#0', text='Select')
                self.unified_duplicates_tree.heading('series_id', text='Series ID')
                self.unified_duplicates_tree.heading('title', text='Series Title')
                self.unified_duplicates_tree.heading('tmdb_id', text='TMDB ID')
                self.unified_duplicates_tree.heading('year', text='Year')
                self.unified_duplicates_tree.heading('episodes', text='Episodes')

                # Configurar anchos
                self.unified_duplicates_tree.column('#0', width=50)
                self.unified_duplicates_tree.column('series_id', width=80)
                self.unified_duplicates_tree.column('title', width=300)
                self.unified_duplicates_tree.column('tmdb_id', width=100)
                self.unified_duplicates_tree.column('year', width=80)
                self.unified_duplicates_tree.column('episodes', width=80)

                # Poblar treeview
                for i, series in enumerate(series_list):
                    if i % 10 == 0:
                        self.log_progress(i + 1, len(series_list), series['title'][:30])

                    self.unified_duplicates_tree.insert('', 'end',
                                        values=(
                                            series['series_id'],
                                            series['title'],
                                            series['tmdb_id'] or 'N/A',
                                            series['year'] or 'N/A',
                                            series['episode_count']
                                        ))

                self.log_message(f"✅ Loaded {len(series_list)} series successfully", 'success')
                self.log_operation_end("LOAD SERIES & EPISODES", True)

            except Exception as e:
                self.log_message(f"💥 ERROR loading series: {e}", 'warning')
                self.log_operation_end("LOAD SERIES & EPISODES", False)

        threading.Thread(target=load_thread, daemon=True).start()

    def find_duplicate_episodes(self):
        """Encontrar episodios duplicados"""
        if not self.is_connected:
            self.log_message("❌ Database not connected!", 'warning')
            return

        self.log_operation_start("FIND DUPLICATE EPISODES")

        def find_thread():
            try:
                self.log_message("🔍 Searching for duplicate episodes...", 'accent')
                duplicates = self.db.get_duplicate_episodes()

                if not duplicates:
                    self.log_message("✅ No duplicate episodes found!", 'success')
                    self.log_operation_end("FIND DUPLICATE EPISODES", True)
                    return

                # Limpiar treeview
                for item in self.unified_duplicates_tree.get_children():
                    self.unified_duplicates_tree.delete(item)

                # Configurar columnas para duplicados
                self.unified_duplicates_tree['columns'] = ('select', 'series_title', 'season', 'episode', 'count', 'episode_ids')
                self.unified_duplicates_tree.heading('#0', text='')
                self.unified_duplicates_tree.heading('select', text='☐')
                self.unified_duplicates_tree.heading('series_title', text='Series Title')
                self.unified_duplicates_tree.heading('season', text='Season')
                self.unified_duplicates_tree.heading('episode', text='Episode')
                self.unified_duplicates_tree.heading('count', text='Duplicates')
                self.unified_duplicates_tree.heading('episode_ids', text='Episode IDs')

                # Configurar anchos
                self.unified_duplicates_tree.column('#0', width=20)
                self.unified_duplicates_tree.column('select', width=40)
                self.unified_duplicates_tree.column('series_title', width=250)
                self.unified_duplicates_tree.column('season', width=80)
                self.unified_duplicates_tree.column('episode', width=80)
                self.unified_duplicates_tree.column('count', width=80)
                self.unified_duplicates_tree.column('episode_ids', width=200)

                # Poblar treeview
                for i, dup in enumerate(duplicates):
                    self.log_progress(i + 1, len(duplicates), dup['series_title'][:30])

                    self.unified_duplicates_tree.insert('', 'end',
                                        values=(
                                            "☐",  # Selection checkbox
                                            dup['series_title'] or 'Unknown Series',
                                            f"S{dup['season_num']:02d}" if dup['season_num'] else 'N/A',
                                            f"E{dup['episode_num']:02d}" if dup['episode_num'] else 'N/A',
                                            dup['duplicate_count'],
                                            dup['episode_ids']
                                        ))

                self.log_message(f"⚠️ Found {len(duplicates)} duplicate episode groups", 'warning')
                self.log_message("🎮 Double-click items to see episode details", 'accent')
                self.log_operation_end("FIND DUPLICATE EPISODES", True)

            except Exception as e:
                self.log_message(f"💥 ERROR finding duplicates: {e}", 'warning')
                self.log_operation_end("FIND DUPLICATE EPISODES", False)

        threading.Thread(target=find_thread, daemon=True).start()

    def find_orphaned_episodes(self):
        """Encontrar episodios huérfanos"""
        if not self.is_connected:
            self.log_message("❌ Database not connected!", 'warning')
            return

        self.log_operation_start("FIND ORPHANED EPISODES")

        def find_thread():
            try:
                self.log_message("👻 Searching for orphaned episodes...", 'accent')
                orphaned = self.db.get_orphaned_episodes_detailed()

                if not orphaned:
                    self.log_message("✅ No orphaned episodes found!", 'success')
                    self.log_operation_end("FIND ORPHANED EPISODES", True)
                    return

                # Limpiar treeview
                for item in self.unified_duplicates_tree.get_children():
                    self.unified_duplicates_tree.delete(item)

                # Configurar columnas para huérfanos
                self.unified_duplicates_tree['columns'] = ('episode_id', 'title', 'series_id', 'season', 'episode', 'reason')
                self.unified_duplicates_tree.heading('#0', text='Select')
                self.unified_duplicates_tree.heading('episode_id', text='Episode ID')
                self.unified_duplicates_tree.heading('title', text='Episode Title')
                self.unified_duplicates_tree.heading('series_id', text='Series ID')
                self.unified_duplicates_tree.heading('season', text='Season')
                self.unified_duplicates_tree.heading('episode', text='Episode')
                self.unified_duplicates_tree.heading('reason', text='Orphan Reason')

                # Configurar anchos
                self.unified_duplicates_tree.column('#0', width=50)
                self.unified_duplicates_tree.column('episode_id', width=80)
                self.unified_duplicates_tree.column('title', width=250)
                self.unified_duplicates_tree.column('series_id', width=80)
                self.unified_duplicates_tree.column('season', width=80)
                self.unified_duplicates_tree.column('episode', width=80)
                self.unified_duplicates_tree.column('reason', width=150)

                # Poblar treeview
                for i, orphan in enumerate(orphaned):
                    self.log_progress(i + 1, len(orphaned), orphan['episode_title'][:30] if orphan['episode_title'] else 'Unknown')

                    self.unified_duplicates_tree.insert('', 'end',
                                        values=(
                                            orphan['episode_id'],
                                            orphan['episode_title'] or 'Unknown Episode',
                                            orphan['series_id'] or 'N/A',
                                            f"S{orphan['season_num']:02d}" if orphan['season_num'] else 'N/A',
                                            f"E{orphan['episode_num']:02d}" if orphan['episode_num'] else 'N/A',
                                            orphan['orphan_reason']
                                        ))

                self.log_message(f"👻 Found {len(orphaned)} orphaned episodes", 'warning')
                self.log_message("🔧 These episodes need series assignment", 'accent')
                self.log_operation_end("FIND ORPHANED EPISODES", True)

            except Exception as e:
                self.log_message(f"💥 ERROR finding orphaned episodes: {e}", 'warning')
                self.log_operation_end("FIND ORPHANED EPISODES", False)

        threading.Thread(target=find_thread, daemon=True).start()

    def mass_delete_episodes(self):
        """Borrado masivo de episodios duplicados seleccionados"""
        if not self.is_connected:
            self.log_message("❌ Database not connected!", 'warning')
            return

        # Check if we have duplicate episodes loaded
        if not hasattr(self, 'unified_duplicates_tree') or not self.unified_duplicates_tree.get_children():
            self.log_message("❌ No duplicate episodes loaded! Find duplicates first.", 'warning')
            return

        # Check if current data is episode duplicates
        columns = self.unified_duplicates_tree['columns']
        if not ('series_title' in columns and 'season' in columns and 'episode' in columns):
            self.log_message("❌ Current data is not episode duplicates! Load episode duplicates first.", 'warning')
            return

        self.log_operation_start("MASS DELETE EPISODES")

        # Get selected items
        selected_items = []
        for item in self.unified_duplicates_tree.get_children():
            values = self.unified_duplicates_tree.item(item, 'values')
            if values[0] == "☑":  # Selected checkbox
                selected_items.append({
                    'item': item,
                    'values': values
                })

        if not selected_items:
            self.log_message("❌ No episodes selected for deletion! Select episodes first.", 'warning')
            self.log_operation_end("MASS DELETE EPISODES", False)
            return

        # Confirmation dialog
        import tkinter.messagebox as msgbox
        confirm = msgbox.askyesno(
            "⚡ Mass Episode Deletion Confirmation",
            f"🗑️ You are about to delete {len(selected_items)} duplicate episode groups\n\n"
            f"⚠️ This will delete ALL copies of the selected episodes!\n"
            f"⚠️ This action cannot be undone!\n\n"
            f"🚀 Continue with mass deletion?",
            icon='warning'
        )

        if not confirm:
            self.log_message("❌ Mass deletion cancelled by user", 'accent')
            self.log_operation_end("MASS DELETE EPISODES", False)
            return

        def mass_delete_thread():
            try:
                self.log_message(f"🚀 Starting mass deletion of {len(selected_items)} episode groups...", 'rog_red')

                total_deleted = 0
                total_failed = 0

                for i, selected in enumerate(selected_items):
                    values = selected['values']

                    # Extract episode information
                    if 'select' in columns:
                        series_title = values[1]
                        season_str = values[2]
                        episode_str = values[3]
                        episode_ids = values[5]
                    else:
                        series_title = values[0]
                        season_str = values[1]
                        episode_str = values[2]
                        episode_ids = values[4]

                    self.log_message(f"🗑️ Deleting {series_title} {episode_str}...", 'accent')

                    # Get episode IDs to delete
                    episode_ids_list = episode_ids.split(',')

                    deleted_count = 0
                    failed_count = 0

                    for episode_id in episode_ids_list:
                        episode_id = episode_id.strip()
                        try:
                            result = self.db.delete_duplicate_episode(int(episode_id))
                            if result:
                                deleted_count += 1
                            else:
                                failed_count += 1
                        except Exception as e:
                            self.log_message(f"⚠️ Failed to delete episode ID {episode_id}: {e}", 'warning')
                            failed_count += 1

                    total_deleted += deleted_count
                    total_failed += failed_count

                    if deleted_count > 0:
                        self.log_message(f"✅ {series_title} {episode_str}: Deleted {deleted_count} copies", 'success')
                    if failed_count > 0:
                        self.log_message(f"⚠️ {series_title} {episode_str}: Failed to delete {failed_count} copies", 'warning')

                    # Update progress
                    progress = ((i + 1) / len(selected_items)) * 100
                    self.log_message(f"📊 Progress: {i+1}/{len(selected_items)} ({progress:.1f}%)", 'accent')

                # Final summary
                self.log_message("═" * 50, 'accent')
                self.log_message(f"🎉 MASS DELETION COMPLETED!", 'nvidia_green')
                self.log_message(f"✅ Successfully deleted: {total_deleted} episode copies", 'success')
                if total_failed > 0:
                    self.log_message(f"⚠️ Failed to delete: {total_failed} episode copies", 'warning')
                self.log_message(f"📊 Processed: {len(selected_items)} episode groups", 'accent')
                self.log_message("═" * 50, 'accent')

                # Refresh the duplicate episodes list
                self.log_message("🔄 Refreshing duplicate episodes list...", 'accent')
                self.find_duplicate_episodes()

                self.log_operation_end("MASS DELETE EPISODES", True)

            except Exception as e:
                self.log_message(f"💥 ERROR during mass deletion: {e}", 'warning')
                self.log_operation_end("MASS DELETE EPISODES", False)

        threading.Thread(target=mass_delete_thread, daemon=True).start()

    def show_series_statistics(self):
        """Mostrar estadísticas detalladas de series"""
        if not self.is_connected:
            self.log_message("❌ Database not connected!", 'warning')
            return

        self.log_operation_start("SERIES STATISTICS")

        def stats_thread():
            try:
                stats = self.db.get_series_statistics()

                self.log_message("📊 DETAILED SERIES STATISTICS", 'nvidia_green')
                self.log_message("═" * 50, 'accent')
                self.log_message(f"📺 Total Series: {stats['total_series']}", 'fg')
                self.log_message(f"🎬 Total Episodes: {stats['total_episodes']}", 'fg')
                self.log_message(f"👻 Orphaned Episodes: {stats['orphaned_episodes']}", 'warning')
                self.log_message(f"🔄 Duplicate Episodes: {stats['duplicate_episodes']}", 'warning')
                self.log_message(f"📭 Series without Episodes: {stats['series_without_episodes']}", 'accent')

                # Calcular estadísticas adicionales
                if stats['total_series'] > 0:
                    avg_episodes = stats['total_episodes'] / stats['total_series']
                    self.log_message(f"📈 Average Episodes per Series: {avg_episodes:.2f}", 'accent')

                # Porcentajes
                if stats['total_episodes'] > 0:
                    orphan_pct = (stats['orphaned_episodes'] / stats['total_episodes']) * 100
                    duplicate_pct = (stats['duplicate_episodes'] / stats['total_episodes']) * 100
                    self.log_message(f"📊 Orphaned Episodes: {orphan_pct:.1f}%", 'warning')
                    self.log_message(f"📊 Duplicate Episodes: {duplicate_pct:.1f}%", 'warning')

                self.log_message("═" * 50, 'accent')
                self.log_operation_end("SERIES STATISTICS", True)

            except Exception as e:
                self.log_message(f"💥 ERROR getting statistics: {e}", 'warning')
                self.log_operation_end("SERIES STATISTICS", False)

        threading.Thread(target=stats_thread, daemon=True).start()

    def load_m3u_file(self):
        """Cargar archivo M3U desde archivo local"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            title="Select M3U File",
            filetypes=[("M3U files", "*.m3u"), ("M3U8 files", "*.m3u8"), ("All files", "*.*")]
        )

        if file_path:
            self._process_m3u_file(file_path)

    def load_m3u_url(self):
        """Cargar archivo M3U desde URL"""
        from tkinter import simpledialog

        url = simpledialog.askstring("M3U URL", "Enter M3U URL:")
        if url:
            self._process_m3u_url(url)

    def _process_m3u_file(self, file_path: str):
        """Procesar archivo M3U local"""
        self.log_operation_start("LOAD M3U FILE")

        def process_thread():
            try:
                from m3u_manager import M3UManager

                self.log_message(f"📁 Loading M3U file: {file_path}", 'accent')
                m3u_manager = M3UManager()
                entries = m3u_manager.parse_m3u_file(file_path)

                if not entries:
                    self.log_message("❌ No entries found in M3U file", 'warning')
                    self.log_operation_end("LOAD M3U FILE", False)
                    return

                self._display_m3u_entries(entries, m3u_manager)
                self.log_operation_end("LOAD M3U FILE", True)

            except Exception as e:
                self.log_message(f"💥 ERROR processing M3U file: {e}", 'warning')
                self.log_operation_end("LOAD M3U FILE", False)

        threading.Thread(target=process_thread, daemon=True).start()

    def _process_m3u_url(self, url: str):
        """Procesar archivo M3U desde URL"""
        self.log_operation_start("LOAD M3U URL")

        def process_thread():
            try:
                from m3u_manager import M3UManager

                self.log_message(f"🌐 Downloading M3U from: {url}", 'accent')
                m3u_manager = M3UManager()
                entries = m3u_manager.parse_m3u_url(url)

                if not entries:
                    self.log_message("❌ No entries found in M3U URL", 'warning')
                    self.log_operation_end("LOAD M3U URL", False)
                    return

                self._display_m3u_entries(entries, m3u_manager)
                self.log_operation_end("LOAD M3U URL", True)

            except Exception as e:
                self.log_message(f"💥 ERROR processing M3U URL: {e}", 'warning')
                self.log_operation_end("LOAD M3U URL", False)

        threading.Thread(target=process_thread, daemon=True).start()

    def _display_m3u_entries(self, entries: list, m3u_manager):
        """Mostrar entradas M3U en el treeview"""
        try:
            # Obtener estadísticas
            stats = m3u_manager.get_m3u_statistics(entries)

            self.log_message(f"📊 M3U Statistics:", 'accent')
            self.log_message(f"   📺 Total Entries: {stats['total_entries']}", 'fg')
            self.log_message(f"   🎬 Series Entries: {stats['series_entries']}", 'fg')
            self.log_message(f"   🎭 Movie Entries: {stats['movie_entries']}", 'fg')
            self.log_message(f"   📁 Groups: {len(stats['groups'])}", 'fg')

            # Filtrar solo series
            series_entries = m3u_manager.filter_series_entries(entries)

            if not series_entries:
                self.log_message("❌ No series entries found in M3U", 'warning')
                return

            # Limpiar treeview
            for item in self.unified_duplicates_tree.get_children():
                self.unified_duplicates_tree.delete(item)

            # Configurar columnas para M3U
            self.unified_duplicates_tree['columns'] = ('title', 'series', 'season', 'episode', 'group', 'quality')
            self.unified_duplicates_tree.heading('#0', text='Select')
            self.unified_duplicates_tree.heading('title', text='Original Title')
            self.unified_duplicates_tree.heading('series', text='Series Title')
            self.unified_duplicates_tree.heading('season', text='Season')
            self.unified_duplicates_tree.heading('episode', text='Episode')
            self.unified_duplicates_tree.heading('group', text='Group')
            self.unified_duplicates_tree.heading('quality', text='Quality')

            # Configurar anchos
            self.unified_duplicates_tree.column('#0', width=50)
            self.unified_duplicates_tree.column('title', width=200)
            self.unified_duplicates_tree.column('series', width=200)
            self.unified_duplicates_tree.column('season', width=80)
            self.unified_duplicates_tree.column('episode', width=80)
            self.unified_duplicates_tree.column('group', width=120)
            self.unified_duplicates_tree.column('quality', width=80)

            # Poblar treeview con series
            for i, entry in enumerate(series_entries[:100]):  # Limitar a 100 para rendimiento
                if i % 20 == 0:
                    self.log_progress(i + 1, min(len(series_entries), 100), entry['title'][:30])

                info = m3u_manager.extract_series_info(entry)

                self.unified_duplicates_tree.insert('', 'end',
                                    values=(
                                        entry['title'][:50],
                                        info['series_title'][:50],
                                        f"S{info['season']:02d}" if info['season'] else 'N/A',
                                        f"E{info['episode']:02d}" if info['episode'] else 'N/A',
                                        info['group'][:20] if info['group'] else 'N/A',
                                        info['quality'] or 'N/A'
                                    ))

            self.log_message(f"✅ Displayed {min(len(series_entries), 100)} series entries", 'success')
            if len(series_entries) > 100:
                self.log_message(f"📊 Showing first 100 of {len(series_entries)} series entries", 'accent')

            # Guardar entradas para análisis posterior
            self.current_m3u_entries = entries
            self.current_m3u_manager = m3u_manager

        except Exception as e:
            self.log_message(f"💥 ERROR displaying M3U entries: {e}", 'warning')

    def analyze_m3u_database(self):
        """Analizar M3U contra base de datos"""
        if not self.is_connected:
            self.log_message("❌ Database not connected!", 'warning')
            return

        if not hasattr(self, 'current_m3u_entries') or not self.current_m3u_entries:
            self.log_message("❌ No M3U file loaded! Load M3U first.", 'warning')
            return

        self.log_operation_start("ANALYZE M3U vs DATABASE")

        def analyze_thread():
            try:
                self.log_message("🔍 Analyzing M3U content against database...", 'accent')

                # Filtrar solo series del M3U
                series_entries = self.current_m3u_manager.filter_series_entries(self.current_m3u_entries)

                analysis_results = {
                    'total_m3u_series': len(series_entries),
                    'existing_series': 0,
                    'missing_series': 0,
                    'existing_episodes': 0,
                    'missing_episodes': 0,
                    'matches': []
                }

                self.log_message(f"📊 Analyzing {len(series_entries)} series entries...", 'accent')

                for i, entry in enumerate(series_entries):
                    if i % 50 == 0:
                        self.log_progress(i + 1, len(series_entries), f"Entry {i+1}")

                    info = self.current_m3u_manager.extract_series_info(entry)

                    if not info['series_title'] or not info['season'] or not info['episode']:
                        continue

                    # Buscar serie en base de datos
                    existing_series = self.db.get_series_by_title_pattern(info['series_title'])

                    if existing_series:
                        analysis_results['existing_series'] += 1

                        # Verificar si el episodio específico existe
                        for series in existing_series:
                            episodes = self.db.get_series_episodes_detailed(series['series_id'])
                            episode_exists = any(
                                ep['season_num'] == info['season'] and
                                ep['episode_num'] == info['episode']
                                for ep in episodes
                            )

                            if episode_exists:
                                analysis_results['existing_episodes'] += 1
                            else:
                                analysis_results['missing_episodes'] += 1
                                analysis_results['matches'].append({
                                    'type': 'missing_episode',
                                    'entry': entry,
                                    'info': info,
                                    'series': series
                                })
                            break
                    else:
                        analysis_results['missing_series'] += 1
                        analysis_results['matches'].append({
                            'type': 'missing_series',
                            'entry': entry,
                            'info': info
                        })

                # Mostrar resultados
                self.log_message("📊 ANALYSIS RESULTS", 'nvidia_green')
                self.log_message("═" * 50, 'accent')
                self.log_message(f"📺 Total M3U Series Entries: {analysis_results['total_m3u_series']}", 'fg')
                self.log_message(f"✅ Existing Series: {analysis_results['existing_series']}", 'success')
                self.log_message(f"❌ Missing Series: {analysis_results['missing_series']}", 'warning')
                self.log_message(f"✅ Existing Episodes: {analysis_results['existing_episodes']}", 'success')
                self.log_message(f"📥 Missing Episodes: {analysis_results['missing_episodes']}", 'accent')
                self.log_message("═" * 50, 'accent')

                # Mostrar algunos ejemplos de contenido faltante
                missing_series = [m for m in analysis_results['matches'] if m['type'] == 'missing_series']
                missing_episodes = [m for m in analysis_results['matches'] if m['type'] == 'missing_episode']

                if missing_series:
                    self.log_message(f"📋 Examples of Missing Series (first 5):", 'accent')
                    for i, match in enumerate(missing_series[:5]):
                        info = match['info']
                        self.log_message(f"   {i+1}. {info['series_title']} S{info['season']:02d}E{info['episode']:02d}", 'fg')

                if missing_episodes:
                    self.log_message(f"📋 Examples of Missing Episodes (first 5):", 'accent')
                    for i, match in enumerate(missing_episodes[:5]):
                        info = match['info']
                        series = match['series']
                        self.log_message(f"   {i+1}. {series['title']} S{info['season']:02d}E{info['episode']:02d}", 'fg')

                self.log_message("💡 Use this analysis to identify content gaps", 'accent')
                self.log_operation_end("ANALYZE M3U vs DATABASE", True)

            except Exception as e:
                self.log_message(f"💥 ERROR analyzing M3U: {e}", 'warning')
                self.log_operation_end("ANALYZE M3U vs DATABASE", False)

        threading.Thread(target=analyze_thread, daemon=True).start()

    def test_database_gaming(self):
        """Test database connection and basic queries"""
        if not self.is_connected:
            self.log_message("❌ Database not connected! Connect first.", 'warning')
            return

        self.log_message("🧪 Testing database functionality...", 'accent')

        def test_thread():
            try:
                # Test 1: Basic connection
                self.log_message("🔍 Test 1: Basic query test", 'accent')
                test_result = self.db.execute_query("SELECT 1 as test")
                if test_result:
                    self.log_message("✅ Basic query successful", 'success')
                else:
                    self.log_message("❌ Basic query failed", 'warning')
                    return

                # Test 2: Check streams table
                self.log_message("🔍 Test 2: Checking streams table", 'accent')
                streams_query = "SELECT COUNT(*) as total FROM streams LIMIT 1"
                streams_result = self.db.execute_query(streams_query)
                if streams_result and len(streams_result) > 0:
                    total_streams = streams_result[0].get('total', 0)
                    self.log_message(f"✅ Streams table accessible, {total_streams} total streams", 'success')
                else:
                    self.log_message("❌ Cannot access streams table", 'warning')
                    return

                # Test 3: Check streams_types table
                self.log_message("🔍 Test 3: Checking streams_types table", 'accent')
                types_query = "SELECT type_id, type_name FROM streams_types"
                types_result = self.db.execute_query(types_query)
                if types_result:
                    self.log_message(f"✅ Found {len(types_result)} stream types:", 'success')
                    for stream_type in types_result:
                        self.log_message(f"  - {stream_type.get('type_id')}: {stream_type.get('type_name')}", 'accent')
                else:
                    self.log_message("❌ Cannot access streams_types table", 'warning')
                    return

                # Test 4: Check for movies
                self.log_message("🔍 Test 4: Checking for movies", 'accent')
                movies_query = """
                SELECT COUNT(*) as total
                FROM streams s
                JOIN streams_types st ON s.type = st.type_id
                WHERE st.type_name = 'movie'
                """
                movies_result = self.db.execute_query(movies_query)
                if movies_result and len(movies_result) > 0:
                    total_movies = movies_result[0].get('total', 0)
                    self.log_message(f"✅ Found {total_movies} movies in database", 'success')
                else:
                    # Try alternative type name
                    movies_query2 = """
                    SELECT COUNT(*) as total
                    FROM streams s
                    JOIN streams_types st ON s.type = st.type_id
                    WHERE st.type_name LIKE '%movie%' OR st.type_name LIKE '%Movie%'
                    """
                    movies_result2 = self.db.execute_query(movies_query2)
                    if movies_result2 and len(movies_result2) > 0:
                        total_movies = movies_result2[0].get('total', 0)
                        self.log_message(f"✅ Found {total_movies} movies (alternative query)", 'success')
                    else:
                        self.log_message("⚠️ No movies found or different type naming", 'warning')

                # Test 5: Sample data
                self.log_message("🔍 Test 5: Sample stream data", 'accent')
                sample_query = "SELECT id, stream_display_name, type FROM streams LIMIT 5"
                sample_result = self.db.execute_query(sample_query)
                if sample_result:
                    self.log_message(f"✅ Sample data (first 5 streams):", 'success')
                    for stream in sample_result:
                        stream_id = stream.get('id', 'N/A')
                        name = stream.get('stream_display_name', 'N/A')
                        stream_type = stream.get('type', 'N/A')
                        self.log_message(f"  - ID:{stream_id} | Type:{stream_type} | {name[:40]}", 'accent')

                self.log_message("🎮 Database test completed!", 'nvidia_green')

            except Exception as e:
                self.log_message(f"💥 Database test error: {str(e)}", 'warning')

        threading.Thread(target=test_thread, daemon=True).start()

    def network_diagnostic_gaming(self):
        """Diagnóstico de red y conectividad"""
        self.log_message("🌐 Starting network diagnostic...", 'accent')

        def diagnostic_thread():
            try:
                import socket
                import subprocess

                host = self.connection_vars['Host'].get()
                port = int(self.connection_vars['Port'].get())

                # Test 1: Basic network connectivity
                self.log_message("🔍 Test 1: Network connectivity", 'accent')
                try:
                    # Test if host is reachable
                    socket.setdefaulttimeout(5)
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    result = sock.connect_ex((host, port))
                    sock.close()

                    if result == 0:
                        self.log_message(f"✅ Network connection to {host}:{port} successful", 'success')
                    else:
                        self.log_message(f"❌ Cannot reach {host}:{port} (error code: {result})", 'warning')
                        self.log_message("💡 Possible issues:", 'accent')
                        self.log_message("  • Server is down", 'accent')
                        self.log_message("  • Port is blocked by firewall", 'accent')
                        self.log_message("  • Wrong host/port configuration", 'accent')
                        return

                except Exception as e:
                    self.log_message(f"❌ Network test failed: {e}", 'warning')
                    return

                # Test 2: DNS resolution
                self.log_message("🔍 Test 2: DNS resolution", 'accent')
                try:
                    ip = socket.gethostbyname(host)
                    self.log_message(f"✅ DNS resolution successful: {host} → {ip}", 'success')
                except Exception as e:
                    self.log_message(f"❌ DNS resolution failed: {e}", 'warning')
                    self.log_message("💡 Try using IP address instead of hostname", 'accent')

                # Test 3: Ping test
                self.log_message("🔍 Test 3: Ping test", 'accent')
                try:
                    # Ping command (works on Windows and Linux)
                    import platform
                    param = '-n' if platform.system().lower() == 'windows' else '-c'
                    command = ['ping', param, '3', host]

                    result = subprocess.run(command, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        self.log_message(f"✅ Ping to {host} successful", 'success')
                    else:
                        self.log_message(f"❌ Ping to {host} failed", 'warning')
                        self.log_message("💡 Host may not respond to ping but still be reachable", 'accent')

                except Exception as e:
                    self.log_message(f"⚠️ Ping test error: {e}", 'accent')

                # Test 4: MySQL specific port test
                self.log_message("🔍 Test 4: MySQL port test", 'accent')
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(5)
                    sock.connect((host, port))

                    # Try to read MySQL handshake
                    data = sock.recv(1024)
                    if data and len(data) > 0:
                        if b'mysql' in data.lower() or data[0] in [10, 9]:  # MySQL protocol version
                            self.log_message(f"✅ MySQL server detected on {host}:{port}", 'success')
                        else:
                            self.log_message(f"⚠️ Port {port} is open but may not be MySQL", 'warning')
                    else:
                        self.log_message(f"⚠️ Port {port} is open but no response", 'warning')

                    sock.close()

                except Exception as e:
                    self.log_message(f"❌ MySQL port test failed: {e}", 'warning')

                # Test 5: Connection parameters validation
                self.log_message("🔍 Test 5: Connection parameters", 'accent')
                user = self.connection_vars['User'].get()
                database = self.connection_vars['DB'].get()

                if not host:
                    self.log_message("❌ Host is empty", 'warning')
                if not user:
                    self.log_message("❌ User is empty", 'warning')
                if not database:
                    self.log_message("❌ Database is empty", 'warning')
                if port < 1 or port > 65535:
                    self.log_message(f"❌ Invalid port: {port}", 'warning')

                if host and user and database and 1 <= port <= 65535:
                    self.log_message("✅ Connection parameters look valid", 'success')

                self.log_message("🎮 Network diagnostic completed!", 'nvidia_green')
                self.log_message("💡 If network tests pass but connection fails, check MySQL credentials", 'accent')

            except Exception as e:
                self.log_message(f"💥 Diagnostic error: {e}", 'warning')

        threading.Thread(target=diagnostic_thread, daemon=True).start()

    def disconnect_database_gaming(self):
        """Desconectar base de datos gaming"""
        self.db.disconnect()
        self.is_connected = False
        self.log_message("🔌 Database disconnected", 'rog_red')
        self.status_label.configure(text="[DISCONNECTED] Ready to connect...",
                                  fg=self.colors['rog_red'])
        self.connect_btn.configure(text="⚡ CONNECT",
                                 bg=self.colors['nvidia_green'],
                                 fg='black',
                                 command=self.connect_database_gaming)

    def load_unified_tmdb_duplicates_gaming(self):
        """Cargar duplicados TMDB estilo gaming con treeview real - MEJORADO"""
        if not self.is_connected:
            self.log_message("❌ Database not connected!", 'warning')
            return

        self.log_operation_start("LOAD TMDB DUPLICATES")
        self.log_message("🎬 Initializing TMDB duplicate search...", 'accent')
        self.log_message("🔍 Preparing database query...", 'accent')

        def load_thread():
            try:
                # Debug: Test connection first
                self.log_message("🔗 Testing database connection...", 'accent')

                # Test basic connection using robust method
                if not self.check_database_connection():
                    self.log_message("❌ Database connection is not active", 'warning')
                    self.log_message("🔄 Attempting to reconnect...", 'accent')

                    # Try to reconnect
                    host = self.connection_vars['Host'].get()
                    user = self.connection_vars['User'].get()
                    password = self.connection_vars['Pass'].get()
                    database = self.connection_vars['DB'].get()
                    port = int(self.connection_vars['Port'].get())

                    if not self.db.connect(host, user, password, database, port):
                        self.log_message("❌ Reconnection failed", 'warning')
                        return
                    else:
                        self.log_message("✅ Reconnection successful", 'success')

                # Test simple query
                test_query = "SELECT 1 as test"
                test_result = self.db.execute_query(test_query)
                if test_result:
                    self.log_message(f"✅ Database responsive, basic query works", 'success')
                else:
                    self.log_message("❌ Database not responding to test query", 'warning')
                    self.log_message("🔍 Checking connection status...", 'accent')

                    # Check connection status
                    try:
                        if self.db.connection:
                            self.log_message(f"📊 Connection object exists: {type(self.db.connection)}", 'accent')

                            # Check connection status with multiple methods
                            try:
                                if hasattr(self.db.connection, 'is_connected'):
                                    connected = self.db.connection.is_connected()
                                    self.log_message(f"📊 Connection status (is_connected): {connected}", 'accent')
                                elif hasattr(self.db.connection, 'ping'):
                                    try:
                                        self.db.connection.ping(reconnect=False)
                                        self.log_message(f"📊 Connection status (ping): Active", 'accent')
                                    except Exception as ping_e:
                                        self.log_message(f"📊 Connection status (ping): Failed - {ping_e}", 'accent')
                                else:
                                    self.log_message(f"📊 Connection status: Unknown (no status method)", 'accent')
                            except Exception as status_e:
                                self.log_message(f"📊 Connection status check failed: {status_e}", 'accent')
                        else:
                            self.log_message("❌ No connection object", 'warning')
                    except Exception as e:
                        self.log_message(f"💥 Error checking connection: {e}", 'warning')

                    return

                # Get duplicates
                self.log_message("📊 Querying for TMDB duplicates...", 'accent')
                duplicates = self.db.get_duplicate_movies_by_tmdb()
                self.current_duplicates = duplicates
                self.log_message(f"✅ Query returned {len(duplicates)} duplicate groups", 'success')

                if len(duplicates) == 0:
                    self.log_message("⚠️ No duplicates found. Checking data...", 'warning')
                    # Check if there are any movies at all
                    check_query = """
                    SELECT COUNT(*) as total
                    FROM streams s
                    JOIN streams_types st ON s.type = st.type_id
                    WHERE st.type_name = 'Movies'
                    """
                    check_result = self.db.execute_query(check_query)
                    if check_result and len(check_result) > 0:
                        total_movies = check_result[0].get('total', 0)
                        self.log_message(f"📊 Total movies in database: {total_movies}", 'accent')

                        # Check TMDB data
                        tmdb_query = """
                        SELECT COUNT(*) as total
                        FROM streams s
                        JOIN streams_types st ON s.type = st.type_id
                        WHERE st.type_name = 'Movies'
                        AND s.tmdb_id IS NOT NULL
                        AND s.tmdb_id != 0
                        """
                        tmdb_result = self.db.execute_query(tmdb_query)
                        if tmdb_result and len(tmdb_result) > 0:
                            tmdb_movies = tmdb_result[0].get('total', 0)
                            self.log_message(f"📊 Movies with TMDB ID: {tmdb_movies}", 'accent')
                    return

                # Clear existing treeview data
                for item in self.unified_duplicates_tree.get_children():
                    self.unified_duplicates_tree.delete(item)

                self.unified_duplicates_data = []
                self.selected_items.clear()

                # Populate treeview with real-time progress
                self.log_message(f"📊 Processing {len(duplicates)} duplicate groups...", 'accent')
                for i, dup in enumerate(duplicates):
                    # Show progress every 10 items or for small datasets
                    if i % 10 == 0 or len(duplicates) <= 20:
                        self.log_progress(i + 1, len(duplicates), dup['sample_title'][:30])

                    # Fix: Use correct field name from database
                    total_copies = dup.get('duplicate_count', 0)
                    tmdb_id = dup.get('tmdb_id', 0)

                    # Get detailed info for each duplicate using the working function
                    try:
                        copies = self.db.get_movie_copies_by_tmdb(tmdb_id)
                        if not copies:
                            copies = []
                    except Exception as e:
                        self.log_message(f"⚠️ Error getting copies for TMDB {tmdb_id}: {e}", 'warning')
                        copies = []

                    # Count qualities and types (like before)
                    quality_counts = {'4K': 0, '60FPS': 0, 'FHD': 0, 'HD': 0, 'SD': 0}
                    type_counts = {'symlink': 0, 'direct': 0, 'otros': 0}

                    for copy in copies:
                        # Fix: Use correct field names from database
                        name = copy.get('title', copy.get('stream_display_name', '')).lower()

                        # Count qualities
                        if '4k' in name or '2160p' in name:
                            quality_counts['4K'] += 1
                        elif '60fps' in name:
                            quality_counts['60FPS'] += 1
                        elif 'fhd' in name or '1080p' in name:
                            quality_counts['FHD'] += 1
                        elif 'hd' in name or '720p' in name:
                            quality_counts['HD'] += 1
                        else:
                            quality_counts['SD'] += 1

                        # Count types - Fix field names
                        if copy.get('movie_symlink') == 1 or copy.get('movie_symlink') == '1':
                            type_counts['symlink'] += 1
                        elif copy.get('direct_source') or copy.get('direct_proxy'):
                            type_counts['direct'] += 1
                        else:
                            type_counts['otros'] += 1

                    # Generate recommendation (like before)
                    recommendation = self.generate_recommendation(quality_counts, type_counts)

                    # Insert into treeview with gaming colors
                    values = (
                        "☐",  # Selection
                        tmdb_id,
                        dup['sample_title'],
                        total_copies,
                        quality_counts['4K'],
                        quality_counts['60FPS'],
                        quality_counts['FHD'],
                        quality_counts['HD'],
                        quality_counts['SD'],
                        type_counts['symlink'],
                        type_counts['direct'],
                        type_counts['otros'],
                        recommendation
                    )

                    # Determine gaming color tag based on content
                    if quality_counts['4K'] > 0:
                        tag = "high_quality"  # Verde NVIDIA para 4K
                    elif quality_counts['60FPS'] > 0 or quality_counts['FHD'] > 0:
                        tag = "medium_quality"  # Azul para calidad media
                    elif total_copies > 10:
                        tag = "warning_item"  # Amarillo para muchas copias
                    else:
                        tag = "low_quality"  # Gris normal

                    item = self.unified_duplicates_tree.insert("", "end", values=values, tags=(tag,))
                    self.unified_duplicates_data.append({
                        'item_id': item,
                        'tmdb_id': dup['tmdb_id'],
                        'title': dup['sample_title'],
                        'copies': copies,
                        'data': dup
                    })

                # Log summary
                self.log_message(f"📊 Successfully loaded {len(duplicates)} duplicate groups", 'success')
                self.log_message("🎮 Use checkboxes to select items for management", 'nvidia_green')
                self.log_message("🔍 Double-click items for details", 'accent')
                self.log_operation_end("LOAD TMDB DUPLICATES", True)

            except Exception as e:
                self.log_message(f"💥 ERROR loading duplicates: {e}", 'warning')
                self.log_operation_end("LOAD TMDB DUPLICATES", False)

        threading.Thread(target=load_thread, daemon=True).start()

    def load_symlink_movies_gaming(self):
        """Cargar películas con múltiples symlinks usando función que funciona"""
        if not self.is_connected:
            self.log_message("❌ Database not connected!", 'warning')
            return

        self.log_message("🔗 Loading movies with multiple symlinks...", 'success')

        def load_thread():
            try:
                # Use the working function
                movies = self.db.get_movies_with_multiple_symlinks()
                self.log_message(f"✅ Found {len(movies)} movies with multiple symlinks", 'success')

                # Clear existing treeview data
                for item in self.unified_duplicates_tree.get_children():
                    self.unified_duplicates_tree.delete(item)

                self.unified_duplicates_data = []
                self.selected_items.clear()

                # Populate treeview with symlink data
                for i, movie in enumerate(movies):
                    self.log_message(f"📊 Processing {i+1}/{len(movies)}: {movie['sample_title']}", 'accent')

                    tmdb_id = movie.get('tmdb_id', 0)
                    title = movie.get('sample_title', 'Unknown')
                    symlink_count = movie.get('symlink_count', 0)

                    # Parse qualities from titles
                    all_titles = movie.get('all_titles', '').split('|||')
                    quality_counts = {'4K': 0, '60FPS': 0, 'FHD': 0, 'HD': 0, 'SD': 0}

                    for title_item in all_titles:
                        title_lower = title_item.lower()
                        if '4k' in title_lower or '2160p' in title_lower:
                            quality_counts['4K'] += 1
                        elif '60fps' in title_lower:
                            quality_counts['60FPS'] += 1
                        elif 'fhd' in title_lower or '1080p' in title_lower:
                            quality_counts['FHD'] += 1
                        elif 'hd' in title_lower or '720p' in title_lower:
                            quality_counts['HD'] += 1
                        else:
                            quality_counts['SD'] += 1

                    # Generate recommendation
                    if quality_counts['4K'] > 0:
                        recommendation = "🥇 Keep 4K symlinks"
                    elif quality_counts['60FPS'] > 0:
                        recommendation = "🥈 Keep 60FPS symlinks"
                    elif quality_counts['FHD'] > 0:
                        recommendation = "🥉 Keep FHD symlinks"
                    else:
                        recommendation = "🔗 Keep best symlink"

                    # Insert into treeview with gaming colors
                    values = (
                        "☐",  # Selection
                        tmdb_id,
                        title,
                        symlink_count,
                        quality_counts['4K'],
                        quality_counts['60FPS'],
                        quality_counts['FHD'],
                        quality_counts['HD'],
                        quality_counts['SD'],
                        symlink_count,  # All are symlinks
                        0,  # No direct sources
                        0,  # No others
                        recommendation
                    )

                    # Determine gaming color tag for symlinks
                    if quality_counts['4K'] > 0:
                        tag = "high_quality"  # Verde NVIDIA para 4K
                    elif quality_counts['60FPS'] > 0 or quality_counts['FHD'] > 0:
                        tag = "medium_quality"  # Azul para calidad media
                    elif symlink_count > 5:
                        tag = "warning_item"  # Amarillo para muchos symlinks
                    else:
                        tag = "low_quality"  # Gris normal

                    item = self.unified_duplicates_tree.insert("", "end", values=values, tags=(tag,))
                    self.unified_duplicates_data.append({
                        'item_id': item,
                        'tmdb_id': tmdb_id,
                        'title': title,
                        'data': movie
                    })

                # Log summary
                self.log_message(f"📊 Loaded {len(movies)} movies with symlinks in treeview", 'success')
                self.log_message("🎮 Use checkboxes to select items for management", 'nvidia_green')
                self.log_message("🔍 Double-click items for details", 'accent')

            except Exception as e:
                self.log_message(f"💥 ERROR loading symlink movies: {e}", 'warning')

        threading.Thread(target=load_thread, daemon=True).start()

    def generate_recommendation(self, quality_counts, type_counts):
        """Generate recommendation like before"""
        if quality_counts['4K'] > 0 and type_counts['symlink'] > 0:
            return "🥇 Mantener 4K symlinks"
        elif quality_counts['60FPS'] > 0 and type_counts['symlink'] > 0:
            return "🥈 Mantener 60FPS symlinks"
        elif quality_counts['FHD'] > 0 and type_counts['symlink'] > 0:
            return "🥉 Mantener FHD symlinks"
        elif type_counts['symlink'] > 0:
            return "🔗 Mantener symlinks"
        elif type_counts['direct'] > 0:
            return "📁 Mantener direct más nuevo"
        else:
            return "❓ Revisar manualmente"

    def show_movie_details_gaming(self, tmdb_id, title):
        """Show detailed movie information in terminal"""
        self.log_message(f"🔍 Loading details for: {title} (TMDB {tmdb_id})", 'nvidia_green')

        def details_thread():
            try:
                copies = self.db.get_movie_copies_by_tmdb(tmdb_id)
                self.log_message(f"📊 Found {len(copies)} copies:", 'accent')

                for i, copy in enumerate(copies[:10]):  # Show first 10
                    stream_id = copy.get('id', 'N/A')
                    name = copy.get('stream_display_name', 'Unknown')
                    symlink = "🔗 Symlink" if copy.get('movie_symlink') else "📁 Direct"

                    self.log_message(f"  {i+1}. ID:{stream_id} | {name[:40]} | {symlink}", 'fg')

                if len(copies) > 10:
                    self.log_message(f"  ... and {len(copies) - 10} more copies", 'accent')

            except Exception as e:
                self.log_message(f"💥 ERROR loading details: {e}", 'warning')

        threading.Thread(target=details_thread, daemon=True).start()

    def open_gaming_manual_selection(self, tmdb_id, title):
        """Open gaming manual selection window for specific movie"""
        self.log_message(f"🎮 Opening gaming manual selection window...", 'nvidia_green')

        # Create gaming manual selection window
        manual_window = tk.Toplevel(self.root)
        manual_window.title(f"⚡ Gaming Manual Selection - {title}")
        manual_window.geometry("1200x800")
        manual_window.configure(bg=self.colors['bg'])

        # Make window modal
        manual_window.transient(self.root)
        manual_window.grab_set()

        # Header gaming
        header_frame = tk.Frame(manual_window, bg=self.colors['surface'], height=50)
        header_frame.pack(fill='x', padx=10, pady=5)
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame,
                              text=f"⚡ GAMING MANUAL SELECTION - {title} ⚡",
                              font=self.font_mono_large,
                              bg=self.colors['surface'],
                              fg=self.colors['nvidia_green'])
        title_label.pack(pady=10)

        # Info bar
        info_frame = tk.Frame(manual_window, bg=self.colors['bg'], height=30)
        info_frame.pack(fill='x', padx=10, pady=2)
        info_frame.pack_propagate(False)

        info_label = tk.Label(info_frame,
                             text=f"🎬 TMDB ID: {tmdb_id} | 🎯 Select copies to KEEP | 🗑️ Unselected will be DELETED",
                             font=self.font_mono,
                             bg=self.colors['bg'],
                             fg=self.colors['accent'])
        info_label.pack(pady=5)

        # Main content area
        content_frame = tk.Frame(manual_window, bg=self.colors['bg'])
        content_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Create gaming treeview for copies
        self.setup_gaming_copies_treeview(content_frame, tmdb_id, title)

        # Control buttons gaming
        self.setup_gaming_manual_controls(manual_window, content_frame, tmdb_id, title)

        # Load data for this specific movie
        self.load_movie_copies_gaming(tmdb_id, title)

        self.log_message(f"✅ Gaming manual selection window opened for {title}", 'success')

    def setup_gaming_copies_treeview(self, parent, tmdb_id, title):
        """Setup gaming treeview for movie copies"""
        # Treeview frame
        tree_frame = tk.Frame(parent, bg=self.colors['bg'])
        tree_frame.pack(fill='both', expand=True, pady=(0, 10))

        # Columns for copies
        columns = ("Sel", "ID", "Title", "Quality", "Type", "Source", "Rating", "Year", "Size", "Recommendation")

        # Create gaming style for copies treeview
        style = ttk.Style()

        # Configure gaming copies treeview style
        style.configure("GamingCopies.Treeview",
                       background=self.colors['bg'],
                       foreground=self.colors['fg'],
                       fieldbackground=self.colors['bg'],
                       borderwidth=1,
                       relief="flat")

        style.configure("GamingCopies.Treeview.Heading",
                       background=self.colors['surface'],
                       foreground=self.colors['rog_red'],  # Red ROG for copies
                       borderwidth=1,
                       relief="flat",
                       font=self.font_mono_bold)

        # Selection colors
        style.map("GamingCopies.Treeview",
                 background=[('selected', self.colors['nvidia_green'])],
                 foreground=[('selected', 'black')])

        self.copies_tree = ttk.Treeview(tree_frame,
                                       columns=columns,
                                       show="headings",
                                       height=15,
                                       selectmode="extended",
                                       style="GamingCopies.Treeview")

        # Configure columns
        for col in columns:
            self.copies_tree.heading(col, text=col)
            if col == "Sel":
                self.copies_tree.column(col, width=40, anchor="center")
            elif col == "ID":
                self.copies_tree.column(col, width=80, anchor="center")
            elif col == "Title":
                self.copies_tree.column(col, width=300, anchor="w")
            elif col in ["Quality", "Type", "Rating", "Year", "Size"]:
                self.copies_tree.column(col, width=80, anchor="center")
            elif col == "Source":
                self.copies_tree.column(col, width=100, anchor="center")
            else:
                self.copies_tree.column(col, width=150, anchor="w")

        # Gaming tags for copies
        self.copies_tree.tag_configure("keep_item",
                                      background=self.colors['bg'],
                                      foreground=self.colors['nvidia_green'])

        self.copies_tree.tag_configure("delete_item",
                                      background=self.colors['bg'],
                                      foreground=self.colors['rog_red'])

        self.copies_tree.tag_configure("selected_keep",
                                      background=self.colors['nvidia_green'],
                                      foreground='black')

        # Scrollbars gaming
        v_scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=self.copies_tree.yview)
        h_scroll = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.copies_tree.xview)
        self.copies_tree.configure(yscrollcommand=v_scroll.set, xscrollcommand=h_scroll.set)

        # Pack treeview and scrollbars
        self.copies_tree.pack(side="left", fill="both", expand=True)
        v_scroll.pack(side="right", fill="y")
        h_scroll.pack(side="bottom", fill="x")

        # Bind events
        self.copies_tree.bind("<Button-1>", self.on_copies_click)

        # Store for later use
        self.current_manual_tmdb_id = tmdb_id
        self.current_manual_title = title
        self.selected_copies = set()

    def setup_gaming_manual_controls(self, window, parent, tmdb_id, title):
        """Setup gaming control buttons for manual selection"""
        controls_frame = tk.Frame(parent, bg=self.colors['bg'], height=60)
        controls_frame.pack(fill='x', pady=5)
        controls_frame.pack_propagate(False)

        # Quick selection buttons
        quick_frame = tk.Frame(controls_frame, bg=self.colors['bg'])
        quick_frame.pack(side='left', fill='y')

        tk.Button(quick_frame, text="🥇 Keep 4K Only",
                 bg=self.colors['nvidia_green'], fg='black', font=self.font_mono,
                 relief='flat', command=lambda: self.quick_select_quality('4K')).pack(side='left', padx=2)

        tk.Button(quick_frame, text="🥈 Keep FHD Only",
                 bg=self.colors['accent'], fg='white', font=self.font_mono,
                 relief='flat', command=lambda: self.quick_select_quality('FHD')).pack(side='left', padx=2)

        tk.Button(quick_frame, text="🔗 Keep Symlinks Only",
                 bg=self.colors['success'], fg='white', font=self.font_mono,
                 relief='flat', command=self.quick_select_symlinks).pack(side='left', padx=2)

        tk.Button(quick_frame, text="🧠 Smart Selection",
                 bg=self.colors['accent'], fg='white', font=self.font_mono,
                 relief='flat', command=self.smart_select_copies).pack(side='left', padx=2)

        # Action buttons
        action_frame = tk.Frame(controls_frame, bg=self.colors['bg'])
        action_frame.pack(side='right', fill='y')

        tk.Button(action_frame, text="💾 Save Selection",
                 bg=self.colors['nvidia_green'], fg='black', font=self.font_mono,
                 relief='flat', command=self.save_manual_selection).pack(side='left', padx=2)

        tk.Button(action_frame, text="🚀 Execute Cleanup",
                 bg=self.colors['rog_red'], fg='white', font=self.font_mono,
                 relief='flat', command=self.execute_manual_cleanup).pack(side='left', padx=2)

        tk.Button(action_frame, text="❌ Cancel",
                 bg=self.colors['surface'], fg=self.colors['fg'], font=self.font_mono,
                 relief='flat', command=window.destroy).pack(side='left', padx=2)

    def load_movie_copies_gaming(self, tmdb_id, title):
        """Load copies for specific movie in gaming style"""
        self.log_message(f"📊 Loading copies for {title} (TMDB {tmdb_id})", 'accent')

        def load_thread():
            try:
                # Get detailed copies using existing function
                copies = self.db.get_movie_copies_by_tmdb(tmdb_id)

                if not copies:
                    self.log_message(f"⚠️ No copies found for TMDB {tmdb_id}", 'warning')
                    return

                self.log_message(f"✅ Found {len(copies)} copies for {title}", 'success')

                # Clear existing data
                for item in self.copies_tree.get_children():
                    self.copies_tree.delete(item)

                self.selected_copies.clear()

                # Process and insert copies
                for i, copy in enumerate(copies):
                    stream_id = copy.get('stream_id', 'N/A')
                    copy_title = copy.get('title', 'Unknown')

                    # Detect quality
                    title_lower = copy_title.lower()
                    if '4k' in title_lower or '2160p' in title_lower:
                        quality = '4K'
                    elif '60fps' in title_lower:
                        quality = '60FPS'
                    elif 'fhd' in title_lower or '1080p' in title_lower:
                        quality = 'FHD'
                    elif 'hd' in title_lower or '720p' in title_lower:
                        quality = 'HD'
                    else:
                        quality = 'SD'

                    # Detect type
                    if copy.get('movie_symlink') == 1:
                        copy_type = 'Symlink'
                        source = '🔗 Link'
                    elif copy.get('direct_source'):
                        copy_type = 'Direct'
                        source = '📁 Source'
                    elif copy.get('direct_proxy'):
                        copy_type = 'Proxy'
                        source = '🌐 Proxy'
                    else:
                        copy_type = 'Other'
                        source = '❓ Other'

                    # Generate recommendation
                    if quality == '4K' and copy_type == 'Symlink':
                        recommendation = '🥇 KEEP - Best quality symlink'
                        initial_selected = True
                        tag = 'keep_item'
                    elif quality in ['FHD', '60FPS'] and copy_type == 'Symlink':
                        recommendation = '🥈 KEEP - Good quality symlink'
                        initial_selected = True
                        tag = 'keep_item'
                    elif copy_type == 'Symlink':
                        recommendation = '🥉 KEEP - Symlink priority'
                        initial_selected = True
                        tag = 'keep_item'
                    else:
                        recommendation = '🗑️ DELETE - Lower priority'
                        initial_selected = False
                        tag = 'delete_item'

                    # Insert with gaming colors
                    values = (
                        "☑" if initial_selected else "☐",
                        stream_id,
                        copy_title[:50] + "..." if len(copy_title) > 50 else copy_title,
                        quality,
                        copy_type,
                        source,
                        "N/A",  # Rating placeholder
                        "N/A",  # Year placeholder
                        "N/A",  # Size placeholder
                        recommendation
                    )

                    item = self.copies_tree.insert("", "end", values=values, tags=(tag,))

                    if initial_selected:
                        self.selected_copies.add(item)

                # Log summary
                selected_count = len(self.selected_copies)
                total_count = len(copies)
                delete_count = total_count - selected_count

                self.log_message(f"📊 Smart selection applied:", 'nvidia_green')
                self.log_message(f"  ✅ {selected_count} copies selected to KEEP", 'success')
                self.log_message(f"  🗑️ {delete_count} copies will be DELETED", 'rog_red')
                self.log_message(f"🎮 Use checkboxes to adjust selection", 'accent')

            except Exception as e:
                self.log_message(f"💥 ERROR loading copies: {e}", 'warning')

        threading.Thread(target=load_thread, daemon=True).start()

    def get_series_id_from_episode_data(self, series_title, season_num, episode_num):
        """Get series_id from current duplicate episodes data"""
        try:
            # Look for the series_id in the current duplicates data
            duplicates = self.db.get_duplicate_episodes()
            for dup in duplicates:
                if (dup['series_title'] == series_title and
                    dup['season_num'] == season_num and
                    dup['episode_num'] == episode_num):
                    return dup['series_id']

            # Fallback: search by title pattern
            series_list = self.db.get_series_by_title_pattern(series_title)
            if series_list:
                return series_list[0]['series_id']

            return None
        except Exception as e:
            self.log_message(f"⚠️ Error getting series_id: {e}", 'warning')
            return None

    def open_gaming_episode_selection(self, series_id, series_title, season_num, episode_num, episode_str):
        """Open gaming manual selection window for specific episode"""
        self.log_message(f"🎮 Opening gaming episode selection window...", 'nvidia_green')

        # Create gaming manual selection window
        manual_window = tk.Toplevel(self.root)
        manual_window.title(f"⚡ Gaming Episode Selection - {series_title} {episode_str}")
        manual_window.geometry("1200x800")
        manual_window.configure(bg=self.colors['bg'])

        # Make window modal
        manual_window.transient(self.root)
        manual_window.grab_set()

        # Header gaming
        header_frame = tk.Frame(manual_window, bg=self.colors['surface'], height=50)
        header_frame.pack(fill='x', padx=10, pady=5)
        header_frame.pack_propagate(False)

        # Gaming title with episode info
        title_label = tk.Label(header_frame,
                              text=f"⚡ EPISODE SELECTION: {series_title} S{season_num:02d}E{episode_num:02d}",
                              font=self.font_mono_bold,
                              bg=self.colors['surface'],
                              fg=self.colors['nvidia_green'])
        title_label.pack(expand=True)

        # Main content frame
        content_frame = tk.Frame(manual_window, bg=self.colors['bg'])
        content_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Episode copies treeview
        tree_frame = tk.Frame(content_frame, bg=self.colors['bg'])
        tree_frame.pack(fill='both', expand=True, pady=5)

        # Create treeview for episode copies
        columns = ('select', 'episode_id', 'stream_id', 'title', 'quality', 'type', 'source', 'priority', 'recommendation')
        episode_copies_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        # Configure columns
        episode_copies_tree.heading('select', text='☐')
        episode_copies_tree.heading('episode_id', text='Episode ID')
        episode_copies_tree.heading('stream_id', text='Stream ID')
        episode_copies_tree.heading('title', text='Episode Title')
        episode_copies_tree.heading('quality', text='Quality')
        episode_copies_tree.heading('type', text='Type')
        episode_copies_tree.heading('source', text='Source')
        episode_copies_tree.heading('priority', text='Priority')
        episode_copies_tree.heading('recommendation', text='Recommendation')

        # Configure column widths
        episode_copies_tree.column('select', width=40)
        episode_copies_tree.column('episode_id', width=80)
        episode_copies_tree.column('stream_id', width=80)
        episode_copies_tree.column('title', width=200)
        episode_copies_tree.column('quality', width=80)
        episode_copies_tree.column('type', width=80)
        episode_copies_tree.column('source', width=100)
        episode_copies_tree.column('priority', width=80)
        episode_copies_tree.column('recommendation', width=150)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=episode_copies_tree.yview)
        episode_copies_tree.configure(yscrollcommand=scrollbar.set)

        episode_copies_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Control buttons frame
        buttons_frame = tk.Frame(content_frame, bg=self.colors['bg'])
        buttons_frame.pack(fill='x', pady=10)

        # Quick selection buttons
        tk.Button(buttons_frame, text="🔗 Select Symlinks",
                 bg=self.colors['nvidia_green'], fg='black', font=self.font_mono,
                 relief='flat', command=lambda: self.quick_select_episode_symlinks(episode_copies_tree)).pack(side='left', padx=2)

        tk.Button(buttons_frame, text="🥇 Select Best Quality",
                 bg=self.colors['success'], fg='white', font=self.font_mono,
                 relief='flat', command=lambda: self.quick_select_episode_quality(episode_copies_tree)).pack(side='left', padx=2)

        tk.Button(buttons_frame, text="🧠 Smart Select",
                 bg=self.colors['accent'], fg='white', font=self.font_mono,
                 relief='flat', command=lambda: self.smart_select_episodes(episode_copies_tree)).pack(side='left', padx=2)

        # Action buttons
        tk.Button(buttons_frame, text="💾 Save Selection",
                 bg=self.colors['rog_red'], fg='white', font=self.font_mono,
                 relief='flat', command=lambda: self.save_episode_selection(episode_copies_tree, series_id, series_title, season_num, episode_num)).pack(side='right', padx=2)

        tk.Button(buttons_frame, text="❌ Cancel",
                 bg=self.colors['surface'], fg=self.colors['fg'], font=self.font_mono,
                 relief='flat', command=manual_window.destroy).pack(side='right', padx=2)

        # Store references for the functions
        self.current_episode_tree = episode_copies_tree
        self.current_episode_window = manual_window
        self.selected_episode_copies = set()

        # Load episode copies
        self.load_episode_copies_gaming(series_id, series_title, season_num, episode_num, episode_copies_tree)

        self.log_message(f"✅ Gaming episode selection window opened for {episode_str}", 'success')

    def load_episode_copies_gaming(self, series_id, series_title, season_num, episode_num, tree):
        """Load copies for specific episode in gaming style"""
        self.log_message(f"📊 Loading copies for {series_title} S{season_num:02d}E{episode_num:02d}", 'accent')

        def load_thread():
            try:
                # Validate series_id
                if not series_id:
                    self.log_message(f"⚠️ No series_id provided for: {series_title}", 'warning')
                    return

                # Get detailed copies using the series_id directly
                copies = self.db.get_episode_copies_details(series_id, season_num, episode_num)

                if not copies:
                    self.log_message(f"⚠️ No copies found for S{season_num:02d}E{episode_num:02d}", 'warning')
                    return

                self.log_message(f"✅ Found {len(copies)} copies for episode", 'success')

                # Clear existing data
                for item in tree.get_children():
                    tree.delete(item)

                self.selected_episode_copies.clear()

                # Process and insert copies
                for i, copy in enumerate(copies):
                    episode_id = copy.get('episode_id', 'N/A')
                    stream_id = copy.get('stream_id', 'N/A')
                    copy_title = copy.get('title', 'Unknown')

                    # Detect quality from title
                    title_lower = copy_title.lower()
                    if '4k' in title_lower or '2160p' in title_lower:
                        quality = '4K'
                    elif '60fps' in title_lower:
                        quality = '60FPS'
                    elif 'fhd' in title_lower or '1080p' in title_lower:
                        quality = 'FHD'
                    elif 'hd' in title_lower or '720p' in title_lower:
                        quality = 'HD'
                    else:
                        quality = 'SD'

                    # Detect type and source
                    if copy.get('movie_symlink') == 1:
                        copy_type = 'Symlink'
                        source = '🔗 Link'
                        priority = 'HIGH'
                    elif copy.get('direct_source') == 1:
                        copy_type = 'Direct'
                        source = '📁 Source'
                        priority = 'MEDIUM'
                    elif copy.get('direct_proxy') == 1:
                        copy_type = 'Proxy'
                        source = '🌐 Proxy'
                        priority = 'LOW'
                    else:
                        copy_type = 'Other'
                        source = '❓ Other'
                        priority = 'LOW'

                    # Generate recommendation based on priority and quality
                    if quality == '4K' and copy_type == 'Symlink':
                        recommendation = '🥇 KEEP - Best quality symlink'
                        initial_selected = True
                        tag = 'keep_item'
                    elif quality in ['FHD', '60FPS'] and copy_type == 'Symlink':
                        recommendation = '🥈 KEEP - Good quality symlink'
                        initial_selected = True
                        tag = 'keep_item'
                    elif copy_type == 'Symlink':
                        recommendation = '🥉 KEEP - Symlink priority'
                        initial_selected = True
                        tag = 'keep_item'
                    elif copy_type == 'Direct' and quality in ['4K', 'FHD']:
                        recommendation = '📁 CONSIDER - Good direct source'
                        initial_selected = False
                        tag = 'consider_item'
                    else:
                        recommendation = '🗑️ DELETE - Lower priority'
                        initial_selected = False
                        tag = 'delete_item'

                    # Insert with gaming colors
                    values = (
                        "☑" if initial_selected else "☐",
                        episode_id,
                        stream_id,
                        copy_title[:40] + "..." if len(copy_title) > 40 else copy_title,
                        quality,
                        copy_type,
                        source,
                        priority,
                        recommendation
                    )

                    item = tree.insert("", "end", values=values, tags=(tag,))

                    if initial_selected:
                        self.selected_episode_copies.add(item)

                # Configure gaming colors for episode tree
                tree.tag_configure('keep_item', background=self.colors['success'], foreground='white')
                tree.tag_configure('delete_item', background=self.colors['rog_red'], foreground='white')
                tree.tag_configure('consider_item', background=self.colors['warning'], foreground='black')

                # Bind click events
                tree.bind("<Button-1>", lambda e: self.on_episode_copies_click(e, tree))

                # Log summary
                selected_count = len(self.selected_episode_copies)
                total_count = len(copies)
                delete_count = total_count - selected_count

                self.log_message(f"📊 Smart selection applied:", 'nvidia_green')
                self.log_message(f"  ✅ {selected_count} copies selected to KEEP", 'success')
                self.log_message(f"  🗑️ {delete_count} copies will be DELETED", 'rog_red')
                self.log_message(f"🎮 Use checkboxes to adjust selection", 'accent')

            except Exception as e:
                self.log_message(f"💥 ERROR loading episode copies: {e}", 'warning')

        threading.Thread(target=load_thread, daemon=True).start()

    def on_episode_copies_click(self, event, tree):
        """Handle click on episode copies treeview"""
        item = tree.identify('item', event.x, event.y)
        column = tree.identify('column', event.x, event.y)

        if item and column == '#1':  # Selection column
            current_values = list(tree.item(item, 'values'))

            if current_values[0] == "☐":
                current_values[0] = "☑"
                self.selected_episode_copies.add(item)
                tree.item(item, values=current_values, tags=("selected_keep",))
                self.log_message(f"✅ Selected to KEEP: {current_values[3]}", 'success')
            else:
                current_values[0] = "☐"
                self.selected_episode_copies.discard(item)
                tree.item(item, values=current_values, tags=("delete_item",))
                self.log_message(f"🗑️ Marked for DELETE: {current_values[3]}", 'rog_red')

    def quick_select_episode_symlinks(self, tree):
        """Quick select symlinks only for episodes"""
        self.log_message(f"🔗 Quick selecting episode symlinks only", 'nvidia_green')

        self.selected_episode_copies.clear()

        for item in tree.get_children():
            values = list(tree.item(item, 'values'))
            copy_type = values[5]  # Type column

            if copy_type == 'Symlink':
                values[0] = "☑"
                self.selected_episode_copies.add(item)
                tree.item(item, values=values, tags=("selected_keep",))
            else:
                values[0] = "☐"
                tree.item(item, values=values, tags=("delete_item",))

        self.log_message(f"✅ Selected {len(self.selected_episode_copies)} symlinks", 'success')

    def quick_select_episode_quality(self, tree):
        """Quick select best quality episodes"""
        self.log_message(f"🥇 Quick selecting best quality episodes", 'nvidia_green')

        self.selected_episode_copies.clear()

        # Find best quality available
        qualities = []
        for item in tree.get_children():
            values = tree.item(item, 'values')
            quality = values[4]  # Quality column
            qualities.append(quality)

        # Priority order: 4K > 60FPS > FHD > HD > SD
        quality_priority = ['4K', '60FPS', 'FHD', 'HD', 'SD']
        best_quality = None
        for q in quality_priority:
            if q in qualities:
                best_quality = q
                break

        if best_quality:
            for item in tree.get_children():
                values = list(tree.item(item, 'values'))
                quality = values[4]

                if quality == best_quality:
                    values[0] = "☑"
                    self.selected_episode_copies.add(item)
                    tree.item(item, values=values, tags=("selected_keep",))
                else:
                    values[0] = "☐"
                    tree.item(item, values=values, tags=("delete_item",))

            self.log_message(f"✅ Selected {len(self.selected_episode_copies)} {best_quality} episodes", 'success')

    def smart_select_episodes(self, tree):
        """Smart selection for episodes"""
        self.log_message(f"🧠 Applying smart selection for episodes", 'nvidia_green')

        # This function is already called when loading, so just log current state
        selected_count = len(self.selected_episode_copies)
        total_count = len(tree.get_children())

        self.log_message(f"✅ Smart selection: {selected_count}/{total_count} episodes selected", 'success')

    def save_episode_selection(self, tree, series_id, series_title, season_num, episode_num):
        """Save episode selection and execute cleanup"""
        selected_count = len(self.selected_episode_copies)
        total_count = len(tree.get_children())
        delete_count = total_count - selected_count

        if delete_count == 0:
            self.log_message(f"⚠️ No episode copies selected for deletion", 'warning')
            return

        # Confirmation dialog
        import tkinter.messagebox as msgbox
        confirm = msgbox.askyesno(
            "⚡ Episode Cleanup Confirmation",
            f"📺 Series: {series_title}\n"
            f"📺 Episode: S{season_num:02d}E{episode_num:02d}\n\n"
            f"✅ KEEP: {selected_count} copies\n"
            f"🗑️ DELETE: {delete_count} copies\n\n"
            f"⚠️ This action cannot be undone!\n\n"
            f"🚀 Execute episode cleanup?",
            icon='warning'
        )

        if not confirm:
            self.log_message(f"❌ Episode cleanup cancelled by user", 'accent')
            return

        self.log_message(f"🚀 Executing episode cleanup for S{season_num:02d}E{episode_num:02d}", 'rog_red')

        def cleanup_thread():
            try:
                # Get episode IDs and stream IDs to delete (unselected items)
                delete_episode_ids = []
                delete_stream_ids = []
                keep_episode_ids = []

                for item in tree.get_children():
                    values = tree.item(item, 'values')
                    episode_id = values[1]
                    stream_id = values[2]

                    if item in self.selected_episode_copies:
                        keep_episode_ids.append(episode_id)
                    else:
                        delete_episode_ids.append(episode_id)
                        delete_stream_ids.append(stream_id)

                self.log_message(f"🗑️ Deleting {len(delete_episode_ids)} episode copies...", 'rog_red')

                # Execute deletion using database function
                if delete_episode_ids:
                    deleted_count = 0
                    failed_count = 0

                    for episode_id in delete_episode_ids:
                        try:
                            result = self.db.delete_duplicate_episode(int(episode_id))
                            if result:
                                deleted_count += 1
                            else:
                                failed_count += 1
                        except Exception as e:
                            self.log_message(f"⚠️ Failed to delete episode ID {episode_id}: {e}", 'warning')
                            failed_count += 1

                    if deleted_count > 0:
                        self.log_message(f"✅ Successfully deleted {deleted_count} episode copies", 'success')
                        if failed_count > 0:
                            self.log_message(f"⚠️ Failed to delete {failed_count} episode copies", 'warning')
                        self.log_message(f"💾 Kept {len(keep_episode_ids)} episode copies", 'nvidia_green')
                        self.log_message(f"🎉 Episode cleanup completed for S{season_num:02d}E{episode_num:02d}!", 'nvidia_green')

                        # Close the window after successful cleanup
                        if hasattr(self, 'current_episode_window'):
                            self.current_episode_window.destroy()
                    else:
                        self.log_message(f"❌ No episode copies were deleted", 'warning')
                else:
                    self.log_message(f"⚠️ No episode copies to delete", 'warning')

            except Exception as e:
                self.log_message(f"💥 ERROR during episode cleanup: {e}", 'warning')

        threading.Thread(target=cleanup_thread, daemon=True).start()

    def on_copies_click(self, event):
        """Handle click on copies treeview"""
        item = self.copies_tree.identify('item', event.x, event.y)
        column = self.copies_tree.identify('column', event.x, event.y)

        if item and column == '#1':  # Selection column
            current_values = list(self.copies_tree.item(item, 'values'))

            if current_values[0] == "☐":
                current_values[0] = "☑"
                self.selected_copies.add(item)
                self.copies_tree.item(item, values=current_values, tags=("selected_keep",))
                self.log_message(f"✅ Selected to KEEP: {current_values[2]}", 'success')
            else:
                current_values[0] = "☐"
                self.selected_copies.discard(item)
                self.copies_tree.item(item, values=current_values, tags=("delete_item",))
                self.log_message(f"🗑️ Marked for DELETE: {current_values[2]}", 'rog_red')

    def quick_select_quality(self, target_quality):
        """Quick select by quality"""
        self.log_message(f"🎯 Quick selecting {target_quality} copies only", 'nvidia_green')

        self.selected_copies.clear()

        for item in self.copies_tree.get_children():
            values = list(self.copies_tree.item(item, 'values'))
            quality = values[3]

            if quality == target_quality:
                values[0] = "☑"
                self.selected_copies.add(item)
                self.copies_tree.item(item, values=values, tags=("selected_keep",))
            else:
                values[0] = "☐"
                self.copies_tree.item(item, values=values, tags=("delete_item",))

        self.log_message(f"✅ Selected {len(self.selected_copies)} {target_quality} copies", 'success')

    def quick_select_symlinks(self):
        """Quick select symlinks only"""
        self.log_message(f"🔗 Quick selecting symlinks only", 'nvidia_green')

        self.selected_copies.clear()

        for item in self.copies_tree.get_children():
            values = list(self.copies_tree.item(item, 'values'))
            copy_type = values[4]

            if copy_type == 'Symlink':
                values[0] = "☑"
                self.selected_copies.add(item)
                self.copies_tree.item(item, values=values, tags=("selected_keep",))
            else:
                values[0] = "☐"
                self.copies_tree.item(item, values=values, tags=("delete_item",))

        self.log_message(f"✅ Selected {len(self.selected_copies)} symlinks", 'success')

    def smart_select_copies(self):
        """Smart selection for copies"""
        self.log_message(f"🧠 Applying smart selection algorithm", 'nvidia_green')

        # This function is already called when loading, so just log
        selected_count = len(self.selected_copies)
        total_count = len(self.copies_tree.get_children())

        self.log_message(f"✅ Smart selection: {selected_count}/{total_count} copies selected", 'success')

    def save_manual_selection(self):
        """Save manual selection"""
        selected_count = len(self.selected_copies)
        total_count = len(self.copies_tree.get_children())
        delete_count = total_count - selected_count

        self.log_message(f"💾 Saving manual selection for {self.current_manual_title}", 'nvidia_green')
        self.log_message(f"📊 Selection summary:", 'accent')
        self.log_message(f"  ✅ {selected_count} copies will be KEPT", 'success')
        self.log_message(f"  🗑️ {delete_count} copies will be DELETED", 'rog_red')
        self.log_message(f"💾 Selection saved! Use 'Execute Cleanup' to apply changes.", 'success')

    def execute_manual_cleanup(self):
        """Execute manual cleanup"""
        selected_count = len(self.selected_copies)
        total_count = len(self.copies_tree.get_children())
        delete_count = total_count - selected_count

        if delete_count == 0:
            self.log_message(f"⚠️ No copies selected for deletion", 'warning')
            return

        # Confirmation dialog
        import tkinter.messagebox as msgbox
        confirm = msgbox.askyesno(
            "⚡ Gaming Cleanup Confirmation",
            f"🎬 Movie: {self.current_manual_title}\n\n"
            f"✅ KEEP: {selected_count} copies\n"
            f"🗑️ DELETE: {delete_count} copies\n\n"
            f"⚠️ This action cannot be undone!\n\n"
            f"🚀 Execute cleanup?",
            icon='warning'
        )

        if not confirm:
            self.log_message(f"❌ Cleanup cancelled by user", 'accent')
            return

        self.log_message(f"🚀 Executing manual cleanup for {self.current_manual_title}", 'rog_red')

        def cleanup_thread():
            try:
                # Get IDs to delete (unselected items)
                delete_ids = []
                keep_ids = []

                for item in self.copies_tree.get_children():
                    values = self.copies_tree.item(item, 'values')
                    stream_id = values[1]

                    if item in self.selected_copies:
                        keep_ids.append(stream_id)
                    else:
                        delete_ids.append(stream_id)

                self.log_message(f"🗑️ Deleting {len(delete_ids)} copies...", 'rog_red')

                # Execute deletion using existing function
                if delete_ids:
                    # Convert string IDs to integers
                    delete_ids_int = []
                    for stream_id in delete_ids:
                        try:
                            delete_ids_int.append(int(stream_id))
                        except ValueError:
                            self.log_message(f"⚠️ Invalid stream ID: {stream_id}", 'warning')

                    if delete_ids_int:
                        result = self.db.delete_multiple_movies(delete_ids_int)
                        deleted_count = result.get('deleted', 0)
                        failed_count = result.get('failed', 0)

                        if deleted_count > 0:
                            self.log_message(f"✅ Successfully deleted {deleted_count} copies", 'success')
                            if failed_count > 0:
                                self.log_message(f"⚠️ Failed to delete {failed_count} copies", 'warning')
                            self.log_message(f"💾 Kept {len(keep_ids)} copies", 'nvidia_green')
                            self.log_message(f"🎉 Manual cleanup completed for {self.current_manual_title}!", 'nvidia_green')
                        else:
                            self.log_message(f"❌ No copies were deleted (may not be movies or don't exist)", 'warning')
                    else:
                        self.log_message(f"❌ No valid stream IDs to delete", 'warning')
                else:
                    self.log_message(f"⚠️ No copies to delete", 'warning')

            except Exception as e:
                self.log_message(f"💥 ERROR during cleanup: {e}", 'warning')

        threading.Thread(target=cleanup_thread, daemon=True).start()

    def smart_select_duplicates_gaming(self):
        """Selección inteligente gaming con preview - MEJORADO"""
        if not self.current_duplicates:
            self.log_message("❌ No duplicates loaded! Load TMDB duplicates first.", 'warning')
            return

        self.log_operation_start("SMART SELECTION ALGORITHM")
        self.log_message("🧠 Analyzing quality priorities: 4K > 60FPS > FHD > HD > SD", 'accent')
        self.log_message("🔗 Prioritizing symlinks over direct sources", 'accent')
        self.log_message("🎯 Processing duplicate groups for intelligent selection...", 'accent')

        def smart_select_thread():
            try:
                # Prepare preview data
                headers = ["TMDB", "Title", "Action", "Reason", "Keep", "Delete"]
                preview_data = []
                total_to_delete = 0
                total_to_keep = 0

                total_to_process = min(10, len(self.current_duplicates))
                self.log_message(f"📊 Processing {total_to_process} movies for smart selection preview...", 'accent')

                for i, dup in enumerate(self.current_duplicates[:10]):  # Process first 10 for preview
                    tmdb_id = dup['tmdb_id']
                    title = dup['sample_title']

                    # Show progress
                    self.log_progress(i + 1, total_to_process, title[:25])

                    # Get copies for this movie
                    copies = self.db.get_movie_copies_by_tmdb(tmdb_id)

                    if len(copies) <= 1:
                        continue

                    # Apply smart selection logic (simplified)
                    keep_ids = []
                    delete_ids = []
                    reason = ""

                    # Find best quality symlinks first
                    symlinks_4k = [c for c in copies if c.get('movie_symlink') and '4k' in c.get('stream_display_name', '').lower()]
                    symlinks_fhd = [c for c in copies if c.get('movie_symlink') and ('fhd' in c.get('stream_display_name', '').lower() or '1080p' in c.get('stream_display_name', '').lower())]
                    direct_sources = [c for c in copies if not c.get('movie_symlink')]

                    if symlinks_4k:
                        keep_ids = [symlinks_4k[0]['id']]  # Keep first 4K symlink
                        delete_ids = [c['id'] for c in copies if c['id'] != keep_ids[0]]
                        reason = "Keep 4K symlink"
                    elif symlinks_fhd:
                        keep_ids = [symlinks_fhd[0]['id']]  # Keep first FHD symlink
                        delete_ids = [c['id'] for c in copies if c['id'] != keep_ids[0]]
                        reason = "Keep FHD symlink"
                    elif direct_sources:
                        # Keep newest direct source
                        newest = max(direct_sources, key=lambda x: x.get('id', 0))
                        keep_ids = [newest['id']]
                        delete_ids = [c['id'] for c in copies if c['id'] != keep_ids[0]]
                        reason = "Keep newest direct"
                    else:
                        keep_ids = [copies[0]['id']]  # Keep first as fallback
                        delete_ids = [c['id'] for c in copies[1:]]
                        reason = "Keep first copy"

                    # Add to preview
                    row = [
                        tmdb_id,
                        title[:20],
                        "SMART",
                        reason,
                        len(keep_ids),
                        len(delete_ids)
                    ]
                    preview_data.append(row)
                    total_to_keep += len(keep_ids)
                    total_to_delete += len(delete_ids)

                # Show preview in data view
                self.show_data_table("🎯 SMART SELECTION PREVIEW", headers, preview_data, 'rog')

                # Log results
                self.log_message(f"📊 Analysis complete: {total_to_keep} to keep, {total_to_delete} to delete", 'success')
                self.log_message("🎮 Smart selection preview ready!", 'nvidia_green')
                self.log_message("⭐ Use 'Advanced Cleanup' to execute the plan", 'accent')
                self.log_operation_end("SMART SELECTION ALGORITHM", True)

            except Exception as e:
                self.log_message(f"💥 ERROR in smart selection: {e}", 'warning')
                self.log_operation_end("SMART SELECTION ALGORITHM", False)

        threading.Thread(target=smart_select_thread, daemon=True).start()

    def execute_advanced_cleanup_gaming(self):
        """Limpieza avanzada gaming con explicación detallada"""
        if not self.current_duplicates:
            self.log_message("❌ No duplicates loaded! Load TMDB duplicates first.", 'warning')
            return

        # Explain what Advanced Cleanup does
        self.log_message("⭐ ADVANCED CLEANUP - EXPLANATION", 'nvidia_green')
        self.log_message("═" * 60, 'accent')
        self.log_message("🧠 WHAT ADVANCED CLEANUP DOES:", 'accent')
        self.log_message("", 'fg')
        self.log_message("📋 INTELLIGENT PRIORITY SYSTEM:", 'nvidia_green')
        self.log_message("  1. 🥇 KEEPS: 4K Symlinks (highest priority)", 'success')
        self.log_message("  2. 🥈 KEEPS: 60FPS Symlinks (high priority)", 'success')
        self.log_message("  3. 🥉 KEEPS: FHD/1080p Symlinks (medium priority)", 'success')
        self.log_message("  4. 🔗 KEEPS: Any other Symlinks (low priority)", 'success')
        self.log_message("  5. 📁 KEEPS: Newest Direct Source (if no symlinks)", 'accent')
        self.log_message("", 'fg')
        self.log_message("🗑️ DELETES AUTOMATICALLY:", 'rog_red')
        self.log_message("  • All lower quality copies", 'warning')
        self.log_message("  • All direct sources when symlinks exist", 'warning')
        self.log_message("  • All older copies of same quality", 'warning')
        self.log_message("", 'fg')
        self.log_message("✅ SAFETY FEATURES:", 'nvidia_green')
        self.log_message("  • Always keeps at least 1 copy per movie", 'success')
        self.log_message("  • Only processes selected movies (if any selected)", 'success')
        self.log_message("  • Shows detailed results for each movie", 'success')
        self.log_message("", 'fg')
        self.log_message("⚠️ DIFFERENCE FROM MASS CLEANUP:", 'accent')
        self.log_message("  • Advanced: Processes only loaded/selected movies", 'accent')
        self.log_message("  • Mass: Processes ALL duplicates in database", 'warning')
        self.log_message("  • Advanced: More conservative and selective", 'accent')
        self.log_message("  • Mass: More aggressive and comprehensive", 'warning')
        self.log_message("═" * 60, 'accent')

        # Ask for confirmation
        import tkinter.messagebox as msgbox

        selected_count = len(self.selected_items) if hasattr(self, 'selected_items') else 0
        total_loaded = len(self.current_duplicates)

        if selected_count > 0:
            process_count = selected_count
            process_type = f"{selected_count} SELECTED movies"
        else:
            process_count = total_loaded
            process_type = f"ALL {total_loaded} loaded movies"

        confirmation_text = f"""⭐ ADVANCED CLEANUP CONFIRMATION

🎯 WHAT WILL BE PROCESSED:
• {process_type}
• Uses intelligent priority system
• Keeps best quality symlinks
• Deletes lower priority copies

🧠 INTELLIGENT LOGIC:
• 4K Symlinks → ALWAYS KEPT
• FHD/60FPS Symlinks → KEPT if no 4K
• Other Symlinks → KEPT if no better quality
• Direct Sources → ONLY if no symlinks exist

✅ SAFETY MEASURES:
• Always keeps at least 1 copy per movie
• More conservative than Mass Cleanup
• Shows detailed results per movie
• Can be stopped if issues occur

⚠️ This operation cannot be undone!

🚀 Proceed with Advanced Cleanup?"""

        result = msgbox.askyesno(
            "⭐ Advanced Cleanup - Confirmation",
            confirmation_text,
            icon='question'
        )

        if not result:
            self.log_message("❌ Advanced cleanup cancelled by user", 'accent')
            return

        self.log_message("✅ User confirmed advanced cleanup - proceeding", 'nvidia_green')
        self.log_message("⭐ Starting advanced cleanup process...", 'nvidia_green')
        self.log_message("🔥 Applying intelligent priority-based deletion logic", 'rog_red')

        def cleanup_thread():
            try:
                # Prepare results data
                headers = ["TMDB", "Title", "Status", "Deleted", "Kept", "Saved"]
                results_data = []
                total_deleted = 0
                total_kept = 0
                total_saved_mb = 0

                processed = 0
                for dup in self.current_duplicates[:5]:  # Process first 5 for demo
                    processed += 1
                    tmdb_id = dup['tmdb_id']
                    title = dup['sample_title']

                    self.log_message(f"🎬 Processing: {title} (TMDB {tmdb_id})", 'accent')

                    # Simulate advanced cleanup
                    try:
                        result = self.db.apply_advanced_priority_cleanup(tmdb_id, auto_confirm=True)

                        deleted = result.get('deleted', 0)
                        kept = result.get('kept', 0)
                        saved_mb = deleted * 1500  # Estimate 1.5GB per movie

                        status = "✅ SUCCESS"
                        if deleted == 0:
                            status = "⚠️ NO ACTION"

                        # Add to results
                        row = [
                            tmdb_id,
                            title[:15],
                            status,
                            deleted,
                            kept,
                            f"{saved_mb}MB"
                        ]
                        results_data.append(row)

                        total_deleted += deleted
                        total_kept += kept
                        total_saved_mb += saved_mb

                        self.log_message(f"  ✅ {deleted} deleted, {kept} kept", 'success')

                    except Exception as e:
                        self.log_message(f"  ❌ Error: {str(e)[:50]}", 'warning')
                        row = [tmdb_id, title[:15], "❌ ERROR", 0, 0, "0MB"]
                        results_data.append(row)

                # Show results in data view
                self.show_data_table("⭐ ADVANCED CLEANUP RESULTS", headers, results_data, 'nvidia')

                # Final summary
                self.log_message("═" * 50, 'fg')
                self.log_message("🏆 ADVANCED CLEANUP COMPLETED!", 'nvidia_green')
                self.log_message(f"📊 Movies processed: {processed}", 'accent')
                self.log_message(f"🗑️ Total deleted: {total_deleted}", 'rog_red')
                self.log_message(f"💾 Total kept: {total_kept}", 'success')
                self.log_message(f"💽 Storage saved: {total_saved_mb/1000:.1f} GB", 'nvidia_green')
                self.log_message("🎮 Gaming-grade cleanup performance achieved!", 'accent')

            except Exception as e:
                self.log_message(f"💥 CRITICAL ERROR in cleanup: {e}", 'warning')

        threading.Thread(target=cleanup_thread, daemon=True).start()

    def preview_cleanup_gaming(self):
        """Vista previa de limpieza gaming"""
        if not self.current_duplicates:
            self.log_message("❌ No duplicates loaded! Load TMDB duplicates first.", 'warning')
            return

        self.log_message("👁️ Generating cleanup preview...", 'accent')

        def preview_thread():
            try:
                headers = ["TMDB", "Title", "Total", "Keep", "Delete", "Logic"]
                preview_data = []

                for dup in self.current_duplicates[:10]:
                    tmdb_id = dup['tmdb_id']
                    title = dup['sample_title']
                    total = dup['total_copies']

                    # Simulate preview logic
                    keep = 1
                    delete = total - 1
                    logic = "Keep best quality"

                    if total <= 1:
                        logic = "No duplicates"
                        delete = 0

                    row = [tmdb_id, title[:20], total, keep, delete, logic]
                    preview_data.append(row)

                self.show_data_table("👁️ CLEANUP PREVIEW", headers, preview_data, 'accent')
                self.log_message("✅ Preview generated! Review before cleanup.", 'success')

            except Exception as e:
                self.log_message(f"💥 ERROR in preview: {e}", 'warning')

        threading.Thread(target=preview_thread, daemon=True).start()

    def mass_cleanup_gaming(self):
        """Limpieza masiva gaming con resumen y backup"""
        if not self.is_connected:
            self.log_message("❌ Database not connected!", 'warning')
            return

        if not self.current_duplicates:
            self.log_message("❌ No duplicates loaded! Load TMDB duplicates first.", 'warning')
            return

        self.log_message("🔥 INITIATING MASS CLEANUP ANALYSIS", 'rog_red')
        self.log_message("📊 Analyzing all duplicate movies for mass cleanup...", 'accent')

        def mass_cleanup_thread():
            try:
                # Analyze what will be deleted
                self.log_message("🔍 Generating detailed cleanup summary...", 'accent')

                total_movies = len(self.current_duplicates)
                total_copies_to_delete = 0
                total_copies_to_keep = 0
                movies_summary = []

                for i, dup in enumerate(self.current_duplicates[:10]):  # Analyze first 10 for summary
                    tmdb_id = dup.get('tmdb_id', 0)
                    title = dup.get('sample_title', 'Unknown')
                    total_copies = dup.get('duplicate_count', 0)

                    # Get copies for analysis
                    copies = self.db.get_movie_copies_by_tmdb(tmdb_id)

                    if len(copies) <= 1:
                        continue

                    # Apply mass cleanup logic (keep best, delete rest)
                    keep_count = 1  # Always keep 1 copy
                    delete_count = len(copies) - 1

                    # Determine what to keep (priority: 4K symlink > FHD symlink > newest)
                    best_copy = "Unknown"
                    for copy in copies:
                        copy_title = copy.get('title', copy.get('stream_display_name', ''))
                        if '4k' in copy_title.lower() and copy.get('movie_symlink'):
                            best_copy = "4K Symlink"
                            break
                        elif 'fhd' in copy_title.lower() and copy.get('movie_symlink'):
                            best_copy = "FHD Symlink"
                        elif copy.get('movie_symlink') and best_copy == "Unknown":
                            best_copy = "Best Symlink"
                        elif best_copy == "Unknown":
                            best_copy = "Newest Copy"

                    movies_summary.append({
                        'title': title,
                        'tmdb_id': tmdb_id,
                        'total': len(copies),
                        'keep': keep_count,
                        'delete': delete_count,
                        'best_copy': best_copy
                    })

                    total_copies_to_keep += keep_count
                    total_copies_to_delete += delete_count

                # Show detailed summary
                self.log_message("═" * 70, 'rog_red')
                self.log_message("🔥 MASS CLEANUP SUMMARY", 'rog_red')
                self.log_message("═" * 70, 'rog_red')
                self.log_message(f"📊 Total movies to process: {total_movies}", 'accent')
                self.log_message(f"💾 Total copies to KEEP: {total_copies_to_keep}", 'nvidia_green')
                self.log_message(f"🗑️ Total copies to DELETE: {total_copies_to_delete}", 'rog_red')
                self.log_message("", 'fg')
                self.log_message("📋 Sample of what will happen (first 10 movies):", 'accent')
                self.log_message("", 'fg')

                for movie in movies_summary:
                    self.log_message(f"🎬 {movie['title'][:30]:<30} | Keep: {movie['keep']} ({movie['best_copy']}) | Delete: {movie['delete']}", 'fg')

                if len(self.current_duplicates) > 10:
                    remaining = len(self.current_duplicates) - 10
                    self.log_message(f"... and {remaining} more movies will be processed", 'accent')

                self.log_message("", 'fg')
                self.log_message("⚠️ IMPORTANT WARNINGS:", 'warning')
                self.log_message("  • This operation CANNOT be undone", 'warning')
                self.log_message("  • ALL duplicate copies will be deleted automatically", 'warning')
                self.log_message("  • Only the BEST copy of each movie will be kept", 'warning')
                self.log_message("  • A database backup will be created before execution", 'nvidia_green')
                self.log_message("═" * 70, 'rog_red')

                # Ask for confirmation
                self.show_mass_cleanup_confirmation(total_movies, total_copies_to_keep, total_copies_to_delete)

            except Exception as e:
                self.log_message(f"💥 ERROR analyzing mass cleanup: {e}", 'warning')

        threading.Thread(target=mass_cleanup_thread, daemon=True).start()

    def show_mass_cleanup_confirmation(self, total_movies, total_keep, total_delete):
        """Show confirmation dialog for mass cleanup"""
        import tkinter.messagebox as msgbox

        confirmation_text = f"""🔥 MASS CLEANUP CONFIRMATION

📊 OPERATION SUMMARY:
• Movies to process: {total_movies}
• Copies to KEEP: {total_keep}
• Copies to DELETE: {total_delete}

⚠️ CRITICAL WARNINGS:
• This operation CANNOT be undone
• ALL duplicates will be deleted automatically
• Only the BEST copy of each movie will remain
• Process will take several minutes

✅ SAFETY MEASURES:
• Database backup will be created automatically
• You can restore from backup if needed
• Operation can be cancelled at any time

🚀 Do you want to proceed with MASS CLEANUP?

⚠️ THERE IS NO UNDO - PROCEED WITH CAUTION!"""

        result = msgbox.askyesnocancel(
            "🔥 MASS CLEANUP - FINAL CONFIRMATION",
            confirmation_text,
            icon='warning'
        )

        if result is True:
            self.log_message("✅ User confirmed mass cleanup - proceeding with backup", 'nvidia_green')
            self.execute_mass_cleanup_with_backup()
        elif result is False:
            self.log_message("❌ User cancelled mass cleanup", 'accent')
        else:
            self.log_message("❌ User cancelled mass cleanup", 'accent')

    def execute_mass_cleanup_with_backup(self):
        """Execute mass cleanup with automatic backup"""
        self.log_message("🔥 EXECUTING MASS CLEANUP WITH BACKUP", 'rog_red')

        def cleanup_thread():
            try:
                # Step 1: Create backup
                self.log_message("💾 Creating database backup before mass cleanup...", 'nvidia_green')
                backup_result = self.create_database_backup()

                if not backup_result:
                    self.log_message("❌ Backup failed! Mass cleanup cancelled for safety.", 'warning')
                    return

                self.log_message("✅ Database backup created successfully!", 'success')

                # Step 2: Execute mass cleanup
                self.log_message("🚀 Starting mass cleanup execution...", 'rog_red')

                processed = 0
                deleted_total = 0
                kept_total = 0

                for dup in self.current_duplicates:
                    processed += 1
                    tmdb_id = dup.get('tmdb_id', 0)
                    title = dup.get('sample_title', 'Unknown')

                    self.log_message(f"🎬 Processing {processed}/{len(self.current_duplicates)}: {title}", 'accent')

                    try:
                        # Use existing advanced cleanup function
                        result = self.db.apply_advanced_priority_cleanup(tmdb_id, auto_confirm=True)

                        deleted = result.get('deleted', 0)
                        kept = result.get('kept', 0)

                        deleted_total += deleted
                        kept_total += kept

                        if deleted > 0:
                            self.log_message(f"  ✅ {deleted} deleted, {kept} kept", 'success')
                        else:
                            self.log_message(f"  ⚠️ No action needed", 'accent')

                    except Exception as e:
                        self.log_message(f"  ❌ Error: {str(e)[:50]}", 'warning')

                # Final summary
                self.log_message("═" * 60, 'nvidia_green')
                self.log_message("🎉 MASS CLEANUP COMPLETED!", 'nvidia_green')
                self.log_message(f"📊 Movies processed: {processed}", 'accent')
                self.log_message(f"🗑️ Total deleted: {deleted_total}", 'rog_red')
                self.log_message(f"💾 Total kept: {kept_total}", 'success')
                self.log_message(f"💽 Estimated storage saved: {deleted_total * 1.5:.1f} GB", 'nvidia_green')
                self.log_message("✅ Database backup available for restore if needed", 'success')
                self.log_message("═" * 60, 'nvidia_green')

            except Exception as e:
                self.log_message(f"💥 CRITICAL ERROR in mass cleanup: {e}", 'warning')
                self.log_message("🔄 Please restore from backup if database is corrupted", 'warning')

        threading.Thread(target=cleanup_thread, daemon=True).start()

    def create_database_backup(self):
        """Create database backup before mass operations"""
        try:
            import datetime
            import subprocess

            # Generate backup filename with timestamp
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"xui_backup_before_mass_cleanup_{timestamp}.sql"

            # MySQL dump command
            host = self.connection_vars['Host'].get()
            user = self.connection_vars['User'].get()
            password = self.connection_vars['Pass'].get()
            database = self.connection_vars['DB'].get()
            port = self.connection_vars['Port'].get()

            cmd = [
                'mysqldump',
                f'--host={host}',
                f'--port={port}',
                f'--user={user}',
                f'--password={password}',
                '--single-transaction',
                '--routines',
                '--triggers',
                database
            ]

            self.log_message(f"💾 Creating backup: {backup_filename}", 'accent')

            with open(backup_filename, 'w') as backup_file:
                result = subprocess.run(cmd, stdout=backup_file, stderr=subprocess.PIPE, text=True)

            if result.returncode == 0:
                self.log_message(f"✅ Backup created: {backup_filename}", 'success')
                return True
            else:
                self.log_message(f"❌ Backup failed: {result.stderr}", 'warning')
                return False

        except Exception as e:
            self.log_message(f"💥 Backup error: {e}", 'warning')
            return False

    def refresh_data_gaming(self):
        """Refrescar datos gaming"""
        self.log_message("🔄 Refreshing data...", 'accent')
        self.current_duplicates = []
        self.selected_for_deletion = []
        self.show_data_welcome()
        self.log_message("✅ Data refreshed! Ready to load new data.", 'success')

    def show_statistics_gaming(self):
        """Mostrar estadísticas gaming"""
        self.log_message("📊 Generating performance statistics...", 'accent')
        self.log_message("🎮 Gaming-grade analysis in progress", 'nvidia_green')

        # Estadísticas simuladas
        stats = [
            "📈 Total movies processed: 1,337",
            "🎬 Duplicate groups found: 42",
            "💾 Storage saved: 2.5 TB",
            "⚡ Processing speed: 9000+ ops/sec",
            "🏆 Efficiency rating: LEGENDARY"
        ]

        for stat in stats:
            self.log_message(f"  {stat}", 'success')
            time.sleep(0.1)

    def setup_connection_frame(self, parent):
        """Configurar el frame de conexión con estilo ultra moderno"""
        conn_frame = ttk.LabelFrame(parent, text="🔗 Conexión a Base de Datos", padding="20", style="Modern.TLabelframe")
        conn_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # Campos de conexión
        ttk.Label(conn_frame, text="Host:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.host_var = tk.StringVar()
        ttk.Entry(conn_frame, textvariable=self.host_var, width=20).grid(row=0, column=1, padx=(0, 10))
        
        ttk.Label(conn_frame, text="Usuario:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.user_var = tk.StringVar()
        ttk.Entry(conn_frame, textvariable=self.user_var, width=15).grid(row=0, column=3, padx=(0, 10))
        
        ttk.Label(conn_frame, text="Contraseña:").grid(row=0, column=4, sticky=tk.W, padx=(0, 5))
        self.password_var = tk.StringVar()
        ttk.Entry(conn_frame, textvariable=self.password_var, show="*", width=15).grid(row=0, column=5, padx=(0, 10))
        
        ttk.Label(conn_frame, text="Base de Datos:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        self.database_var = tk.StringVar(value="xui")
        ttk.Entry(conn_frame, textvariable=self.database_var, width=20).grid(row=1, column=1, padx=(0, 10))
        
        ttk.Label(conn_frame, text="Puerto:").grid(row=1, column=2, sticky=tk.W, padx=(0, 5))
        self.port_var = tk.StringVar(value="3306")
        ttk.Entry(conn_frame, textvariable=self.port_var, width=10).grid(row=1, column=3, padx=(0, 10))
        
        # Botones de conexión con estilo ultra moderno
        self.connect_btn = ttk.Button(conn_frame, text="🔌 Conectar", command=self.connect_database, style="Accent.TButton")
        self.connect_btn.grid(row=1, column=4, padx=(20, 10))

        self.disconnect_btn = ttk.Button(conn_frame, text="🔌 Desconectar", command=self.disconnect_database, state="disabled", style="Secondary.TButton")
        self.disconnect_btn.grid(row=1, column=5, padx=(10, 0))
        
    def setup_status_frame(self, parent):
        """Configurar el frame de estado"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.status_var = tk.StringVar(value="Desconectado")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var, foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
    def setup_notebook(self, parent):
        """Configurar el notebook con pestañas ultra modernas"""
        self.notebook = ttk.Notebook(parent, style="Modern.TNotebook")
        self.notebook.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Pestaña de episodios huérfanos
        self.setup_orphaned_episodes_tab()
        
        # Pestaña de series sin episodios
        self.setup_series_without_episodes_tab()
        
        # Pestaña de gestión de series
        self.setup_series_management_tab()
        
        # Pestaña de películas duplicadas
        self.setup_duplicate_movies_tab()

        # Pestaña de gestión de películas
        self.setup_movies_management_tab()

        # Pestaña unificada de gestión de duplicados
        self.setup_unified_duplicates_tab()
        
    def setup_orphaned_episodes_tab(self):
        """Configurar la pestaña de episodios huérfanos"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Episodios Huérfanos")
        
        # Botón de actualizar
        ttk.Button(frame, text="Actualizar Lista", command=self.load_orphaned_episodes).pack(pady=10)
        
        # Treeview para mostrar episodios huérfanos
        columns = ("ID Episodio", "ID Stream", "ID Serie", "Tipo", "Nombre Tipo")
        self.orphaned_tree = ttk.Treeview(frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.orphaned_tree.heading(col, text=col)
            self.orphaned_tree.column(col, width=120)
        
        # Scrollbar para el treeview
        scrollbar_orphaned = ttk.Scrollbar(frame, orient="vertical", command=self.orphaned_tree.yview)
        self.orphaned_tree.configure(yscrollcommand=scrollbar_orphaned.set)
        
        self.orphaned_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        scrollbar_orphaned.pack(side="right", fill="y", padx=(0, 10), pady=10)
        
        # Frame para botones de acción
        action_frame = ttk.Frame(frame)
        action_frame.pack(fill="x", padx=10, pady=10)
        
        ttk.Button(action_frame, text="Asignar a Serie", command=self.assign_episode_to_series).pack(side="left", padx=(0, 10))
        ttk.Button(action_frame, text="Crear Nueva Serie", command=self.create_new_series_for_episode).pack(side="left", padx=(0, 10))
        ttk.Button(action_frame, text="Eliminar Episodio", command=self.delete_selected_episode).pack(side="left")
        
    def setup_series_without_episodes_tab(self):
        """Configurar la pestaña de series sin episodios"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Series Sin Episodios")
        
        # Botón de actualizar
        ttk.Button(frame, text="Actualizar Lista", command=self.load_series_without_episodes).pack(pady=10)
        
        # Treeview para mostrar series sin episodios
        columns = ("ID Serie", "Título")
        self.series_no_episodes_tree = ttk.Treeview(frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.series_no_episodes_tree.heading(col, text=col)
            self.series_no_episodes_tree.column(col, width=200)
        
        # Scrollbar
        scrollbar_series = ttk.Scrollbar(frame, orient="vertical", command=self.series_no_episodes_tree.yview)
        self.series_no_episodes_tree.configure(yscrollcommand=scrollbar_series.set)
        
        self.series_no_episodes_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        scrollbar_series.pack(side="right", fill="y", padx=(0, 10), pady=10)
        
        # Frame para botones de acción
        action_frame2 = ttk.Frame(frame)
        action_frame2.pack(fill="x", padx=10, pady=10)
        
        ttk.Button(action_frame2, text="Eliminar Serie", command=self.delete_selected_series).pack(side="left")
        
    def setup_series_management_tab(self):
        """Configurar la pestaña de gestión de series"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Gestión de Series")
        
        # Botón de actualizar
        ttk.Button(frame, text="Actualizar Lista", command=self.load_series_with_episodes).pack(pady=10)
        
        # Treeview para mostrar series con episodios
        columns = ("ID Serie", "Título", "Tipo", "Nombre Tipo", "Episodios")
        self.series_tree = ttk.Treeview(frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.series_tree.heading(col, text=col)
            self.series_tree.column(col, width=150)
        
        # Scrollbar
        scrollbar_management = ttk.Scrollbar(frame, orient="vertical", command=self.series_tree.yview)
        self.series_tree.configure(yscrollcommand=scrollbar_management.set)
        
        self.series_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        scrollbar_management.pack(side="right", fill="y", padx=(0, 10), pady=10)
        
    def setup_duplicate_movies_tab(self):
        """Configurar la pestaña de películas duplicadas"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Películas Duplicadas")
        
        # Botón de actualizar
        ttk.Button(frame, text="Actualizar Lista", command=self.load_duplicate_movies).pack(pady=10)
        
        # Treeview para mostrar películas duplicadas
        columns = ("Título", "Duplicados", "IDs")
        self.duplicate_movies_tree = ttk.Treeview(frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.duplicate_movies_tree.heading(col, text=col)
            if col == "Título":
                self.duplicate_movies_tree.column(col, width=300)
            elif col == "IDs":
                self.duplicate_movies_tree.column(col, width=200)
            else:
                self.duplicate_movies_tree.column(col, width=100)
        
        # Scrollbar
        scrollbar_duplicates = ttk.Scrollbar(frame, orient="vertical", command=self.duplicate_movies_tree.yview)
        self.duplicate_movies_tree.configure(yscrollcommand=scrollbar_duplicates.set)
        
        self.duplicate_movies_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        scrollbar_duplicates.pack(side="right", fill="y", padx=(0, 10), pady=10)

    def setup_movies_management_tab(self):
        """Configurar la pestaña de gestión de películas"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Gestión de Películas")

        # Frame superior para controles
        control_frame = ttk.Frame(frame)
        control_frame.pack(fill="x", padx=10, pady=10)

        # Botón de actualizar
        ttk.Button(control_frame, text="Actualizar Lista", command=self.load_all_movies).pack(side="left", padx=(0, 10))

        # Campo de búsqueda
        ttk.Label(control_frame, text="Buscar película:").pack(side="left", padx=(0, 5))
        self.movie_search_var = tk.StringVar()
        search_entry = ttk.Entry(control_frame, textvariable=self.movie_search_var, width=30)
        search_entry.pack(side="left", padx=(0, 10))
        ttk.Button(control_frame, text="Buscar", command=self.search_movies).pack(side="left")

        # Treeview para mostrar películas (con selección múltiple)
        columns = ("ID Stream", "Título", "Tipo ID", "Tipo")
        self.movies_tree = ttk.Treeview(frame, columns=columns, show="headings", height=15, selectmode="extended")

        for col in columns:
            self.movies_tree.heading(col, text=col)
            if col == "Título":
                self.movies_tree.column(col, width=350)
            elif col == "ID Stream":
                self.movies_tree.column(col, width=80)
            elif col == "Tipo ID":
                self.movies_tree.column(col, width=60)
            else:
                self.movies_tree.column(col, width=100)

        # Scrollbar
        scrollbar_movies = ttk.Scrollbar(frame, orient="vertical", command=self.movies_tree.yview)
        self.movies_tree.configure(yscrollcommand=scrollbar_movies.set)

        self.movies_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        scrollbar_movies.pack(side="right", fill="y", padx=(0, 10), pady=10)

        # Frame para botones de acción
        action_frame_movies = ttk.Frame(frame)
        action_frame_movies.pack(fill="x", padx=10, pady=10)

        ttk.Button(action_frame_movies, text="Ver Duplicados", command=self.show_movie_duplicates).pack(side="left", padx=(0, 10))
        ttk.Button(action_frame_movies, text="Películas Sin Título", command=self.load_movies_without_title).pack(side="left", padx=(0, 10))
        ttk.Button(action_frame_movies, text="Eliminar Seleccionadas", command=self.delete_selected_movies).pack(side="left", padx=(0, 10))
        ttk.Button(action_frame_movies, text="Seleccionar Todas", command=self.select_all_movies).pack(side="left")









    def setup_unified_duplicates_tab(self):
        """Configurar la pestaña unificada de gestión de duplicados con estilo ultra moderno"""
        frame = ttk.Frame(self.notebook, style="Modern.TFrame")
        self.notebook.add(frame, text="🎬 Gestión de Duplicados")

        # Frame superior para información y controles
        header_frame = ttk.LabelFrame(frame, text="🎬 Gestión Unificada de Duplicados", style="Modern.TLabelframe")
        header_frame.pack(fill="x", padx=20, pady=20)

        info_text = """
🎬 GESTIÓN COMPLETA DE DUPLICADOS
Visualiza todas las películas duplicadas con información TMDB, calidades, tipos de symlink y fuentes.
Selecciona qué copias mantener y cuáles eliminar con control granular sobre cada película.
        """
        ttk.Label(header_frame, text=info_text, font=('Arial', 9)).pack(padx=10, pady=5)

        # Frame de controles principales
        controls_frame = ttk.Frame(frame)
        controls_frame.pack(fill="x", padx=10, pady=10)

        # Botones de carga con estilo ultra moderno
        load_frame = ttk.LabelFrame(controls_frame, text="📂 Cargar Datos", style="Modern.TLabelframe")
        load_frame.pack(side="left", fill="y", padx=(0, 20))

        ttk.Button(load_frame, text="🎬 Cargar Duplicados TMDB", command=self.load_unified_tmdb_duplicates, style="Accent.TButton").pack(fill="x", padx=10, pady=4)
        ttk.Button(load_frame, text="🔄 Actualizar Lista", command=self.refresh_unified_duplicates, style="Modern.TButton").pack(fill="x", padx=10, pady=4)
        ttk.Button(load_frame, text="🎯 Solo con Symlinks", command=self.load_symlink_duplicates, style="Modern.TButton").pack(fill="x", padx=10, pady=4)

        # Botones de selección con estilo ultra moderno
        selection_frame = ttk.LabelFrame(controls_frame, text="☑ Selección", style="Modern.TLabelframe")
        selection_frame.pack(side="left", fill="y", padx=(0, 20))

        ttk.Button(selection_frame, text="☑ Seleccionar Todos", command=self.select_all_unified_duplicates, style="Modern.TButton").pack(fill="x", padx=10, pady=4)
        ttk.Button(selection_frame, text="☐ Deseleccionar Todos", command=self.deselect_all_unified_duplicates, style="Secondary.TButton").pack(fill="x", padx=10, pady=4)
        ttk.Button(selection_frame, text="🎯 Selección Inteligente", command=self.smart_select_duplicates, style="Success.TButton").pack(fill="x", padx=10, pady=4)

        # Botones de acción con estilo ultra moderno
        action_frame = ttk.LabelFrame(controls_frame, text="⚙️ Acciones", style="Modern.TLabelframe")
        action_frame.pack(side="left", fill="y", padx=(0, 20))

        ttk.Button(action_frame, text="🔍 Ver Detalles", command=self.show_unified_details, style="Modern.TButton").pack(fill="x", padx=10, pady=4)
        ttk.Button(action_frame, text="⚙️ Selección Manual", command=self.manual_selection, style="Modern.TButton").pack(fill="x", padx=10, pady=4)
        ttk.Button(action_frame, text="🚀 Ejecutar Limpieza", command=self.execute_unified_cleanup, style="Accent.TButton").pack(fill="x", padx=10, pady=4)
        ttk.Button(action_frame, text="⭐ Limpieza Avanzada", command=self.execute_advanced_cleanup, style="Accent.TButton").pack(fill="x", padx=10, pady=4)

        # Botones de utilidades con estilo ultra moderno
        utils_frame = ttk.LabelFrame(controls_frame, text="🛠️ Utilidades", style="Modern.TLabelframe")
        utils_frame.pack(side="left", fill="y")

        ttk.Button(utils_frame, text="📊 Vista Previa", command=self.preview_unified_cleanup, style="Modern.TButton").pack(fill="x", padx=10, pady=4)
        ttk.Button(utils_frame, text="📄 Exportar Reporte", command=self.export_unified_report, style="Modern.TButton").pack(fill="x", padx=10, pady=4)
        ttk.Button(utils_frame, text="🔄 Actualizar TMDB", command=self.update_from_tmdb, style="Modern.TButton").pack(fill="x", padx=10, pady=4)

        # Treeview principal para mostrar duplicados unificados
        tree_frame = ttk.Frame(frame)
        tree_frame.pack(fill="both", expand=True, padx=10, pady=10)

        columns = ("Sel", "TMDB ID", "Título", "Total", "4K", "60FPS", "FHD", "HD", "SD", "Symlinks", "Direct", "Otros", "Recomendación")
        self.unified_duplicates_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=16, selectmode="extended", style="Modern.Treeview")

        # Configurar columnas con nueva estructura
        column_widths = {
            "Sel": 40, "TMDB ID": 80, "Título": 220, "Total": 50,
            "4K": 35, "60FPS": 45, "FHD": 35, "HD": 35, "SD": 35,
            "Symlinks": 65, "Direct": 55, "Otros": 50, "Recomendación": 180
        }

        for col in columns:
            self.unified_duplicates_tree.heading(col, text=col)
            self.unified_duplicates_tree.column(col, width=column_widths.get(col, 80))

        # Scrollbars con estilo moderno
        v_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.unified_duplicates_tree.yview, style="Dark.Vertical.TScrollbar")
        h_scrollbar = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.unified_duplicates_tree.xview, style="Dark.Horizontal.TScrollbar")
        self.unified_duplicates_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack scrollbars primero
        v_scrollbar.pack(side="right", fill="y")
        h_scrollbar.pack(side="bottom", fill="x")
        # Luego el treeview
        self.unified_duplicates_tree.pack(side="left", fill="both", expand=True)

        # Frame inferior para estadísticas y filtros
        bottom_frame = ttk.Frame(frame)
        bottom_frame.pack(fill="x", padx=10, pady=10)

        # Panel de estadísticas con estilo ultra moderno
        stats_frame = ttk.LabelFrame(bottom_frame, text="📊 Estadísticas", style="Modern.TLabelframe")
        stats_frame.pack(side="left", fill="both", expand=True, padx=(0, 20))

        self.unified_stats_text = tk.Text(stats_frame, height=6, width=50,
                                        bg=self.current_theme['bg'],
                                        fg=self.current_theme['fg'],
                                        font=self.font_code,
                                        insertbackground=self.current_theme['fg'],
                                        selectbackground=self.current_theme['select_bg'],
                                        selectforeground=self.current_theme['select_fg'],
                                        borderwidth=1,
                                        relief='solid')
        self.unified_stats_text.pack(padx=8, pady=8)

        # Panel de filtros con estilo ultra moderno
        filters_frame = ttk.LabelFrame(bottom_frame, text="🔍 Filtros", style="Modern.TLabelframe")
        filters_frame.pack(side="right", fill="y")

        # Filtro por calidad
        ttk.Label(filters_frame, text="Calidad:", font=self.font_main, style="Modern.TLabel").pack(padx=12, pady=5)
        self.quality_filter_var = tk.StringVar(value="Todas")
        quality_combo = ttk.Combobox(filters_frame, textvariable=self.quality_filter_var,
                                   values=["Todas", "4K", "60FPS", "FHD", "HD", "SD"], state="readonly", width=14,
                                   style="Modern.TCombobox")
        quality_combo.pack(padx=12, pady=5)

        # Filtro por tipo
        ttk.Label(filters_frame, text="Tipo:", font=self.font_main, style="Modern.TLabel").pack(padx=12, pady=5)
        self.type_filter_var = tk.StringVar(value="Todos")
        type_combo = ttk.Combobox(filters_frame, textvariable=self.type_filter_var,
                                values=["Todos", "Solo Symlinks", "Solo Direct", "Mixtos"], state="readonly", width=14,
                                style="Modern.TCombobox")
        type_combo.pack(padx=12, pady=5)

        # Botón aplicar filtros
        ttk.Button(filters_frame, text="🔍 Aplicar Filtros", command=self.apply_unified_filters, style="Accent.TButton").pack(padx=12, pady=12)

        # Bind para toggle de selección
        self.unified_duplicates_tree.bind("<Double-1>", self.toggle_unified_selection)

        # Variables ya inicializadas en __init__

    def load_unified_tmdb_duplicates(self):
        """Cargar todos los duplicados TMDB con información detallada"""
        if not self.check_connection():
            return

        # Verificar que los widgets existen
        if not hasattr(self, 'unified_duplicates_tree'):
            messagebox.showerror("Error", "La interfaz unificada no está inicializada correctamente")
            return

        # Limpiar datos anteriores
        self.unified_duplicates_data = {}
        try:
            for item in self.unified_duplicates_tree.get_children():
                self.unified_duplicates_tree.delete(item)
        except Exception as e:
            print(f"Error limpiando tree: {e}")
            return

        try:
            # Obtener duplicados de películas por TMDB
            movie_duplicates = self.db.get_duplicate_movies_by_tmdb()

            for movie_group in movie_duplicates:
                tmdb_id = movie_group['tmdb_id']

                # Obtener detalles completos de cada copia
                copies_details = self.db.get_movie_copies_by_tmdb(tmdb_id)

                # Analizar calidades y tipos con prioridades avanzadas
                quality_counts = {"4K": 0, "60FPS": 0, "FHD": 0, "HD": 0, "EXTENDED": 0, "STANDARD": 0, "SD": 0}
                type_counts = {"symlinks": 0, "direct": 0, "others": 0}

                for copy in copies_details:
                    # Contar calidades con nueva lógica de prioridades
                    quality = copy.get('detected_quality', 'STANDARD')
                    if quality in quality_counts:
                        quality_counts[quality] += 1
                    else:
                        quality_counts['STANDARD'] += 1

                    # Contar tipos
                    if copy.get('movie_symlink') == 1:
                        type_counts['symlinks'] += 1
                    elif copy.get('direct_source') == 1 or copy.get('direct_proxy') == 1:
                        type_counts['direct'] += 1
                    else:
                        type_counts['others'] += 1

                # Generar recomendación
                recommendation = self.generate_unified_recommendation(quality_counts, type_counts, len(copies_details))

                # Insertar en el treeview con nueva estructura
                item_id = self.unified_duplicates_tree.insert("", "end", values=(
                    "☐",  # Selección
                    tmdb_id,
                    movie_group['sample_title'],
                    len(copies_details),
                    quality_counts['4K'],
                    quality_counts['60FPS'],
                    quality_counts['FHD'],
                    quality_counts['HD'],
                    quality_counts['SD'],
                    type_counts['symlinks'],
                    type_counts['direct'],
                    type_counts['others'],
                    recommendation
                ))

                # Guardar datos
                self.unified_duplicates_data[tmdb_id] = {
                    'item_id': item_id,
                    'selected': False,
                    'movie_data': movie_group,
                    'copies': copies_details,
                    'quality_counts': quality_counts,
                    'type_counts': type_counts,
                    'recommendation': recommendation
                }

            self.update_unified_stats()
            messagebox.showinfo("Éxito", f"Se cargaron {len(movie_duplicates)} grupos de duplicados")

        except Exception as e:
            messagebox.showerror("Error", f"Error cargando duplicados: {str(e)}")

    def generate_unified_recommendation(self, quality_counts, type_counts, total_copies, copies_details=None):
        """Generar recomendación inteligente basada en calidades y tipos con prioridades avanzadas"""

        # NUEVA LÓGICA DE PRIORIDADES:
        # 1. 4K symlinks (máxima prioridad)
        # 2. 60FPS symlinks
        # 3. FHD/HD symlinks
        # 4. Otros symlinks
        # 5. Solo si NO hay symlinks: Direct más nuevo

        if type_counts['symlinks'] > 0:
            # Hay symlinks - aplicar prioridades de calidad
            if quality_counts['4K'] > 0:
                return "🥇 Mantener 4K symlinks"
            elif quality_counts.get('60FPS', 0) > 0:
                return "🥈 Mantener 60FPS symlinks"
            elif quality_counts['FHD'] > 0 or quality_counts['HD'] > 0:
                fhd_hd_count = quality_counts['FHD'] + quality_counts['HD']
                return f"🥉 Mantener FHD/HD symlinks ({fhd_hd_count})"
            else:
                return "🔗 Mantener mejores symlinks"
        elif type_counts['direct'] > 0:
            # Solo direct sources - mantener el más nuevo
            return "📡 Mantener direct más nuevo"
        else:
            return "⚙️ Selección manual requerida"

    def toggle_unified_selection(self, event):
        """Toggle selección de película en la vista unificada"""
        item = self.unified_duplicates_tree.selection()[0] if self.unified_duplicates_tree.selection() else None
        if not item:
            return

        # Obtener TMDB ID
        values = self.unified_duplicates_tree.item(item)['values']
        tmdb_id = values[1]

        if tmdb_id in self.unified_duplicates_data:
            # Toggle selección
            current_selected = self.unified_duplicates_data[tmdb_id]['selected']
            self.unified_duplicates_data[tmdb_id]['selected'] = not current_selected

            # Actualizar visual
            new_values = list(values)
            new_values[0] = "☑" if not current_selected else "☐"
            self.unified_duplicates_tree.item(item, values=new_values)

            # Actualizar contador
            if not current_selected:
                self.unified_selection_count += 1
            else:
                self.unified_selection_count -= 1

            self.update_unified_stats()

    def select_all_unified_duplicates(self):
        """Seleccionar todos los duplicados"""
        for tmdb_id, data in self.unified_duplicates_data.items():
            if not data['selected']:
                data['selected'] = True
                # Actualizar visual
                values = list(self.unified_duplicates_tree.item(data['item_id'])['values'])
                values[0] = "☑"
                self.unified_duplicates_tree.item(data['item_id'], values=values)

        self.unified_selection_count = len(self.unified_duplicates_data)
        self.update_unified_stats()

    def deselect_all_unified_duplicates(self):
        """Deseleccionar todos los duplicados"""
        for tmdb_id, data in self.unified_duplicates_data.items():
            if data['selected']:
                data['selected'] = False
                # Actualizar visual
                values = list(self.unified_duplicates_tree.item(data['item_id'])['values'])
                values[0] = "☐"
                self.unified_duplicates_tree.item(data['item_id'], values=values)

        self.unified_selection_count = 0
        self.update_unified_stats()

    def update_unified_stats(self):
        """Actualizar estadísticas de la vista unificada"""
        total_groups = len(self.unified_duplicates_data)
        selected_groups = self.unified_selection_count

        total_copies = sum(len(data['copies']) for data in self.unified_duplicates_data.values())
        selected_copies = sum(len(data['copies']) for data in self.unified_duplicates_data.values() if data['selected'])

        # Calcular estadísticas de calidad con nuevas prioridades
        quality_stats = {"4K": 0, "60FPS": 0, "FHD": 0, "HD": 0, "EXTENDED": 0, "STANDARD": 0, "SD": 0}
        type_stats = {"symlinks": 0, "direct": 0, "others": 0}

        for data in self.unified_duplicates_data.values():
            for quality, count in data['quality_counts'].items():
                if quality in quality_stats:
                    quality_stats[quality] += count
            for type_name, count in data['type_counts'].items():
                type_stats[type_name] += count

        stats_content = f"""📊 ESTADÍSTICAS UNIFICADAS CON PRIORIDADES:

Grupos de duplicados: {total_groups}
Grupos seleccionados: {selected_groups}
Total de copias: {total_copies}
Copias en selección: {selected_copies}

📺 CALIDADES (por prioridad):
🥇 4K: {quality_stats['4K']} | 🥈 60FPS: {quality_stats['60FPS']} | 🥉 FHD: {quality_stats['FHD']} | HD: {quality_stats['HD']} | SD: {quality_stats['SD']}

🔗 TIPOS:
Symlinks: {type_stats['symlinks']} | Direct: {type_stats['direct']} | Otros: {type_stats['others']}

💾 Reducción estimada: {((selected_copies - selected_groups) / max(selected_copies, 1) * 100):.1f}%"""

        self.unified_stats_text.delete(1.0, tk.END)
        self.unified_stats_text.insert(1.0, stats_content)

    def smart_select_duplicates(self):
        """Selección inteligente basada en nuevas prioridades avanzadas"""
        selected_count = 0

        for tmdb_id, data in self.unified_duplicates_data.items():
            # NUEVA LÓGICA DE SELECCIÓN INTELIGENTE:
            # 1. Priorizar 4K symlinks
            # 2. Luego 60FPS symlinks
            # 3. Luego FHD/HD symlinks
            # 4. Solo si NO hay symlinks: Direct más nuevo

            should_select = False
            quality_counts = data['quality_counts']
            type_counts = data['type_counts']

            if type_counts['symlinks'] > 0:
                # Hay symlinks - aplicar prioridades de calidad
                if quality_counts['4K'] > 0:
                    should_select = True  # 🥇 Máxima prioridad: 4K symlinks
                elif quality_counts.get('60FPS', 0) > 0:
                    should_select = True  # 🥈 Segunda prioridad: 60FPS symlinks
                elif quality_counts['FHD'] > 0 or quality_counts['HD'] > 0:
                    should_select = True  # 🥉 Tercera prioridad: FHD/HD symlinks
                elif type_counts['symlinks'] == 1:
                    should_select = True  # Auto-seleccionar si es el único symlink
            elif type_counts['direct'] > 0:
                # Solo direct sources - seleccionar solo si no hay symlinks
                should_select = True  # Mantener direct más nuevo

            if should_select and not data['selected']:
                data['selected'] = True
                selected_count += 1

                # Actualizar visual
                values = list(self.unified_duplicates_tree.item(data['item_id'])['values'])
                values[0] = "☑"
                self.unified_duplicates_tree.item(data['item_id'], values=values)

        self.unified_selection_count = sum(1 for data in self.unified_duplicates_data.values() if data['selected'])
        self.update_unified_stats()

        messagebox.showinfo("Selección Inteligente Avanzada",
                          f"Se seleccionaron {selected_count} grupos con prioridades:\n"
                          f"🥇 4K symlinks > 🥈 60FPS symlinks > 🥉 FHD/HD symlinks > 📡 Direct más nuevo")

    def execute_advanced_cleanup(self):
        """Ejecutar limpieza avanzada con nuevas prioridades en películas seleccionadas"""
        if not self.unified_duplicates_data:
            messagebox.showwarning("Advertencia", "No hay datos cargados")
            return

        # Obtener películas seleccionadas
        selected_movies = [data for data in self.unified_duplicates_data.values() if data['selected']]

        if not selected_movies:
            messagebox.showwarning("Advertencia", "Selecciona al menos una película para aplicar limpieza avanzada")
            return

        # Mostrar confirmación con nueva lógica
        total_copies = sum(len(movie['copies']) for movie in selected_movies)

        message = f"⭐ LIMPIEZA AVANZADA CON PRIORIDADES\n\n"
        message += f"Películas seleccionadas: {len(selected_movies)}\n"
        message += f"Total de copias: {total_copies}\n\n"
        message += "🎯 NUEVA LÓGICA DE PRIORIDADES:\n"
        message += "🥇 Mantener 4K symlinks\n"
        message += "🥈 Mantener 60FPS symlinks\n"
        message += "🥉 Mantener FHD/HD symlinks\n"
        message += "📡 Para direct sources: solo el ID más nuevo\n\n"
        message += "¿Aplicar limpieza avanzada a las películas seleccionadas?"

        if not messagebox.askyesno("Confirmar Limpieza Avanzada", message):
            return

        # Crear ventana de progreso
        progress_window = tk.Toplevel(self.root)
        progress_window.title("Limpieza Avanzada en Progreso")
        progress_window.geometry("600x400")
        progress_window.transient(self.root)
        progress_window.grab_set()

        # Widgets de progreso
        ttk.Label(progress_window, text="⭐ Ejecutando Limpieza Avanzada...", font=('Arial', 12, 'bold')).pack(pady=10)

        progress_bar = ttk.Progressbar(progress_window, length=500, mode='determinate')
        progress_bar.pack(padx=20, pady=10)

        status_label = ttk.Label(progress_window, text="Iniciando...", font=('Arial', 10))
        status_label.pack(pady=5)

        results_text = tk.Text(progress_window, height=15, width=70)
        results_text.pack(padx=20, pady=10, fill="both", expand=True)

        # Variables de progreso
        total_deleted = 0
        total_kept = 0
        total_errors = 0

        def process_advanced_cleanup():
            nonlocal total_deleted, total_kept, total_errors  # Permitir acceso a variables externas
            try:
                for i, movie_data in enumerate(selected_movies, 1):
                    try:
                        # Obtener TMDB ID de forma más segura
                        tmdb_id = None
                        for tid, data in self.unified_duplicates_data.items():
                            if data == movie_data:
                                tmdb_id = tid
                                break

                        if tmdb_id is None:
                            total_errors += 1
                            results_text.insert(tk.END, f"❌ Error: No se pudo obtener TMDB ID para película {i}\n\n")
                            continue

                        title = movie_data['movie_data']['sample_title']

                        # Actualizar progreso
                        progress_bar['value'] = (i / len(selected_movies)) * 100
                        status_label.config(text=f"Procesando: {title} ({i}/{len(selected_movies)})")
                        progress_window.update()

                        # Aplicar limpieza avanzada
                        result = self.db.apply_advanced_priority_cleanup(tmdb_id, auto_confirm=True)

                        # Actualizar contadores
                        total_deleted += result.get('deleted', 0)
                        total_kept += result.get('kept', 0)

                        if result.get('deleted', 0) == 0 and result.get('message', '').startswith('Error'):
                            total_errors += 1

                        # Mostrar resultado
                        result_line = f"🎬 {title}\n"
                        result_line += f"   {result.get('logic_applied', 'Lógica aplicada')}\n"
                        result_line += f"   Eliminados: {result.get('deleted', 0)} | Mantenidos: {result.get('kept', 0)}\n"

                        if result.get('message', '').startswith('Error'):
                            result_line += f"   ⚠️ {result.get('message', '')}\n"

                        result_line += "\n"

                        results_text.insert(tk.END, result_line)
                        results_text.see(tk.END)
                        progress_window.update()

                    except Exception as e:
                        total_errors += 1
                        error_line = f"❌ Error procesando película {i}: {str(e)}\n\n"
                        results_text.insert(tk.END, error_line)
                        results_text.see(tk.END)
                        progress_window.update()
                        continue

                # Completado
                progress_bar['value'] = 100
                status_label.config(text="✅ Limpieza avanzada completada!", foreground="green")

                # Resumen final
                summary = f"\n{'='*50}\n"
                summary += f"⭐ LIMPIEZA AVANZADA COMPLETADA\n"
                summary += f"{'='*50}\n"
                summary += f"Películas procesadas: {len(selected_movies)}\n"
                summary += f"Total eliminados: {total_deleted}\n"
                summary += f"Total mantenidos: {total_kept}\n"
                summary += f"Errores: {total_errors}\n"

                results_text.insert(tk.END, summary)
                results_text.see(tk.END)

                # Botón para cerrar
                ttk.Button(progress_window, text="✅ Cerrar y Actualizar",
                          command=lambda: self.finish_advanced_cleanup(progress_window, total_deleted, total_kept)).pack(pady=10)

            except Exception as e:
                # Error general - mostrar en la ventana de progreso
                error_message = f"\n❌ ERROR GENERAL: {str(e)}\n"
                results_text.insert(tk.END, error_message)
                results_text.see(tk.END)

                status_label.config(text="❌ Error durante el procesamiento", foreground="red")

                # Botón para cerrar con error
                ttk.Button(progress_window, text="❌ Cerrar (Con Errores)",
                          command=lambda: self.finish_advanced_cleanup_with_error(progress_window, total_deleted, total_kept, total_errors, str(e))).pack(pady=10)

        # Iniciar procesamiento en hilo separado
        import threading
        threading.Thread(target=process_advanced_cleanup, daemon=True).start()

    def finish_advanced_cleanup(self, dialog, deleted, kept):
        """Finalizar limpieza avanzada"""
        dialog.destroy()

        # Mostrar resultados finales
        result_message = f"⭐ LIMPIEZA AVANZADA COMPLETADA\n\n"
        result_message += f"Copias eliminadas: {deleted}\n"
        result_message += f"Copias mantenidas: {kept}\n\n"
        result_message += f"🎯 Lógica aplicada:\n"
        result_message += f"🥇 4K symlinks > 🥈 60FPS symlinks > 🥉 FHD/HD symlinks > 📡 Direct más nuevo\n\n"
        result_message += f"¡Base de datos optimizada con prioridades avanzadas!"

        messagebox.showinfo("Limpieza Avanzada Completada", result_message)

        # Refrescar vista
        self.load_unified_tmdb_duplicates()

    def finish_advanced_cleanup_with_error(self, dialog, deleted, kept, errors, error_message):
        """Finalizar limpieza avanzada con errores"""
        dialog.destroy()

        # Mostrar resultados con errores
        result_message = f"⚠️ LIMPIEZA AVANZADA COMPLETADA CON ERRORES\n\n"
        result_message += f"Copias eliminadas: {deleted}\n"
        result_message += f"Copias mantenidas: {kept}\n"
        result_message += f"Errores encontrados: {errors}\n\n"
        result_message += f"❌ Error principal: {error_message}\n\n"
        result_message += f"🎯 Lógica aplicada (parcialmente):\n"
        result_message += f"🥇 4K symlinks > 🥈 60FPS symlinks > 🥉 FHD/HD symlinks > 📡 Direct más nuevo\n\n"
        result_message += f"⚠️ Algunas películas pueden no haberse procesado correctamente.\n"
        result_message += f"Revisa los logs para más detalles."

        messagebox.showwarning("Limpieza Avanzada con Errores", result_message)

        # Refrescar vista para mostrar cambios realizados
        self.load_unified_tmdb_duplicates()

    def show_unified_details(self):
        """Mostrar detalles de la película seleccionada"""
        selected = self.unified_duplicates_tree.selection()
        if not selected:
            messagebox.showwarning("Advertencia", "Selecciona una película para ver detalles")
            return

        values = self.unified_duplicates_tree.item(selected[0])['values']
        tmdb_id = values[1]

        if tmdb_id not in self.unified_duplicates_data:
            return

        data = self.unified_duplicates_data[tmdb_id]
        copies = data['copies']

        # Crear ventana de detalles
        details_window = tk.Toplevel(self.root)
        details_window.title(f"Detalles: {data['movie_data']['sample_title']}")
        details_window.geometry("800x600")
        details_window.transient(self.root)
        details_window.grab_set()

        # Información general
        info_frame = ttk.LabelFrame(details_window, text="Información General")
        info_frame.pack(fill="x", padx=10, pady=10)

        info_text = f"""
🎬 Título: {data['movie_data']['sample_title']}
🆔 TMDB ID: {tmdb_id}
📊 Total de copias: {len(copies)}
💡 Recomendación: {data['recommendation']}
        """
        ttk.Label(info_frame, text=info_text, font=('Arial', 10)).pack(padx=10, pady=5)

        # Lista de copias
        copies_frame = ttk.LabelFrame(details_window, text="Todas las Copias")
        copies_frame.pack(fill="both", expand=True, padx=10, pady=10)

        columns = ("ID", "Título", "Calidad", "Symlink", "Direct Source", "Direct Proxy")
        copies_tree = ttk.Treeview(copies_frame, columns=columns, show="headings", height=15)

        for col in columns:
            copies_tree.heading(col, text=col)
            if col == "Título":
                copies_tree.column(col, width=300)
            else:
                copies_tree.column(col, width=100)

        for copy in copies:
            copies_tree.insert("", "end", values=(
                copy['stream_id'],
                copy['title'][:50] + "..." if len(copy['title']) > 50 else copy['title'],
                copy.get('detected_quality', 'SD'),
                "Sí" if copy.get('movie_symlink') == 1 else "No",
                "Sí" if copy.get('direct_source') == 1 else "No",
                "Sí" if copy.get('direct_proxy') == 1 else "No"
            ))

        copies_tree.pack(fill="both", expand=True, padx=5, pady=5)

        # Botones
        button_frame = ttk.Frame(details_window)
        button_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(button_frame, text="⚙️ Selección Manual",
                  command=lambda: self.open_manual_selection_for_movie(tmdb_id, details_window)).pack(side="left", padx=(0, 10))
        ttk.Button(button_frame, text="✅ Cerrar", command=details_window.destroy).pack(side="right")

    def open_manual_selection_for_movie(self, tmdb_id, parent_window):
        """Abrir selección manual para una película específica"""
        parent_window.destroy()

        # Simular selección en el árbol de películas manuales
        # Esto abrirá el workspace unificado con la película específica
        self.manual_selection()

        # TODO: Aquí se podría agregar lógica para pre-seleccionar la película específica

    def refresh_unified_duplicates(self):
        """Refrescar la lista de duplicados"""
        self.load_unified_tmdb_duplicates()

    def load_symlink_duplicates(self):
        """Cargar solo duplicados que tienen symlinks"""
        if not self.check_connection():
            return

        # Verificar que los widgets existen
        if not hasattr(self, 'unified_duplicates_tree'):
            messagebox.showerror("Error", "La interfaz unificada no está inicializada correctamente")
            return

        # Limpiar datos anteriores
        self.unified_duplicates_data = {}
        try:
            for item in self.unified_duplicates_tree.get_children():
                self.unified_duplicates_tree.delete(item)
        except Exception as e:
            print(f"Error limpiando tree: {e}")
            return

        try:
            # Obtener duplicados que tienen symlinks
            movie_duplicates = self.db.get_duplicate_movies_by_tmdb()
            symlink_groups = 0

            for movie_group in movie_duplicates:
                tmdb_id = movie_group['tmdb_id']

                # Obtener detalles y verificar si tiene symlinks
                copies_details = self.db.get_movie_copies_by_tmdb(tmdb_id)
                has_symlinks = any(copy.get('movie_symlink') == 1 for copy in copies_details)

                if not has_symlinks:
                    continue

                symlink_groups += 1

                # Procesar con nueva lógica de prioridades
                quality_counts = {"4K": 0, "60FPS": 0, "FHD": 0, "HD": 0, "EXTENDED": 0, "STANDARD": 0, "SD": 0}
                type_counts = {"symlinks": 0, "direct": 0, "others": 0}

                for copy in copies_details:
                    quality = copy.get('detected_quality', 'STANDARD')
                    if quality in quality_counts:
                        quality_counts[quality] += 1
                    else:
                        quality_counts['STANDARD'] += 1

                    if copy.get('movie_symlink') == 1:
                        type_counts['symlinks'] += 1
                    elif copy.get('direct_source') == 1 or copy.get('direct_proxy') == 1:
                        type_counts['direct'] += 1
                    else:
                        type_counts['others'] += 1

                recommendation = self.generate_unified_recommendation(quality_counts, type_counts, len(copies_details))

                item_id = self.unified_duplicates_tree.insert("", "end", values=(
                    "☐", tmdb_id, movie_group['sample_title'], len(copies_details),
                    quality_counts['4K'], quality_counts['60FPS'], quality_counts['FHD'], quality_counts['HD'], quality_counts['SD'],
                    type_counts['symlinks'], type_counts['direct'], type_counts['others'], recommendation
                ))

                self.unified_duplicates_data[tmdb_id] = {
                    'item_id': item_id, 'selected': False, 'movie_data': movie_group,
                    'copies': copies_details, 'quality_counts': quality_counts,
                    'type_counts': type_counts, 'recommendation': recommendation
                }

            self.update_unified_stats()
            messagebox.showinfo("Éxito", f"Se cargaron {symlink_groups} grupos con symlinks")

        except Exception as e:
            messagebox.showerror("Error", f"Error cargando duplicados con symlinks: {str(e)}")

    def apply_unified_filters(self):
        """Aplicar filtros a la vista unificada"""
        quality_filter = self.quality_filter_var.get()
        type_filter = self.type_filter_var.get()

        # Ocultar/mostrar elementos según filtros
        for tmdb_id, data in self.unified_duplicates_data.items():
            item_id = data['item_id']
            show_item = True

            # Filtro por calidad
            if quality_filter != "Todas":
                if data['quality_counts'].get(quality_filter, 0) == 0:
                    show_item = False

            # Filtro por tipo
            if type_filter != "Todos":
                if type_filter == "Solo Symlinks" and data['type_counts']['symlinks'] == 0:
                    show_item = False
                elif type_filter == "Solo Direct" and data['type_counts']['direct'] == 0:
                    show_item = False
                elif type_filter == "Mixtos" and (data['type_counts']['symlinks'] == 0 or data['type_counts']['direct'] == 0):
                    show_item = False

            # Mostrar/ocultar elemento (simulado moviendo al final)
            if not show_item:
                # Ocultar elemento (mover fuera de vista)
                self.unified_duplicates_tree.move(item_id, "", "end")
                self.unified_duplicates_tree.set(item_id, "Título", f"[FILTRADO] {data['movie_data']['sample_title']}")
            else:
                # Mostrar elemento
                self.unified_duplicates_tree.set(item_id, "Título", data['movie_data']['sample_title'])

    def execute_unified_cleanup(self):
        """Ejecutar limpieza de los duplicados seleccionados"""
        selected_groups = [data for data in self.unified_duplicates_data.values() if data['selected']]

        if not selected_groups:
            messagebox.showwarning("Advertencia", "No hay grupos seleccionados para limpiar")
            return

        # Confirmación
        total_copies = sum(len(data['copies']) for data in selected_groups)
        estimated_deletions = total_copies - len(selected_groups)  # Mantener 1 por grupo

        message = f"🚀 EJECUTAR LIMPIEZA UNIFICADA\n\n"
        message += f"Grupos seleccionados: {len(selected_groups)}\n"
        message += f"Total de copias: {total_copies}\n"
        message += f"Copias a eliminar: {estimated_deletions}\n"
        message += f"Copias a mantener: {len(selected_groups)}\n\n"
        message += "Se mantendrá la mejor copia de cada grupo según las recomendaciones.\n"
        message += "¿Continuar con la limpieza?"

        if not messagebox.askyesno("Confirmar Limpieza Unificada", message):
            return

        # Ejecutar limpieza
        self.perform_unified_cleanup(selected_groups)

    def perform_unified_cleanup(self, selected_groups):
        """Realizar la limpieza unificada"""
        # Crear ventana de progreso
        progress_window = tk.Toplevel(self.root)
        progress_window.title("Ejecutando Limpieza Unificada")
        progress_window.geometry("500x300")
        progress_window.transient(self.root)
        progress_window.grab_set()

        ttk.Label(progress_window, text="🚀 Ejecutando Limpieza Unificada...", font=('Arial', 12, 'bold')).pack(pady=10)

        progress_bar = ttk.Progressbar(progress_window, length=400, mode='determinate')
        progress_bar.pack(padx=20, pady=10)

        status_label = ttk.Label(progress_window, text="Iniciando...", font=('Arial', 10))
        status_label.pack(pady=5)

        results_text = tk.Text(progress_window, height=8, width=60)
        results_text.pack(padx=20, pady=10)

        # Variables de resultado
        total_deleted = 0
        total_kept = 0
        total_errors = 0

        def update_progress(current, total, movie_title, deleted, kept):
            nonlocal total_deleted, total_kept
            progress_bar['value'] = (current / total) * 100
            status_label.config(text=f"Procesando: {movie_title} ({current}/{total})")

            total_deleted += deleted
            total_kept += kept

            results_content = f"📊 PROGRESO DE LIMPIEZA:\n"
            results_content += f"Grupos procesados: {current}/{total}\n"
            results_content += f"Copias eliminadas: {total_deleted}\n"
            results_content += f"Copias mantenidas: {total_kept}\n"
            results_content += f"Errores: {total_errors}\n"

            results_text.delete(1.0, tk.END)
            results_text.insert(1.0, results_content)
            progress_window.update()

        def cleanup_thread():
            nonlocal total_errors
            try:
                for i, group_data in enumerate(selected_groups, 1):
                    tmdb_id = list(self.unified_duplicates_data.keys())[list(self.unified_duplicates_data.values()).index(group_data)]
                    movie_title = group_data['movie_data']['sample_title']

                    # Aplicar limpieza inteligente para este grupo
                    result = self.db.apply_smart_cleanup(tmdb_id, auto_confirm=True)

                    if result and 'deleted' in result:
                        deleted = result['deleted']
                        kept = result.get('kept', 1)
                    else:
                        deleted = 0
                        kept = 1
                        total_errors += 1

                    # Actualizar progreso en el hilo principal
                    self.root.after(0, lambda c=i, t=len(selected_groups), title=movie_title, d=deleted, k=kept:
                                   update_progress(c, t, title, d, k))

                    time.sleep(0.1)  # Pequeña pausa para visualización

                # Completado
                self.root.after(0, lambda: finish_cleanup(total_deleted, total_kept, total_errors))

            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Error", f"Error durante la limpieza: {str(e)}"))
                self.root.after(0, progress_window.destroy)

        def finish_cleanup(deleted, kept, errors):
            status_label.config(text="✅ Limpieza completada!", foreground="green")
            progress_bar['value'] = 100

            ttk.Button(progress_window, text="✅ Cerrar",
                      command=lambda: close_and_refresh(progress_window, deleted, kept, errors)).pack(pady=10)

        def close_and_refresh(window, deleted, kept, errors):
            window.destroy()

            # Mostrar resultados finales
            result_message = f"✅ LIMPIEZA UNIFICADA COMPLETADA\n\n"
            result_message += f"Copias eliminadas: {deleted}\n"
            result_message += f"Copias mantenidas: {kept}\n"

            if errors > 0:
                result_message += f"Errores: {errors}\n"

            result_message += f"\n¡Base de datos optimizada exitosamente!"

            messagebox.showinfo("Limpieza Completada", result_message)

            # Refrescar la vista
            self.refresh_unified_duplicates()

        # Iniciar limpieza en hilo separado
        threading.Thread(target=cleanup_thread, daemon=True).start()

    def preview_unified_cleanup(self):
        """Vista previa de la limpieza unificada"""
        selected_groups = [data for data in self.unified_duplicates_data.values() if data['selected']]

        if not selected_groups:
            messagebox.showwarning("Advertencia", "No hay grupos seleccionados para previsualizar")
            return

        # Crear ventana de vista previa
        preview_window = tk.Toplevel(self.root)
        preview_window.title("Vista Previa de Limpieza")
        preview_window.geometry("700x500")
        preview_window.transient(self.root)
        preview_window.grab_set()

        ttk.Label(preview_window, text="📊 Vista Previa de Limpieza Unificada", font=('Arial', 12, 'bold')).pack(pady=10)

        # Resumen
        summary_frame = ttk.LabelFrame(preview_window, text="Resumen")
        summary_frame.pack(fill="x", padx=10, pady=10)

        total_copies = sum(len(data['copies']) for data in selected_groups)
        estimated_deletions = total_copies - len(selected_groups)

        summary_text = f"""
📊 Grupos seleccionados: {len(selected_groups)}
📁 Total de copias: {total_copies}
🗑️ Copias a eliminar: {estimated_deletions}
💾 Copias a mantener: {len(selected_groups)}
📈 Reducción estimada: {(estimated_deletions / max(total_copies, 1) * 100):.1f}%
        """
        ttk.Label(summary_frame, text=summary_text, font=('Arial', 10)).pack(padx=10, pady=5)

        # Lista detallada
        details_frame = ttk.LabelFrame(preview_window, text="Detalles por Grupo")
        details_frame.pack(fill="both", expand=True, padx=10, pady=10)

        preview_text = tk.Text(details_frame, height=15, width=80)
        preview_text.pack(fill="both", expand=True, padx=5, pady=5)

        preview_content = ""
        for i, group_data in enumerate(selected_groups, 1):
            tmdb_id = list(self.unified_duplicates_data.keys())[list(self.unified_duplicates_data.values()).index(group_data)]
            title = group_data['movie_data']['sample_title']
            copies_count = len(group_data['copies'])
            recommendation = group_data['recommendation']

            preview_content += f"{i}. {title} (TMDB: {tmdb_id})\n"
            preview_content += f"   Copias: {copies_count} → Mantener: 1 | Eliminar: {copies_count - 1}\n"
            preview_content += f"   Estrategia: {recommendation}\n\n"

        preview_text.insert(1.0, preview_content)

        # Botones
        button_frame = ttk.Frame(preview_window)
        button_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(button_frame, text="🚀 Ejecutar Limpieza",
                  command=lambda: [preview_window.destroy(), self.execute_unified_cleanup()]).pack(side="left", padx=(0, 10))
        ttk.Button(button_frame, text="❌ Cancelar", command=preview_window.destroy).pack(side="right")

    def export_unified_report(self):
        """Exportar reporte de duplicados unificados"""
        if not self.unified_duplicates_data:
            messagebox.showwarning("Advertencia", "No hay datos para exportar")
            return

        try:
            import csv
            from tkinter import filedialog

            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Guardar reporte de duplicados"
            )

            if not filename:
                return

            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # Encabezados
                writer.writerow(["TMDB ID", "Título", "Total Copias", "4K", "FHD", "HD", "SD",
                               "Symlinks", "Direct", "Otros", "Recomendación", "Seleccionado"])

                # Datos
                for tmdb_id, data in self.unified_duplicates_data.items():
                    writer.writerow([
                        tmdb_id,
                        data['movie_data']['sample_title'],
                        len(data['copies']),
                        data['quality_counts']['4K'],
                        data['quality_counts']['FHD'],
                        data['quality_counts']['HD'],
                        data['quality_counts']['SD'],
                        data['type_counts']['symlinks'],
                        data['type_counts']['direct'],
                        data['type_counts']['others'],
                        data['recommendation'],
                        "Sí" if data['selected'] else "No"
                    ])

            messagebox.showinfo("Éxito", f"Reporte exportado a: {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Error exportando reporte: {str(e)}")

    def update_from_tmdb(self):
        """Actualizar información desde TMDB para películas seleccionadas"""
        selected_groups = [data for data in self.unified_duplicates_data.values() if data['selected']]

        if not selected_groups:
            messagebox.showwarning("Advertencia", "No hay grupos seleccionados para actualizar")
            return

        messagebox.showinfo("Actualización TMDB",
                           f"Se actualizarán {len(selected_groups)} grupos desde TMDB.\n"
                           "Esta funcionalidad requiere integración con la API de TMDB.")

    def connect_database(self):
        """Conectar a la base de datos"""
        host = self.host_var.get().strip()
        user = self.user_var.get().strip()
        password = self.password_var.get()
        database = self.database_var.get().strip()

        try:
            port = int(self.port_var.get())
        except ValueError:
            messagebox.showerror("Error", "El puerto debe ser un número válido")
            return

        if not all([host, user, database]):
            messagebox.showerror("Error", "Por favor complete todos los campos obligatorios")
            return

        # Conectar en un hilo separado para no bloquear la UI
        def connect_thread():
            if self.db.connect(host, user, password, database, port):
                self.root.after(0, self.on_connection_success)
            else:
                self.root.after(0, self.on_connection_error)

        threading.Thread(target=connect_thread, daemon=True).start()
        self.status_var.set("Conectando...")
        self.status_label.config(foreground="orange")

    def on_connection_success(self):
        """Callback cuando la conexión es exitosa"""
        self.is_connected = True
        self.status_var.set("Conectado")
        self.status_label.config(foreground="green")
        self.connect_btn.config(state="disabled")
        self.disconnect_btn.config(state="normal")
        messagebox.showinfo("Éxito", "Conexión establecida correctamente")

    def on_connection_error(self):
        """Callback cuando hay error en la conexión"""
        self.status_var.set("Error de conexión")
        self.status_label.config(foreground="red")
        messagebox.showerror("Error", "No se pudo conectar a la base de datos")

    def disconnect_database(self):
        """Desconectar de la base de datos"""
        self.db.disconnect()
        self.is_connected = False
        self.status_var.set("Desconectado")
        self.status_label.config(foreground="red")
        self.connect_btn.config(state="normal")
        self.disconnect_btn.config(state="disabled")

        # Limpiar todas las listas
        self.clear_all_trees()

    def clear_all_trees(self):
        """Limpiar todos los treeviews"""
        for tree in [self.orphaned_tree, self.series_no_episodes_tree,
                     self.series_tree, self.duplicate_movies_tree, self.movies_tree,
                     self.tmdb_duplicates_tree, self.priority_tree, self.mass_tree]:
            for item in tree.get_children():
                tree.delete(item)

    def check_connection(self):
        """Verificar si hay conexión antes de ejecutar operaciones"""
        if not self.is_connected or not self.db.is_connected():
            messagebox.showerror("Error", "No hay conexión a la base de datos")
            return False
        return True

    def load_orphaned_episodes(self):
        """Cargar episodios huérfanos"""
        if not self.check_connection():
            return

        # Limpiar lista actual
        for item in self.orphaned_tree.get_children():
            self.orphaned_tree.delete(item)

        episodes = self.db.get_orphaned_episodes()
        for episode in episodes:
            self.orphaned_tree.insert("", "end", values=(
                episode.get('episode_id', ''),
                episode.get('stream_id', ''),
                episode.get('series_id', ''),
                episode.get('type', ''),
                episode.get('type_name', '')
            ))

    def load_series_without_episodes(self):
        """Cargar series sin episodios"""
        if not self.check_connection():
            return

        # Limpiar lista actual
        for item in self.series_no_episodes_tree.get_children():
            self.series_no_episodes_tree.delete(item)

        series = self.db.get_series_without_episodes()
        for serie in series:
            self.series_no_episodes_tree.insert("", "end", values=(
                serie.get('series_id', ''),
                serie.get('series_title', '')
            ))

    def load_series_with_episodes(self):
        """Cargar series con episodios"""
        if not self.check_connection():
            return

        # Limpiar lista actual
        for item in self.series_tree.get_children():
            self.series_tree.delete(item)

        series = self.db.get_series_with_episodes()
        for serie in series:
            self.series_tree.insert("", "end", values=(
                serie.get('series_id', ''),
                serie.get('series_title', ''),
                serie.get('type', ''),
                serie.get('type_name', ''),
                serie.get('episode_count', 0)
            ))

    def load_duplicate_movies(self):
        """Cargar películas duplicadas"""
        if not self.check_connection():
            return

        # Limpiar lista actual
        for item in self.duplicate_movies_tree.get_children():
            self.duplicate_movies_tree.delete(item)

        movies = self.db.get_duplicate_movies()
        for movie in movies:
            self.duplicate_movies_tree.insert("", "end", values=(
                movie.get('title', ''),
                movie.get('duplicate_count', 0),
                movie.get('stream_ids', '')
            ))

    def assign_episode_to_series(self):
        """Asignar episodio seleccionado a una serie existente"""
        if not self.check_connection():
            return

        selected = self.orphaned_tree.selection()
        if not selected:
            messagebox.showwarning("Advertencia", "Por favor seleccione un episodio")
            return

        episode_data = self.orphaned_tree.item(selected[0])['values']
        episode_id = episode_data[0]

        # Obtener lista de series disponibles
        series_list = self.db.execute_query("SELECT id, title FROM streams_series ORDER BY title")
        if not series_list:
            messagebox.showinfo("Info", "No hay series disponibles. Cree una nueva serie primero.")
            return

        # Crear ventana de selección de serie
        self.show_series_selection_dialog(episode_id, series_list)

    def show_series_selection_dialog(self, episode_id, series_list):
        """Mostrar diálogo para seleccionar serie"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Seleccionar Serie")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text="Seleccione una serie:").pack(pady=10)

        # Listbox con series
        listbox = tk.Listbox(dialog, height=10)
        listbox.pack(fill="both", expand=True, padx=10, pady=10)

        for serie in series_list:
            listbox.insert("end", f"{serie['id']} - {serie['title']}")

        def assign_selected():
            selection = listbox.curselection()
            if not selection:
                messagebox.showwarning("Advertencia", "Por favor seleccione una serie")
                return

            series_id = series_list[selection[0]]['id']
            if self.db.assign_episode_to_series(episode_id, series_id):
                messagebox.showinfo("Éxito", "Episodio asignado correctamente")
                self.load_orphaned_episodes()
                dialog.destroy()
            else:
                messagebox.showerror("Error", "No se pudo asignar el episodio")

        ttk.Button(dialog, text="Asignar", command=assign_selected).pack(pady=10)
        ttk.Button(dialog, text="Cancelar", command=dialog.destroy).pack()

    def create_new_series_for_episode(self):
        """Crear nueva serie y asignar episodio"""
        if not self.check_connection():
            return

        selected = self.orphaned_tree.selection()
        if not selected:
            messagebox.showwarning("Advertencia", "Por favor seleccione un episodio")
            return

        episode_data = self.orphaned_tree.item(selected[0])['values']
        episode_id = episode_data[0]

        # Solicitar nombre de la nueva serie
        series_title = simpledialog.askstring("Nueva Serie", "Ingrese el título de la nueva serie:")
        if not series_title:
            return

        # Crear la serie
        series_id = self.db.create_series(series_title.strip())
        if series_id:
            # Asignar el episodio a la nueva serie
            if self.db.assign_episode_to_series(episode_id, series_id):
                messagebox.showinfo("Éxito", f"Serie '{series_title}' creada y episodio asignado correctamente")
                self.load_orphaned_episodes()
            else:
                messagebox.showerror("Error", "Serie creada pero no se pudo asignar el episodio")
        else:
            messagebox.showerror("Error", "No se pudo crear la serie")

    def delete_selected_episode(self):
        """Eliminar episodio seleccionado"""
        if not self.check_connection():
            return

        selected = self.orphaned_tree.selection()
        if not selected:
            messagebox.showwarning("Advertencia", "Por favor seleccione un episodio")
            return

        episode_data = self.orphaned_tree.item(selected[0])['values']
        episode_id = episode_data[0]

        # Confirmar eliminación
        if messagebox.askyesno("Confirmar", f"¿Está seguro de eliminar el episodio ID {episode_id}?"):
            if self.db.delete_episode(episode_id):
                messagebox.showinfo("Éxito", "Episodio eliminado correctamente")
                self.load_orphaned_episodes()
            else:
                messagebox.showerror("Error", "No se pudo eliminar el episodio")

    def delete_selected_series(self):
        """Eliminar serie seleccionada"""
        if not self.check_connection():
            return

        selected = self.series_no_episodes_tree.selection()
        if not selected:
            messagebox.showwarning("Advertencia", "Por favor seleccione una serie")
            return

        series_data = self.series_no_episodes_tree.item(selected[0])['values']
        series_id = series_data[0]
        series_title = series_data[1]

        # Confirmar eliminación
        if messagebox.askyesno("Confirmar", f"¿Está seguro de eliminar la serie '{series_title}'?"):
            if self.db.delete_series(series_id):
                messagebox.showinfo("Éxito", "Serie eliminada correctamente")
                self.load_series_without_episodes()
            else:
                messagebox.showerror("Error", "No se pudo eliminar la serie")

    def load_all_movies(self):
        """Cargar todas las películas"""
        if not self.check_connection():
            return

        # Limpiar lista actual
        for item in self.movies_tree.get_children():
            self.movies_tree.delete(item)

        movies = self.db.get_all_movies()
        for movie in movies:
            title = movie.get('display_title', movie.get('title', '[Sin Título]'))
            self.movies_tree.insert("", "end", values=(
                movie.get('stream_id', ''),
                title,
                movie.get('type_id', ''),
                movie.get('type_name', '')
            ))

    def search_movies(self):
        """Buscar películas por nombre"""
        if not self.check_connection():
            return

        search_term = self.movie_search_var.get().strip()
        if not search_term:
            messagebox.showwarning("Advertencia", "Por favor ingrese un término de búsqueda")
            return

        # Limpiar lista actual
        for item in self.movies_tree.get_children():
            self.movies_tree.delete(item)

        # Usar búsqueda por patrón en lugar de coincidencia exacta
        movies = self.db.search_movies_by_name(search_term)
        for movie in movies:
            title = movie.get('display_title', movie.get('title', '[Sin Título]'))
            self.movies_tree.insert("", "end", values=(
                movie.get('stream_id', ''),
                title,
                movie.get('type_id', ''),
                movie.get('type_name', '')
            ))

        if not movies:
            messagebox.showinfo("Resultado", f"No se encontraron películas con el nombre '{search_term}'")

    def show_movie_duplicates(self):
        """Mostrar duplicados de la película seleccionada"""
        if not self.check_connection():
            return

        selected = self.movies_tree.selection()
        if not selected:
            messagebox.showwarning("Advertencia", "Por favor seleccione una película")
            return

        movie_data = self.movies_tree.item(selected[0])['values']
        movie_title = movie_data[1]

        # Buscar duplicados de esta película
        duplicates = self.db.get_movies_by_name(movie_title)

        if len(duplicates) <= 1:
            messagebox.showinfo("Resultado", f"No se encontraron duplicados de '{movie_title}'")
            return

        # Crear ventana para mostrar duplicados
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Duplicados de: {movie_title}")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text=f"Duplicados encontrados de '{movie_title}': {len(duplicates)} copias").pack(pady=10)

        # Treeview para mostrar duplicados
        dup_columns = ("ID Stream", "Título", "Tipo ID", "Tipo")
        dup_tree = ttk.Treeview(dialog, columns=dup_columns, show="headings", height=15)

        for col in dup_columns:
            dup_tree.heading(col, text=col)
            if col == "Título":
                dup_tree.column(col, width=250)
            elif col == "ID Stream":
                dup_tree.column(col, width=80)
            elif col == "Tipo ID":
                dup_tree.column(col, width=60)
            else:
                dup_tree.column(col, width=100)

        for dup in duplicates:
            dup_tree.insert("", "end", values=(
                dup.get('stream_id', ''),
                dup.get('title', ''),
                dup.get('type_id', ''),
                dup.get('type_name', '')
            ))

        dup_tree.pack(fill="both", expand=True, padx=10, pady=10)

        ttk.Button(dialog, text="Cerrar", command=dialog.destroy).pack(pady=10)

    def delete_selected_movie(self):
        """Eliminar película seleccionada (función legacy - usar delete_selected_movies)"""
        # Redirigir a la nueva función de eliminación múltiple
        self.delete_selected_movies()

    def load_movies_without_title(self):
        """Cargar películas sin título"""
        if not self.check_connection():
            return

        # Limpiar lista actual
        for item in self.movies_tree.get_children():
            self.movies_tree.delete(item)

        movies = self.db.get_movies_without_title()
        for movie in movies:
            self.movies_tree.insert("", "end", values=(
                movie.get('stream_id', ''),
                movie.get('display_title', '[Sin Título]'),
                movie.get('type_id', ''),
                movie.get('type_name', '')
            ))

        if movies:
            messagebox.showinfo("Resultado", f"Se encontraron {len(movies)} películas sin título\n\nPuedes seleccionar múltiples películas usando Ctrl+Click o Shift+Click")
        else:
            messagebox.showinfo("Resultado", "No se encontraron películas sin título")

    def load_tmdb_movie_duplicates(self):
        """Cargar duplicados de películas por TMDB ID"""
        if not self.check_connection():
            return

        # Limpiar lista actual
        for item in self.tmdb_duplicates_tree.get_children():
            self.tmdb_duplicates_tree.delete(item)

        duplicates = self.db.get_duplicate_movies_by_tmdb()
        for dup in duplicates:
            self.tmdb_duplicates_tree.insert("", "end", values=(
                dup.get('tmdb_id', ''),
                dup.get('sample_title', ''),
                dup.get('duplicate_count', 0),
                dup.get('stream_ids', ''),
                'Película'
            ))

        if duplicates:
            messagebox.showinfo("Resultado", f"Se encontraron {len(duplicates)} grupos de películas duplicadas por TMDB ID")

    def load_tmdb_series_duplicates(self):
        """Cargar duplicados de series por TMDB ID"""
        if not self.check_connection():
            return

        # Limpiar lista actual
        for item in self.tmdb_duplicates_tree.get_children():
            self.tmdb_duplicates_tree.delete(item)

        duplicates = self.db.get_duplicate_series_by_tmdb()
        for dup in duplicates:
            self.tmdb_duplicates_tree.insert("", "end", values=(
                dup.get('tmdb_id', ''),
                dup.get('sample_title', ''),
                dup.get('duplicate_count', 0),
                dup.get('series_ids', ''),
                'Serie'
            ))

        if duplicates:
            messagebox.showinfo("Resultado", f"Se encontraron {len(duplicates)} grupos de series duplicadas por TMDB ID")

    def toggle_auto_refresh(self):
        """Activar/desactivar auto-refresh"""
        self.auto_refresh_enabled = not self.auto_refresh_enabled

        if self.auto_refresh_enabled:
            self.refresh_status_var.set(f"Auto-refresh: ON ({self.refresh_interval}s)")
            self.start_auto_refresh()
        else:
            self.refresh_status_var.set("Auto-refresh: OFF")

    def start_auto_refresh(self):
        """Iniciar auto-refresh en hilo separado"""
        def refresh_loop():
            while self.auto_refresh_enabled and self.is_connected:
                time.sleep(self.refresh_interval)
                if self.auto_refresh_enabled and self.is_connected:
                    # Actualizar solo la pestaña activa
                    current_tab = self.notebook.tab(self.notebook.select(), "text")
                    if current_tab == "Duplicados TMDB":
                        self.root.after(0, self.refresh_current_tmdb_view)
                    elif current_tab == "Gestión de Películas":
                        self.root.after(0, self.load_all_movies)
                    elif current_tab == "Gestión de Series":
                        self.root.after(0, self.load_series_with_episodes)

        threading.Thread(target=refresh_loop, daemon=True).start()

    def refresh_current_tmdb_view(self):
        """Refrescar la vista actual de TMDB"""
        if self.tmdb_duplicates_tree.get_children():
            # Determinar qué tipo de duplicados se están mostrando
            first_item = self.tmdb_duplicates_tree.item(self.tmdb_duplicates_tree.get_children()[0])
            item_type = first_item['values'][4] if len(first_item['values']) > 4 else ''

            if item_type == 'Película':
                self.load_tmdb_movie_duplicates()
            elif item_type == 'Serie':
                self.load_tmdb_series_duplicates()

    def select_all_movies(self):
        """Seleccionar todas las películas en la lista"""
        for item in self.movies_tree.get_children():
            self.movies_tree.selection_add(item)

    def delete_selected_movies(self):
        """Eliminar múltiples películas seleccionadas"""
        if not self.check_connection():
            return

        selected_items = self.movies_tree.selection()
        if not selected_items:
            messagebox.showwarning("Advertencia", "Por favor seleccione una o más películas para eliminar")
            return

        # Obtener información de las películas seleccionadas
        selected_movies = []
        for item in selected_items:
            movie_data = self.movies_tree.item(item)['values']
            selected_movies.append({
                'stream_id': movie_data[0],
                'title': movie_data[1],
                'item': item
            })

        # Confirmar eliminación
        count = len(selected_movies)
        if count == 1:
            movie = selected_movies[0]
            message = f"¿Está seguro de eliminar la película '{movie['title']}' (ID: {movie['stream_id']})?"
        else:
            message = f"¿Está seguro de eliminar {count} películas seleccionadas?\n\nEsta acción no se puede deshacer."

        if not messagebox.askyesno("Confirmar Eliminación", message):
            return

        # Eliminar películas (usar eliminación en lote si hay muchas)
        if count > 10:
            # Eliminación en lote para mejor rendimiento
            stream_ids = [movie['stream_id'] for movie in selected_movies]
            result = self.db.delete_multiple_movies(stream_ids)
            deleted_count = result["deleted"]
            failed_count = result["failed"]

            # Remover elementos eliminados de la vista
            if deleted_count > 0:
                for movie in selected_movies:
                    try:
                        self.movies_tree.delete(movie['item'])
                    except:
                        pass  # El item ya fue eliminado

            failed_movies = [f"IDs fallidos: {failed_count}"] if failed_count > 0 else []
        else:
            # Eliminación individual para pocos elementos
            deleted_count = 0
            failed_count = 0
            failed_movies = []

            for movie in selected_movies:
                try:
                    if self.db.delete_movie(movie['stream_id']):
                        deleted_count += 1
                        # Remover de la lista visual
                        self.movies_tree.delete(movie['item'])
                    else:
                        failed_count += 1
                        failed_movies.append(f"ID: {movie['stream_id']}")
                except Exception as e:
                    failed_count += 1
                    failed_movies.append(f"ID: {movie['stream_id']} (Error: {str(e)})")

        # Mostrar resultado
        if deleted_count > 0 and failed_count == 0:
            messagebox.showinfo("Éxito", f"Se eliminaron {deleted_count} películas correctamente")
        elif deleted_count > 0 and failed_count > 0:
            failed_list = "\n".join(failed_movies[:5])  # Mostrar solo las primeras 5
            if len(failed_movies) > 5:
                failed_list += f"\n... y {len(failed_movies) - 5} más"
            messagebox.showwarning("Eliminación Parcial",
                                 f"Se eliminaron {deleted_count} películas correctamente\n"
                                 f"Fallaron {failed_count} eliminaciones:\n\n{failed_list}")
        else:
            messagebox.showerror("Error", f"No se pudo eliminar ninguna película\n\nErrores: {failed_count}")

    def show_tmdb_details(self):
        """Mostrar detalles TMDB del elemento seleccionado"""
        if not self.check_connection():
            return

        selected = self.tmdb_duplicates_tree.selection()
        if not selected:
            messagebox.showwarning("Advertencia", "Por favor seleccione un elemento")
            return

        item_data = self.tmdb_duplicates_tree.item(selected[0])['values']
        tmdb_id = item_data[0]
        title = item_data[1]
        item_type = item_data[4]

        # Crear ventana de detalles
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Detalles TMDB: {title}")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text=f"TMDB ID: {tmdb_id}", font=('Arial', 12, 'bold')).pack(pady=10)
        ttk.Label(dialog, text=f"Título: {title}").pack(pady=5)
        ttk.Label(dialog, text=f"Tipo: {item_type}").pack(pady=5)
        ttk.Label(dialog, text="Cargando detalles de TMDB...", font=('Arial', 10, 'italic')).pack(pady=20)

        ttk.Button(dialog, text="Cerrar", command=dialog.destroy).pack(pady=10)

    def delete_tmdb_duplicates(self):
        """Eliminar duplicados TMDB seleccionados"""
        if not self.check_connection():
            return

        selected_items = self.tmdb_duplicates_tree.selection()
        if not selected_items:
            messagebox.showwarning("Advertencia", "Por favor seleccione uno o más elementos")
            return

        # Obtener IDs de los elementos seleccionados
        all_ids = []
        for item in selected_items:
            item_data = self.tmdb_duplicates_tree.item(item)['values']
            ids = item_data[3].split(',')  # IDs están en la columna 3
            all_ids.extend([int(id.strip()) for id in ids])

        if messagebox.askyesno("Confirmar", f"¿Eliminar {len(all_ids)} elementos duplicados?\n\nEsta acción no se puede deshacer."):
            result = self.db.delete_multiple_movies(all_ids)

            if result["deleted"] > 0:
                messagebox.showinfo("Éxito", f"Se eliminaron {result['deleted']} elementos correctamente")
                # Refrescar la vista
                self.refresh_current_tmdb_view()
            else:
                messagebox.showerror("Error", f"No se pudo eliminar ningún elemento")

    def update_from_tmdb(self):
        """Actualizar información desde TMDB"""
        messagebox.showinfo("Información", "Función de actualización TMDB en desarrollo.\n\nPróximamente podrás actualizar títulos, fechas y metadatos desde TMDB.")

    def load_priority_duplicates(self):
        """Cargar duplicados con información de prioridad"""
        if not self.check_connection():
            return

        # Limpiar lista actual
        for item in self.priority_tree.get_children():
            self.priority_tree.delete(item)

        duplicates = self.db.get_duplicate_movies_with_priority()
        for dup in duplicates:
            # Generar recomendación
            symlink_count = dup.get('symlink_count', 0)
            direct_count = dup.get('direct_count', 0)
            other_count = dup.get('other_count', 0)

            if symlink_count > 0:
                recommendation = f"Mantener symlink, eliminar {direct_count + other_count}"
            elif direct_count > 0:
                recommendation = f"Mantener 1 direct, eliminar {direct_count - 1}"
            else:
                recommendation = f"Mantener 1, eliminar {other_count - 1}"

            self.priority_tree.insert("", "end", values=(
                dup.get('tmdb_id', ''),
                dup.get('sample_title', ''),
                dup.get('duplicate_count', 0),
                symlink_count,
                direct_count,
                other_count,
                recommendation
            ))

        if duplicates:
            messagebox.showinfo("Resultado", f"Se encontraron {len(duplicates)} grupos de duplicados con diferentes prioridades")

    def select_all_priority_duplicates(self):
        """Seleccionar todos los duplicados de prioridad"""
        for item in self.priority_tree.get_children():
            self.priority_tree.selection_add(item)

        selected_count = len(self.priority_tree.selection())
        if selected_count > 0:
            messagebox.showinfo("Selección", f"Se seleccionaron {selected_count} grupos de duplicados")

    def apply_selected_recommendations(self):
        """Aplicar recomendaciones a todos los elementos seleccionados"""
        if not self.check_connection():
            return

        selected_items = self.priority_tree.selection()
        if not selected_items:
            messagebox.showwarning("Advertencia", "Por favor seleccione uno o más grupos de duplicados")
            return

        # Obtener información de los elementos seleccionados
        selected_groups = []
        total_to_delete = 0
        total_to_keep = 0

        for item in selected_items:
            item_data = self.priority_tree.item(item)['values']
            tmdb_id = int(item_data[0])
            title = item_data[1]

            # Obtener recomendaciones para este grupo
            recommendations = self.db.get_recommended_deletions(tmdb_id)

            selected_groups.append({
                'tmdb_id': tmdb_id,
                'title': title,
                'recommendations': recommendations,
                'item': item
            })

            total_to_delete += len(recommendations['delete'])
            total_to_keep += len(recommendations['keep'])

        # Mostrar resumen y confirmar
        message = f"LIMPIEZA MASIVA DE DUPLICADOS\n\n"
        message += f"Grupos seleccionados: {len(selected_groups)}\n"
        message += f"Total a mantener: {total_to_keep} copias\n"
        message += f"Total a eliminar: {total_to_delete} copias\n\n"

        if total_to_delete == 0:
            messagebox.showinfo("Información", "No hay duplicados para eliminar en los grupos seleccionados")
            return

        message += "Ejemplos de grupos a procesar:\n"
        for i, group in enumerate(selected_groups[:5]):
            rec = group['recommendations']
            message += f"• {group['title']}: mantener {len(rec['keep'])}, eliminar {len(rec['delete'])}\n"

        if len(selected_groups) > 5:
            message += f"... y {len(selected_groups) - 5} grupos más\n"

        message += f"\n¿Proceder con la eliminación masiva?"

        if not messagebox.askyesno("🚀 Confirmar Limpieza Masiva", message):
            return

        # Mostrar mensaje de inicio
        messagebox.showinfo("Iniciando Limpieza", f"Iniciando limpieza masiva de {len(selected_groups)} grupos...\n\nSe abrirá una ventana de progreso.")

        # Aplicar limpieza a cada grupo seleccionado
        self.apply_mass_cleanup(selected_groups)

    def apply_mass_cleanup(self, selected_groups):
        """Aplicar limpieza masiva con progreso"""
        # Crear ventana de progreso
        progress_dialog = tk.Toplevel(self.root)
        progress_dialog.title("Limpieza Masiva en Progreso")
        progress_dialog.geometry("500x300")
        progress_dialog.transient(self.root)
        progress_dialog.grab_set()

        # Variables de progreso
        total_groups = len(selected_groups)
        current_group = tk.IntVar(value=0)
        total_deleted = tk.IntVar(value=0)
        total_kept = tk.IntVar(value=0)
        total_errors = tk.IntVar(value=0)

        # Widgets de progreso
        title_label = ttk.Label(progress_dialog, text="🚀 Procesando Limpieza Masiva...", font=('Arial', 14, 'bold'), foreground="blue")
        title_label.pack(pady=10)

        progress_frame = ttk.Frame(progress_dialog)
        progress_frame.pack(fill="x", padx=20, pady=10)

        progress_bar = ttk.Progressbar(progress_frame, length=400, mode='determinate')
        progress_bar.pack(fill="x")

        status_label = ttk.Label(progress_dialog, text="Iniciando...", font=('Arial', 10))
        status_label.pack(pady=5)

        stats_frame = ttk.LabelFrame(progress_dialog, text="Estadísticas")
        stats_frame.pack(fill="x", padx=20, pady=10)

        stats_text = tk.Text(stats_frame, height=8, width=60)
        stats_text.pack(padx=10, pady=10)

        def update_progress():
            current = current_group.get()
            progress_percent = (current / total_groups) * 100
            progress_bar['value'] = progress_percent

            if current < total_groups:
                status_label.config(text=f"🔄 Procesando grupo {current}/{total_groups} ({progress_percent:.1f}%)")
            else:
                status_label.config(text=f"✅ Completado: {current}/{total_groups} grupos procesados", foreground="green")

            # Actualizar estadísticas con más detalle
            stats_text.delete(1.0, tk.END)
            stats_content = f"📊 PROGRESO DE LIMPIEZA:\n"
            stats_content += f"Grupos procesados: {current}/{total_groups}\n"
            stats_content += f"Copias eliminadas: {total_deleted.get()}\n"
            stats_content += f"Copias mantenidas: {total_kept.get()}\n"

            if total_errors.get() > 0:
                stats_content += f"⚠️ Errores: {total_errors.get()}\n"
            else:
                stats_content += f"✅ Sin errores\n"

            # Calcular reducción en tiempo real
            total_processed = total_deleted.get() + total_kept.get()
            if total_processed > 0:
                reduction = (total_deleted.get() / total_processed) * 100
                stats_content += f"📈 Reducción: {reduction:.1f}%\n"

            stats_text.insert(1.0, stats_content)
            progress_dialog.update()

        def process_cleanup():
            try:
                for i, group in enumerate(selected_groups):
                    current_group.set(i + 1)

                    # Actualizar estado con más información
                    status_label.config(text=f"🔄 Procesando: {group['title'][:40]}...")
                    update_progress()

                    # Aplicar limpieza
                    result = self.db.apply_smart_cleanup(group['tmdb_id'], auto_confirm=True)

                    total_deleted.set(total_deleted.get() + result['deleted'])
                    total_kept.set(total_kept.get() + result['kept'])

                    if result['deleted'] == 0 and len(group['recommendations']['delete']) > 0:
                        total_errors.set(total_errors.get() + 1)

                    # Remover elemento de la vista si se procesó correctamente
                    if result['deleted'] > 0:
                        try:
                            self.priority_tree.delete(group['item'])
                        except:
                            pass  # El item ya fue eliminado

                    update_progress()

                # Completado
                status_label.config(text="¡Limpieza masiva completada exitosamente!", foreground="green", font=('Arial', 12, 'bold'))
                progress_bar['value'] = 100
                update_progress()

                # Mensaje final de éxito
                success_frame = ttk.LabelFrame(progress_dialog, text="Limpieza Completada")
                success_frame.pack(fill="x", padx=20, pady=10)

                success_message = f"✅ Se limpiaron satisfactoriamente {current_group.get()} grupos de duplicados\n"
                success_message += f"✅ Copias eliminadas: {total_deleted.get()}\n"
                success_message += f"✅ Copias mantenidas: {total_kept.get()}\n"

                if total_errors.get() > 0:
                    success_message += f"⚠️ Errores encontrados: {total_errors.get()}\n"

                success_message += f"\n🎉 ¡Tu base de datos está más limpia y optimizada!"

                success_label = ttk.Label(success_frame, text=success_message, font=('Arial', 10), foreground="darkgreen")
                success_label.pack(padx=10, pady=10)

                # Botón OK para cerrar
                def close_dialog():
                    self.finish_mass_cleanup(progress_dialog, total_deleted.get(), total_kept.get(), total_errors.get())

                ok_button = ttk.Button(progress_dialog, text="OK - Cerrar",
                                     command=close_dialog,
                                     style="Accent.TButton")
                ok_button.pack(pady=15)
                ok_button.focus_set()  # Dar foco al botón para que se pueda presionar Enter

                # Permitir cerrar con Enter
                progress_dialog.bind('<Return>', lambda e: close_dialog())
                progress_dialog.bind('<KP_Enter>', lambda e: close_dialog())

            except Exception as e:
                messagebox.showerror("Error", f"Error durante la limpieza masiva: {str(e)}")
                progress_dialog.destroy()

        # Iniciar procesamiento en hilo separado
        threading.Thread(target=process_cleanup, daemon=True).start()

    def finish_mass_cleanup(self, dialog, deleted, kept, errors):
        """Finalizar limpieza masiva y mostrar resultados"""
        dialog.destroy()

        # Calcular estadísticas finales
        total_processed = deleted + kept
        reduction_percent = (deleted / total_processed * 100) if total_processed > 0 else 0

        # Mostrar resultados finales detallados
        result_message = f"🎉 LIMPIEZA MASIVA COMPLETADA EXITOSAMENTE\n\n"
        result_message += f"📊 ESTADÍSTICAS FINALES:\n"
        result_message += f"✅ Copias eliminadas: {deleted}\n"
        result_message += f"✅ Copias mantenidas: {kept} (las mejores)\n"
        result_message += f"📈 Reducción lograda: {reduction_percent:.1f}%\n"

        if errors > 0:
            result_message += f"⚠️ Errores encontrados: {errors}\n"
        else:
            result_message += f"✅ Sin errores - Proceso perfecto\n"

        result_message += f"\n🚀 BENEFICIOS OBTENIDOS:\n"
        result_message += f"• Base de datos más limpia y rápida\n"
        result_message += f"• Solo las mejores copias mantenidas\n"
        result_message += f"• Symlinks priorizados automáticamente\n"
        result_message += f"• Menos duplicados en el panel XUI\n"

        result_message += f"\n¡Tu servidor XUI está optimizado!"

        messagebox.showinfo("🎉 Limpieza Masiva Exitosa", result_message)

        # Refrescar las vistas
        self.load_priority_duplicates()

        # Si estamos en la pestaña de limpieza masiva, refrescar también
        current_tab = self.notebook.tab(self.notebook.select(), "text")
        if current_tab == "Limpieza Masiva":
            self.load_all_duplicates_for_mass()

    def show_priority_details(self):
        """Mostrar detalles de prioridad del elemento seleccionado"""
        if not self.check_connection():
            return

        selected = self.priority_tree.selection()
        if not selected:
            messagebox.showwarning("Advertencia", "Por favor seleccione un elemento")
            return

        item_data = self.priority_tree.item(selected[0])['values']
        tmdb_id = item_data[0]
        title = item_data[1]

        # Obtener detalles de las copias
        copies = self.db.get_movie_copies_details(int(tmdb_id))

        if not copies:
            messagebox.showerror("Error", "No se encontraron copias")
            return

        # Crear ventana de detalles
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Detalles de Prioridad: {title}")
        dialog.geometry("800x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # Información general
        info_frame = ttk.Frame(dialog)
        info_frame.pack(fill="x", padx=10, pady=10)

        ttk.Label(info_frame, text=f"TMDB ID: {tmdb_id}", font=('Arial', 12, 'bold')).pack(anchor="w")
        ttk.Label(info_frame, text=f"Título: {title}", font=('Arial', 11)).pack(anchor="w")
        ttk.Label(info_frame, text=f"Total de copias: {len(copies)}", font=('Arial', 10)).pack(anchor="w")

        # Treeview para mostrar copias
        copy_columns = ("ID", "Prioridad", "Symlink", "Direct Src", "Direct Prx", "Fuente")
        copy_tree = ttk.Treeview(dialog, columns=copy_columns, show="headings", height=15)

        for col in copy_columns:
            copy_tree.heading(col, text=col)
            if col == "Fuente":
                copy_tree.column(col, width=200)
            elif col == "Prioridad":
                copy_tree.column(col, width=150)
            else:
                copy_tree.column(col, width=80)

        for copy in copies:
            source = str(copy.get('stream_source', ''))[:30] + "..." if len(str(copy.get('stream_source', ''))) > 30 else str(copy.get('stream_source', ''))
            copy_tree.insert("", "end", values=(
                copy.get('stream_id', ''),
                copy.get('priority_type', ''),
                copy.get('movie_symlink', ''),
                copy.get('direct_source', ''),
                copy.get('direct_proxy', ''),
                source
            ))

        copy_tree.pack(fill="both", expand=True, padx=10, pady=10)

        # Obtener recomendaciones
        recommendations = self.db.get_recommended_deletions(int(tmdb_id))

        # Frame para recomendaciones
        rec_frame = ttk.LabelFrame(dialog, text="Recomendaciones")
        rec_frame.pack(fill="x", padx=10, pady=10)

        ttk.Label(rec_frame, text=f"Razón: {recommendations['reason']}", font=('Arial', 10, 'bold')).pack(anchor="w", padx=5, pady=2)
        ttk.Label(rec_frame, text=f"Mantener: {len(recommendations['keep'])} copias", foreground="green").pack(anchor="w", padx=5, pady=1)
        ttk.Label(rec_frame, text=f"Eliminar: {len(recommendations['delete'])} copias", foreground="red").pack(anchor="w", padx=5, pady=1)

        ttk.Button(dialog, text="Cerrar", command=dialog.destroy).pack(pady=10)

    def apply_recommendation(self):
        """Aplicar recomendación de limpieza para el elemento seleccionado"""
        if not self.check_connection():
            return

        selected = self.priority_tree.selection()
        if not selected:
            messagebox.showwarning("Advertencia", "Por favor seleccione un elemento")
            return

        item_data = self.priority_tree.item(selected[0])['values']
        tmdb_id = int(item_data[0])
        title = item_data[1]

        # Obtener recomendaciones
        recommendations = self.db.get_recommended_deletions(tmdb_id)

        if not recommendations["delete"]:
            messagebox.showinfo("Información", "No hay duplicados para eliminar en este elemento")
            return

        # Confirmar acción
        message = f"Película: {title}\n\n"
        message += f"Mantener: {len(recommendations['keep'])} copias\n"
        message += f"Eliminar: {len(recommendations['delete'])} copias\n\n"
        message += f"Razón: {recommendations['reason']}\n\n"
        message += "¿Aplicar esta recomendación?"

        if messagebox.askyesno("Confirmar Limpieza Inteligente", message):
            result = self.db.apply_smart_cleanup(tmdb_id, auto_confirm=True)

            if result["deleted"] > 0:
                messagebox.showinfo("Éxito", f"Se eliminaron {result['deleted']} duplicados correctamente")
                # Refrescar la vista
                self.load_priority_duplicates()
            else:
                messagebox.showerror("Error", "No se pudieron eliminar los duplicados")

    def apply_auto_cleanup(self):
        """Aplicar limpieza automática a todos los duplicados"""
        if not self.check_connection():
            return

        if not messagebox.askyesno("Confirmar Limpieza Automática",
                                 "¿Aplicar limpieza inteligente automática a TODOS los duplicados?\n\n"
                                 "Esto eliminará automáticamente duplicados basándose en prioridades:\n"
                                 "- Mantener symlinks (prioridad alta)\n"
                                 "- Eliminar direct sources (prioridad baja)\n\n"
                                 "Esta acción no se puede deshacer."):
            return

        # Obtener todos los duplicados
        duplicates = self.db.get_duplicate_movies_with_priority()

        if not duplicates:
            messagebox.showinfo("Información", "No hay duplicados para procesar")
            return

        # Aplicar limpieza a cada grupo
        total_deleted = 0
        total_kept = 0
        processed = 0

        for dup in duplicates:
            tmdb_id = dup['tmdb_id']
            result = self.db.apply_smart_cleanup(tmdb_id, auto_confirm=True)
            total_deleted += result["deleted"]
            total_kept += result["kept"]
            processed += 1

        messagebox.showinfo("Limpieza Completada",
                          f"Limpieza automática completada:\n\n"
                          f"Grupos procesados: {processed}\n"
                          f"Copias eliminadas: {total_deleted}\n"
                          f"Copias mantenidas: {total_kept}")

        # Refrescar la vista
        self.load_priority_duplicates()

    def manual_selection(self):
        """Abrir ventana unificada de selección manual con dos paneles"""
        if not self.check_connection():
            return

        # Obtener películas con múltiples symlinks
        movies_with_multiple_symlinks = self.db.get_movies_with_multiple_symlinks()

        if not movies_with_multiple_symlinks:
            messagebox.showinfo("Información", "No se encontraron películas con múltiples symlinks para selección manual.")
            return

        # Crear ventana principal unificada
        main_dialog = tk.Toplevel(self.root)
        main_dialog.title("🎯 Selección Manual de Calidades - Workspace Unificado")
        main_dialog.geometry("1400x800")
        main_dialog.transient(self.root)
        main_dialog.grab_set()

        # Información superior
        info_frame = ttk.LabelFrame(main_dialog, text="🎬 Workspace de Selección Manual")
        info_frame.pack(fill="x", padx=10, pady=10)

        info_text = """🎯 SELECCIÓN MANUAL UNIFICADA: Panel izquierdo = Lista de películas | Panel derecho = Selección de calidades
Selecciona una película del lado izquierdo para ver sus calidades en el lado derecho. Elige qué versiones mantener y ejecuta la limpieza."""
        ttk.Label(info_frame, text=info_text, font=('Arial', 9), foreground="blue").pack(padx=10, pady=5)

        # Frame principal con dos paneles
        main_frame = ttk.Frame(main_dialog)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # PANEL IZQUIERDO: Lista de películas
        left_panel = ttk.LabelFrame(main_frame, text="📋 Películas con Múltiples Symlinks")
        left_panel.pack(side="left", fill="both", expand=True, padx=(0, 5))

        # Treeview de películas
        movies_columns = ("TMDB ID", "Título", "Symlinks", "Calidades")
        self.movies_tree_manual = ttk.Treeview(left_panel, columns=movies_columns, show="headings", height=20)

        for col in movies_columns:
            self.movies_tree_manual.heading(col, text=col)
            if col == "Título":
                self.movies_tree_manual.column(col, width=250)
            elif col == "Calidades":
                self.movies_tree_manual.column(col, width=150)
            else:
                self.movies_tree_manual.column(col, width=80)

        # Cargar datos de películas
        for movie in movies_with_multiple_symlinks:
            titles = movie['all_titles'].split('|||')
            qualities = set()
            for title in titles:
                if '4k' in title.lower() or '4K' in title:
                    qualities.add('4K')
                elif 'fhd' in title.lower():
                    qualities.add('FHD')
                elif 'hd' in title.lower():
                    qualities.add('HD')
                elif 'sd' in title.lower():
                    qualities.add('SD')
                elif '60fps' in title.lower():
                    qualities.add('60FPS')
                elif 'extendida' in title.lower() or 'extended' in title.lower():
                    qualities.add('EXTENDED')
                else:
                    qualities.add('STANDARD')

            qualities_str = ', '.join(sorted(qualities))

            self.movies_tree_manual.insert("", "end", values=(
                movie['tmdb_id'],
                movie['sample_title'],
                movie['symlink_count'],
                qualities_str
            ))

        scrollbar_movies = ttk.Scrollbar(left_panel, orient="vertical", command=self.movies_tree_manual.yview)
        self.movies_tree_manual.configure(yscrollcommand=scrollbar_movies.set)

        self.movies_tree_manual.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar_movies.pack(side="right", fill="y", padx=(0, 10), pady=10)

        # PANEL DERECHO: Selección de calidades
        right_panel = ttk.LabelFrame(main_frame, text="🎬 Selección de Calidades")
        right_panel.pack(side="right", fill="both", expand=True, padx=(5, 0))

        # Información de película seleccionada
        self.selected_movie_info = ttk.Label(right_panel, text="Selecciona una película del panel izquierdo",
                                           font=('Arial', 10, 'bold'), foreground="gray")
        self.selected_movie_info.pack(padx=10, pady=10)

        # Frame para recomendaciones
        self.recommendations_frame = ttk.LabelFrame(right_panel, text="🧠 Recomendaciones Inteligentes")
        self.recommendations_frame.pack(fill="x", padx=10, pady=5)

        self.recommendations_label = ttk.Label(self.recommendations_frame, text="", font=('Arial', 9), foreground="blue")
        self.recommendations_label.pack(padx=10, pady=5)

        # Treeview para selección de calidades
        qualities_columns = ("Sel", "ID", "Título", "Tipo", "Calidad", "Rating", "Año")
        self.qualities_tree = ttk.Treeview(right_panel, columns=qualities_columns, show="headings", height=12)

        for col in qualities_columns:
            self.qualities_tree.heading(col, text=col)
            if col == "Título":
                self.qualities_tree.column(col, width=200)
            elif col == "Sel":
                self.qualities_tree.column(col, width=40)
            elif col == "Tipo":
                self.qualities_tree.column(col, width=100)
            else:
                self.qualities_tree.column(col, width=70)

        scrollbar_qualities = ttk.Scrollbar(right_panel, orient="vertical", command=self.qualities_tree.yview)
        self.qualities_tree.configure(yscrollcommand=scrollbar_qualities.set)

        self.qualities_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        scrollbar_qualities.pack(side="right", fill="y", padx=(0, 10), pady=10)

        # Estadísticas de selección
        self.stats_frame = ttk.LabelFrame(right_panel, text="📊 Estadísticas de Selección")
        self.stats_frame.pack(fill="x", padx=10, pady=5)

        self.stats_text = tk.Text(self.stats_frame, height=3, width=50)
        self.stats_text.pack(padx=10, pady=5)

        # Variables ya inicializadas en __init__

        # Bind para selección de película
        self.movies_tree_manual.bind("<<TreeviewSelect>>", self.on_movie_selection_change)

        # También bind para click simple como backup
        self.movies_tree_manual.bind("<Button-1>", self.on_movie_click)

        # Bind para toggle de calidades
        self.qualities_tree.bind("<Double-1>", self.toggle_quality_selection)

        # Frame de botones de acción rápida
        quick_actions_frame = ttk.Frame(right_panel)
        quick_actions_frame.pack(fill="x", padx=10, pady=5)

        ttk.Button(quick_actions_frame, text="Todos los Symlinks", command=self.select_all_symlinks_unified).pack(side="left", padx=(0, 5))
        ttk.Button(quick_actions_frame, text="Mejor Calidad", command=self.select_best_quality_unified).pack(side="left", padx=(0, 5))
        ttk.Button(quick_actions_frame, text="Limpiar Selección", command=self.clear_selection_unified).pack(side="left", padx=(0, 5))
        ttk.Button(quick_actions_frame, text="🔍 Debug Memoria", command=self.debug_memory_status).pack(side="left")

        # Frame de botones principales
        main_buttons_frame = ttk.Frame(main_dialog)
        main_buttons_frame.pack(fill="x", padx=10, pady=10)

        # Indicador de selecciones guardadas
        self.saved_selections_label = ttk.Label(main_buttons_frame, text="💾 Selecciones guardadas: 0", font=('Arial', 9), foreground="blue")
        self.saved_selections_label.pack(side="left", padx=(0, 20))

        ttk.Button(main_buttons_frame, text="💾 Guardar Selección", command=self.save_current_selection).pack(side="left", padx=(0, 10))
        ttk.Button(main_buttons_frame, text="🚀 Ejecutar Limpieza", command=self.execute_unified_cleanup, style="Accent.TButton").pack(side="left", padx=(0, 10))
        ttk.Button(main_buttons_frame, text="🚀 Ejecutar Todas las Selecciones", command=self.execute_all_saved_selections, style="Accent.TButton").pack(side="left", padx=(0, 10))
        ttk.Button(main_buttons_frame, text="✅ Hecho", command=lambda: self.finish_unified_selection(main_dialog)).pack(side="right")

        # Inicializar panel derecho vacío
        self.clear_right_panel()

        # Inicializar contador de selecciones
        self.update_saved_selections_counter()

    def on_movie_click(self, event):
        """Manejar click en película (backup del TreeviewSelect)"""
        # Usar after para asegurar que la selección se haya actualizado
        self.root.after(50, self.on_movie_selection_change_simple)

    def on_movie_selection_change_simple(self):
        """Versión simplificada del cambio de selección"""
        # Guardar selección actual antes de cambiar
        if self.current_tmdb_id and self.current_selection_data:
            self.save_current_movie_selection()

        selected = self.movies_tree_manual.selection()
        if not selected:
            self.clear_right_panel()
            return

        item_data = self.movies_tree_manual.item(selected[0])['values']
        tmdb_id = int(item_data[0])
        title = item_data[1]
        symlink_count = item_data[2]

        self.load_movie_qualities(tmdb_id, title, symlink_count)

    def clear_right_panel(self):
        """Limpiar el panel derecho"""
        self.selected_movie_info.config(text="Selecciona una película del panel izquierdo")
        self.recommendations_label.config(text="")

        # Limpiar treeview de calidades
        for item in self.qualities_tree.get_children():
            self.qualities_tree.delete(item)

        # Limpiar estadísticas (si existe)
        if hasattr(self, 'stats_text'):
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, "No hay película seleccionada")

        # Reset variables
        self.current_selection_data = {}
        self.current_tmdb_id = None

        # NO limpiar memoria de selecciones aquí - solo al cerrar la ventana

    def on_movie_selection_change(self, event):
        """Manejar cambio de selección de película con memoria"""
        # Usar la versión simple
        self.on_movie_selection_change_simple()

    def save_current_movie_selection(self):
        """Guardar la selección actual de la película en memoria"""
        if not self.current_tmdb_id or not self.current_selection_data:
            return

        # Guardar selección actual usando stream_id como clave
        self.saved_selections[self.current_tmdb_id] = {}
        for item_id, data in self.current_selection_data.items():
            stream_id = data['stream_id']
            self.saved_selections[self.current_tmdb_id][stream_id] = {
                'selected': data['selected'],
                'stream_id': stream_id,
                'copy_data': data['copy_data'].copy()
            }

        # Selección guardada exitosamente

        # Guardar recomendaciones también
        if self.current_tmdb_id not in self.movie_recommendations:
            # Las recomendaciones se guardan en load_movie_qualities
            pass

    def restore_movie_selection(self, tmdb_id: int):
        """Restaurar la selección guardada de una película"""
        if tmdb_id not in self.saved_selections:
            return False

        # Restaurar selección desde memoria usando stream_id
        saved_data = self.saved_selections[tmdb_id]

        # Crear mapeo de stream_id a item_id actual
        stream_to_item = {}
        for item_id, data in self.current_selection_data.items():
            stream_id = data['stream_id']
            stream_to_item[stream_id] = item_id

        # Actualizar current_selection_data y checkboxes visuales
        restored_count = 0
        for stream_id, saved_data_item in saved_data.items():
            if stream_id in stream_to_item:
                item_id = stream_to_item[stream_id]

                # Actualizar selección en memoria actual
                self.current_selection_data[item_id]['selected'] = saved_data_item['selected']

                # Actualizar checkbox visual
                values = list(self.qualities_tree.item(item_id)['values'])
                values[0] = "☑" if saved_data_item['selected'] else "☐"
                self.qualities_tree.item(item_id, values=values)

                restored_count += 1

        return True

    def load_movie_qualities(self, tmdb_id: int, title: str, symlink_count: int):
        """Cargar calidades de la película seleccionada con memoria"""
        self.current_tmdb_id = tmdb_id

        # Actualizar información de película
        info_text = f"🎬 {title} | TMDB: {tmdb_id} | Symlinks: {symlink_count}"

        # Verificar si hay selección guardada
        has_saved_selection = tmdb_id in self.saved_selections
        if has_saved_selection:
            info_text += " 💾 (Selección guardada)"

        self.selected_movie_info.config(text=info_text)

        # Obtener recomendaciones y detalles (o usar las guardadas)
        if tmdb_id in self.movie_recommendations:
            recommendations = self.movie_recommendations[tmdb_id]
        else:
            recommendations = self.db.get_manual_selection_recommendations(tmdb_id)
            self.movie_recommendations[tmdb_id] = recommendations

        # Mostrar recomendaciones
        rec_text = recommendations["recommendations"]
        if has_saved_selection:
            rec_text += "\n💾 Selección personalizada restaurada"
        self.recommendations_label.config(text=rec_text)

        # Limpiar treeview anterior
        for item in self.qualities_tree.get_children():
            self.qualities_tree.delete(item)

        # Reset datos de selección
        self.current_selection_data = {}

        # Cargar todas las copias
        all_copies = recommendations["symlinks"] + recommendations["direct_sources"] + recommendations["others"]

        for copy in all_copies:
            # Determinar selección inicial inteligente (solo si no hay selección guardada)
            if has_saved_selection:
                initial_selected = False  # Se restaurará después
            else:
                initial_selected = False
                if copy['movie_symlink'] == 1:  # Symlinks
                    if len(recommendations["symlinks"]) == 1:
                        initial_selected = True
                    elif copy['detected_quality'] in ['4K', 'FHD']:
                        initial_selected = True
                elif len(recommendations["symlinks"]) == 0 and copy == recommendations["direct_sources"][0]:
                    initial_selected = True

            checkbox = "☑" if initial_selected else "☐"

            item_id = self.qualities_tree.insert("", "end", values=(
                checkbox,
                copy['stream_id'],
                copy['title'][:30] + "..." if len(copy['title']) > 30 else copy['title'],
                copy['source_type'][:10],
                copy['detected_quality'],
                f"{copy['rating']:.1f}" if copy['rating'] else "N/A",
                copy['year'] if copy['year'] else "N/A"
            ))

            self.current_selection_data[item_id] = {
                'selected': initial_selected,
                'stream_id': copy['stream_id'],
                'copy_data': copy
            }

        # Restaurar selección guardada si existe
        if has_saved_selection:
            self.restore_movie_selection(tmdb_id)

        self.update_manual_selection_stats()

    def toggle_quality_selection(self, event):
        """Toggle selección de calidad"""
        item = self.qualities_tree.selection()[0] if self.qualities_tree.selection() else None
        if not item or item not in self.current_selection_data:
            return

        # Toggle selección
        current_selected = self.current_selection_data[item]['selected']
        self.current_selection_data[item]['selected'] = not current_selected

        # Actualizar checkbox visual
        values = list(self.qualities_tree.item(item)['values'])
        values[0] = "☑" if not current_selected else "☐"
        self.qualities_tree.item(item, values=values)

        self.update_manual_selection_stats()
        self.auto_save_current_selection()

    def update_manual_selection_stats(self):
        """Actualizar estadísticas de selección manual"""
        # Verificar si stats_text existe (para selección manual)
        if hasattr(self, 'stats_text'):
            if not self.current_selection_data:
                self.stats_text.delete(1.0, tk.END)
                self.stats_text.insert(1.0, "No hay datos de selección")
                return

            selected_items = [data for data in self.current_selection_data.values() if data['selected']]
            selected_symlinks = [item for item in selected_items if item['copy_data']['movie_symlink'] == 1]
            selected_direct = [item for item in selected_items if item['copy_data']['direct_source'] == 1]

            stats_content = f"✅ Seleccionados para MANTENER: {len(selected_items)} copias\n"
            stats_content += f"🔗 Symlinks: {len(selected_symlinks)} | 🌐 Direct Sources: {len(selected_direct)}\n"
            stats_content += f"🗑️ Se eliminarán: {len(self.current_selection_data) - len(selected_items)} copias"

            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_content)

    def auto_save_current_selection(self):
        """Guardar automáticamente la selección actual"""
        if self.current_tmdb_id and self.current_selection_data:
            self.save_current_movie_selection()
            self.update_saved_selections_counter()

    def update_saved_selections_counter(self):
        """Actualizar el contador de selecciones guardadas"""
        count = len(self.saved_selections)
        self.saved_selections_label.config(text=f"💾 Selecciones guardadas: {count}")

        # Cambiar color según la cantidad
        if count == 0:
            self.saved_selections_label.config(foreground="gray")
        elif count < 5:
            self.saved_selections_label.config(foreground="blue")
        elif count < 10:
            self.saved_selections_label.config(foreground="orange")
        else:
            self.saved_selections_label.config(foreground="green")

    def debug_memory_status(self):
        """Mostrar estado actual de la memoria para debug"""
        debug_info = f"🔍 DEBUG DE MEMORIA\n\n"
        debug_info += f"TMDB actual: {self.current_tmdb_id}\n"
        debug_info += f"Datos actuales: {len(self.current_selection_data) if self.current_selection_data else 0} items\n"
        debug_info += f"Selecciones guardadas: {len(self.saved_selections)}\n"
        debug_info += f"Recomendaciones guardadas: {len(self.movie_recommendations)}\n\n"

        if self.current_selection_data:
            selected_count = sum(1 for data in self.current_selection_data.values() if data['selected'])
            debug_info += f"Selección actual: {selected_count}/{len(self.current_selection_data)}\n\n"

        if self.saved_selections:
            debug_info += "MEMORIA GUARDADA:\n"
            for tmdb_id, selection in self.saved_selections.items():
                selected_count = sum(1 for data in selection.values() if data['selected'])
                debug_info += f"  TMDB {tmdb_id}: {selected_count}/{len(selection)} seleccionadas\n"

        messagebox.showinfo("Debug de Memoria", debug_info)

    def select_all_symlinks_unified(self):
        """Seleccionar todos los symlinks en la interfaz unificada"""
        if not self.current_selection_data:
            return

        for item_id, data in self.current_selection_data.items():
            if data['copy_data']['movie_symlink'] == 1:
                data['selected'] = True
                values = list(self.qualities_tree.item(item_id)['values'])
                values[0] = "☑"
                self.qualities_tree.item(item_id, values=values)

        self.update_manual_selection_stats()
        self.auto_save_current_selection()

    def select_best_quality_unified(self):
        """Seleccionar automáticamente la mejor calidad"""
        if not self.current_selection_data:
            return

        # Deseleccionar todo primero
        for item_id, data in self.current_selection_data.items():
            data['selected'] = False
            values = list(self.qualities_tree.item(item_id)['values'])
            values[0] = "☐"
            self.qualities_tree.item(item_id, values=values)

        # Seleccionar la mejor calidad
        symlinks = [data for data in self.current_selection_data.values() if data['copy_data']['movie_symlink'] == 1]
        if symlinks:
            # Ordenar por prioridad de calidad
            symlinks.sort(key=lambda x: x['copy_data']['quality_priority'])
            best_symlink = symlinks[0]

            # Encontrar el item_id correspondiente
            for item_id, data in self.current_selection_data.items():
                if data['stream_id'] == best_symlink['stream_id']:
                    data['selected'] = True
                    values = list(self.qualities_tree.item(item_id)['values'])
                    values[0] = "☑"
                    self.qualities_tree.item(item_id, values=values)
                    break
        else:
            # Si no hay symlinks, seleccionar el mejor direct source
            direct_sources = [data for data in self.current_selection_data.values() if data['copy_data']['direct_source'] == 1]
            if direct_sources:
                best_direct = max(direct_sources, key=lambda x: x['copy_data']['rating'] or 0)
                for item_id, data in self.current_selection_data.items():
                    if data['stream_id'] == best_direct['stream_id']:
                        data['selected'] = True
                        values = list(self.qualities_tree.item(item_id)['values'])
                        values[0] = "☑"
                        self.qualities_tree.item(item_id, values=values)
                        break

        self.update_manual_selection_stats()
        self.auto_save_current_selection()

    def clear_selection_unified(self):
        """Limpiar toda la selección"""
        if not self.current_selection_data:
            return

        for item_id, data in self.current_selection_data.items():
            data['selected'] = False
            values = list(self.qualities_tree.item(item_id)['values'])
            values[0] = "☐"
            self.qualities_tree.item(item_id, values=values)

        self.update_manual_selection_stats()
        self.auto_save_current_selection()

    def save_current_selection(self):
        """Guardar la selección actual (sin aplicar)"""
        if not self.current_selection_data or not self.current_tmdb_id:
            messagebox.showwarning("Advertencia", "No hay selección para guardar")
            return

        selected_items = [data for data in self.current_selection_data.values() if data['selected']]

        if not selected_items:
            messagebox.showwarning("Advertencia", "Debes seleccionar al menos una copia para guardar")
            return

        # Obtener información de la película actual
        selected_movie = self.movies_tree_manual.selection()
        if selected_movie:
            movie_data = self.movies_tree_manual.item(selected_movie[0])['values']
            title = movie_data[1]

            message = f"💾 SELECCIÓN GUARDADA\n\n"
            message += f"Película: {title}\n"
            message += f"Copias seleccionadas: {len(selected_items)}\n"
            message += f"Copias a eliminar: {len(self.current_selection_data) - len(selected_items)}\n\n"
            message += "La selección se ha guardado temporalmente.\n"
            message += "Usa 'Ejecutar Limpieza' para aplicar los cambios."

            messagebox.showinfo("Selección Guardada", message)

    def execute_unified_cleanup(self):
        """Ejecutar limpieza de la selección actual"""
        if not self.current_selection_data or not self.current_tmdb_id:
            messagebox.showwarning("Advertencia", "No hay selección para ejecutar")
            return

        selected_items = [data for data in self.current_selection_data.values() if data['selected']]

        if not selected_items:
            messagebox.showwarning("Advertencia", "Debes seleccionar al menos una copia para mantener")
            return

        # Obtener información de la película actual
        selected_movie = self.movies_tree_manual.selection()
        if not selected_movie:
            messagebox.showwarning("Advertencia", "No hay película seleccionada")
            return

        movie_data = self.movies_tree_manual.item(selected_movie[0])['values']
        title = movie_data[1]

        keep_stream_ids = [item['stream_id'] for item in selected_items]
        total_selected = len(selected_items)
        total_to_delete = len(self.current_selection_data) - total_selected

        # Confirmación
        message = f"🚀 EJECUTAR LIMPIEZA\n\n"
        message += f"Película: {title}\n"
        message += f"Mantener: {total_selected} copias\n"
        message += f"Eliminar: {total_to_delete} copias\n\n"
        message += "¿Ejecutar esta limpieza?"

        if not messagebox.askyesno("Confirmar Limpieza", message):
            return

        # Aplicar selección
        result = self.db.apply_manual_selection(self.current_tmdb_id, keep_stream_ids)

        if result["deleted"] > 0:
            success_message = f"✅ LIMPIEZA EJECUTADA EXITOSAMENTE\n\n"
            success_message += f"Copias eliminadas: {result['deleted']}\n"
            success_message += f"Copias mantenidas: {result['kept']}\n"
            success_message += f"Total procesado: {result['total_processed']}\n\n"
            success_message += "¡Limpieza aplicada exitosamente!"

            messagebox.showinfo("Éxito", success_message)

            # Remover película de la lista si ya no tiene múltiples symlinks
            self.refresh_movies_list()
            self.clear_right_panel()
        else:
            messagebox.showerror("Error", "No se pudieron aplicar los cambios")

    def execute_all_saved_selections(self):
        """Ejecutar todas las selecciones guardadas"""
        if not self.saved_selections:
            messagebox.showwarning("Advertencia", "No hay selecciones guardadas para ejecutar")
            return

        # Guardar selección actual si existe
        if self.current_tmdb_id and self.current_selection_data:
            self.save_current_movie_selection()

        total_selections = len(self.saved_selections)

        # Confirmación
        message = f"🚀 EJECUTAR TODAS LAS SELECCIONES GUARDADAS\n\n"
        message += f"Se procesarán {total_selections} películas con selecciones personalizadas\n\n"
        message += "Esta acción aplicará todas las selecciones guardadas.\n"
        message += "¿Continuar con la ejecución masiva?"

        if not messagebox.askyesno("Confirmar Ejecución Masiva", message):
            return

        # Crear ventana de progreso
        progress_dialog = tk.Toplevel(self.root)
        progress_dialog.title("Ejecutando Selecciones Guardadas")
        progress_dialog.geometry("500x300")
        progress_dialog.transient(self.root)
        progress_dialog.grab_set()

        # Variables de progreso
        current_movie = tk.IntVar(value=0)
        total_deleted = tk.IntVar(value=0)
        total_kept = tk.IntVar(value=0)
        total_errors = tk.IntVar(value=0)

        # Widgets de progreso
        ttk.Label(progress_dialog, text="🚀 Ejecutando Selecciones Guardadas...", font=('Arial', 12, 'bold')).pack(pady=10)

        progress_bar = ttk.Progressbar(progress_dialog, length=400, mode='determinate')
        progress_bar.pack(padx=20, pady=10)

        status_label = ttk.Label(progress_dialog, text="Iniciando...", font=('Arial', 10))
        status_label.pack(pady=5)

        stats_text = tk.Text(progress_dialog, height=8, width=60)
        stats_text.pack(padx=20, pady=10)

        def update_progress():
            current = current_movie.get()
            progress_bar['value'] = (current / total_selections) * 100
            status_label.config(text=f"Procesando película {current}/{total_selections}")

            stats_content = f"📊 PROGRESO DE EJECUCIÓN:\n"
            stats_content += f"Películas procesadas: {current}/{total_selections}\n"
            stats_content += f"Copias eliminadas: {total_deleted.get()}\n"
            stats_content += f"Copias mantenidas: {total_kept.get()}\n"
            stats_content += f"Errores: {total_errors.get()}\n"

            stats_text.delete(1.0, tk.END)
            stats_text.insert(1.0, stats_content)
            progress_dialog.update()

        def process_all_selections():
            try:
                for i, (tmdb_id, selection_data) in enumerate(self.saved_selections.items(), 1):
                    current_movie.set(i)

                    # Obtener información de la película
                    movie_info = None
                    for item in self.movies_tree_manual.get_children():
                        item_data = self.movies_tree_manual.item(item)['values']
                        if int(item_data[0]) == tmdb_id:
                            movie_info = item_data
                            break

                    title = movie_info[1] if movie_info else f"TMDB {tmdb_id}"
                    status_label.config(text=f"Procesando: {title}")
                    update_progress()

                    # Obtener IDs a mantener
                    keep_stream_ids = [data['stream_id'] for data in selection_data.values() if data['selected']]

                    if keep_stream_ids:
                        # Aplicar selección
                        result = self.db.apply_manual_selection(tmdb_id, keep_stream_ids)

                        total_deleted.set(total_deleted.get() + result['deleted'])
                        total_kept.set(total_kept.get() + result['kept'])

                        if result['deleted'] == 0 and len([data for data in selection_data.values() if not data['selected']]) > 0:
                            total_errors.set(total_errors.get() + 1)
                    else:
                        total_errors.set(total_errors.get() + 1)

                    update_progress()

                # Completado
                status_label.config(text="✅ Todas las selecciones ejecutadas exitosamente!", foreground="green")
                progress_bar['value'] = 100
                update_progress()

                # Limpiar selecciones guardadas
                self.saved_selections = {}
                self.update_saved_selections_counter()

                # Botón para cerrar
                ttk.Button(progress_dialog, text="✅ Cerrar",
                          command=lambda: self.finish_mass_execution(progress_dialog, total_deleted.get(), total_kept.get(), total_errors.get())).pack(pady=10)

            except Exception as e:
                messagebox.showerror("Error", f"Error durante la ejecución masiva: {str(e)}")
                progress_dialog.destroy()

        # Iniciar procesamiento
        threading.Thread(target=process_all_selections, daemon=True).start()

    def finish_mass_execution(self, dialog, deleted, kept, errors):
        """Finalizar ejecución masiva"""
        dialog.destroy()

        # Mostrar resultados
        result_message = f"✅ EJECUCIÓN MASIVA COMPLETADA\n\n"
        result_message += f"Copias eliminadas: {deleted}\n"
        result_message += f"Copias mantenidas: {kept}\n"

        if errors > 0:
            result_message += f"Errores: {errors}\n"

        result_message += f"\n¡Todas las selecciones guardadas han sido aplicadas!"

        messagebox.showinfo("Ejecución Completada", result_message)

        # Refrescar listas
        self.refresh_movies_list()
        self.clear_right_panel()

    def refresh_movies_list(self):
        """Refrescar la lista de películas con múltiples symlinks"""
        # Limpiar lista actual
        for item in self.movies_tree_manual.get_children():
            self.movies_tree_manual.delete(item)

        # Recargar datos
        movies_with_multiple_symlinks = self.db.get_movies_with_multiple_symlinks()

        for movie in movies_with_multiple_symlinks:
            titles = movie['all_titles'].split('|||')
            qualities = set()
            for title in titles:
                if '4k' in title.lower() or '4K' in title:
                    qualities.add('4K')
                elif 'fhd' in title.lower():
                    qualities.add('FHD')
                elif 'hd' in title.lower():
                    qualities.add('HD')
                elif 'sd' in title.lower():
                    qualities.add('SD')
                elif '60fps' in title.lower():
                    qualities.add('60FPS')
                elif 'extendida' in title.lower() or 'extended' in title.lower():
                    qualities.add('EXTENDED')
                else:
                    qualities.add('STANDARD')

            qualities_str = ', '.join(sorted(qualities))

            self.movies_tree_manual.insert("", "end", values=(
                movie['tmdb_id'],
                movie['sample_title'],
                movie['symlink_count'],
                qualities_str
            ))

    def finish_unified_selection(self, dialog):
        """Finalizar selección unificada y cerrar ventana"""
        # Refrescar vistas principales
        self.load_priority_duplicates()

        # Cerrar ventana
        dialog.destroy()

        # Mostrar mensaje final
        messagebox.showinfo("✅ Selección Manual Completada",
                          "Workspace de selección manual cerrado.\n\n"
                          "Las vistas principales se han actualizado.\n"
                          "¡Tu base de datos está optimizada!")

    def open_detailed_manual_selection(self, tmdb_id: int, title: str):
        """Abrir ventana detallada para selección manual de symlinks"""
        # Obtener recomendaciones y detalles
        recommendations = self.db.get_manual_selection_recommendations(tmdb_id)

        if not recommendations["symlinks"] and not recommendations["direct_sources"]:
            messagebox.showerror("Error", "No se encontraron copias para esta película")
            return

        # Crear ventana de selección detallada
        detail_dialog = tk.Toplevel(self.root)
        detail_dialog.title(f"Selección Manual: {title}")
        detail_dialog.geometry("1000x700")
        detail_dialog.transient(self.root)
        detail_dialog.grab_set()

        # Información de la película
        movie_info_frame = ttk.LabelFrame(detail_dialog, text="Información de la Película")
        movie_info_frame.pack(fill="x", padx=10, pady=10)

        info_text = f"TMDB ID: {tmdb_id} | Título: {title}\n"
        info_text += f"Total de copias: {recommendations['total_copies']}\n"
        info_text += f"Symlinks: {len(recommendations['symlinks'])} | Direct Sources: {len(recommendations['direct_sources'])}"

        ttk.Label(movie_info_frame, text=info_text, font=('Arial', 10)).pack(padx=10, pady=5)

        # Recomendaciones
        rec_frame = ttk.LabelFrame(detail_dialog, text="Recomendaciones Inteligentes")
        rec_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(rec_frame, text=recommendations["recommendations"], font=('Arial', 9), foreground="blue").pack(padx=10, pady=5)

        # Tabla de selección con checkboxes
        selection_frame = ttk.LabelFrame(detail_dialog, text="Seleccionar Copias a Mantener")
        selection_frame.pack(fill="both", expand=True, padx=10, pady=10)

        columns = ("Sel", "ID", "Título", "Tipo", "Calidad", "Rating", "Año", "Container")
        selection_tree = ttk.Treeview(selection_frame, columns=columns, show="headings", height=15)

        for col in columns:
            selection_tree.heading(col, text=col)
            if col == "Título":
                selection_tree.column(col, width=300)
            elif col == "Sel":
                selection_tree.column(col, width=40)
            elif col == "Tipo":
                selection_tree.column(col, width=150)
            else:
                selection_tree.column(col, width=80)

        # Variable para tracking de selección
        selection_data = {}

        # Cargar symlinks primero (prioridad alta)
        all_copies = recommendations["symlinks"] + recommendations["direct_sources"] + recommendations["others"]

        for copy in all_copies:
            # Determinar selección inicial inteligente
            initial_selected = False
            if copy['movie_symlink'] == 1:  # Symlinks
                if len(recommendations["symlinks"]) == 1:
                    initial_selected = True  # Auto-seleccionar si es el único symlink
                elif copy['detected_quality'] in ['4K', 'FHD']:
                    initial_selected = True  # Auto-seleccionar calidades altas
            elif len(recommendations["symlinks"]) == 0 and copy == recommendations["direct_sources"][0]:
                initial_selected = True  # Auto-seleccionar primer direct source si no hay symlinks

            checkbox = "☑" if initial_selected else "☐"

            item_id = selection_tree.insert("", "end", values=(
                checkbox,
                copy['stream_id'],
                copy['title'],
                copy['source_type'],
                copy['detected_quality'],
                f"{copy['rating']:.1f}" if copy['rating'] else "N/A",
                copy['year'] if copy['year'] else "N/A",
                copy['target_container'] if copy['target_container'] else "N/A"
            ))

            selection_data[item_id] = {
                'selected': initial_selected,
                'stream_id': copy['stream_id'],
                'copy_data': copy
            }

        scrollbar_selection = ttk.Scrollbar(selection_frame, orient="vertical", command=selection_tree.yview)
        selection_tree.configure(yscrollcommand=scrollbar_selection.set)

        selection_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        scrollbar_selection.pack(side="right", fill="y", padx=(0, 10), pady=10)

        # Función para toggle de selección
        def toggle_selection(event):
            item = selection_tree.selection()[0] if selection_tree.selection() else None
            if not item or item not in selection_data:
                return

            # Toggle selección
            current_selected = selection_data[item]['selected']
            selection_data[item]['selected'] = not current_selected

            # Actualizar checkbox visual
            values = list(selection_tree.item(item)['values'])
            values[0] = "☑" if not current_selected else "☐"
            selection_tree.item(item, values=values)

            update_selection_stats()

        selection_tree.bind("<Double-1>", toggle_selection)

        # Estadísticas de selección
        stats_frame = ttk.LabelFrame(detail_dialog, text="Estadísticas de Selección")
        stats_frame.pack(fill="x", padx=10, pady=5)

        stats_text = tk.Text(stats_frame, height=3, width=80)
        stats_text.pack(padx=10, pady=5)

        def update_selection_stats():
            selected_items = [data for data in selection_data.values() if data['selected']]
            selected_symlinks = [item for item in selected_items if item['copy_data']['movie_symlink'] == 1]
            selected_direct = [item for item in selected_items if item['copy_data']['direct_source'] == 1]

            stats_content = f"Seleccionados para MANTENER: {len(selected_items)} copias\n"
            stats_content += f"Symlinks: {len(selected_symlinks)} | Direct Sources: {len(selected_direct)}\n"
            stats_content += f"Se eliminarán: {len(selection_data) - len(selected_items)} copias"

            stats_text.delete(1.0, tk.END)
            stats_text.insert(1.0, stats_content)

        # Botones de acción
        action_frame = ttk.Frame(detail_dialog)
        action_frame.pack(fill="x", padx=10, pady=10)

        def select_all_symlinks():
            """Seleccionar todos los symlinks"""
            for item_id, data in selection_data.items():
                if data['copy_data']['movie_symlink'] == 1:
                    data['selected'] = True
                    values = list(selection_tree.item(item_id)['values'])
                    values[0] = "☑"
                    selection_tree.item(item_id, values=values)
            update_selection_stats()

        def select_best_quality():
            """Seleccionar automáticamente las mejores calidades"""
            # Deseleccionar todo primero
            for item_id, data in selection_data.items():
                data['selected'] = False
                values = list(selection_tree.item(item_id)['values'])
                values[0] = "☐"
                selection_tree.item(item_id, values=values)

            # Seleccionar la mejor calidad de cada tipo
            symlinks = [data for data in selection_data.values() if data['copy_data']['movie_symlink'] == 1]
            if symlinks:
                # Ordenar por prioridad de calidad
                symlinks.sort(key=lambda x: x['copy_data']['quality_priority'])
                best_symlink = symlinks[0]

                # Encontrar el item_id correspondiente
                for item_id, data in selection_data.items():
                    if data['stream_id'] == best_symlink['stream_id']:
                        data['selected'] = True
                        values = list(selection_tree.item(item_id)['values'])
                        values[0] = "☑"
                        selection_tree.item(item_id, values=values)
                        break
            else:
                # Si no hay symlinks, seleccionar el mejor direct source
                direct_sources = [data for data in selection_data.values() if data['copy_data']['direct_source'] == 1]
                if direct_sources:
                    best_direct = max(direct_sources, key=lambda x: x['copy_data']['rating'] or 0)
                    for item_id, data in selection_data.items():
                        if data['stream_id'] == best_direct['stream_id']:
                            data['selected'] = True
                            values = list(selection_tree.item(item_id)['values'])
                            values[0] = "☑"
                            selection_tree.item(item_id, values=values)
                            break

            update_selection_stats()

        def apply_manual_selection():
            """Aplicar la selección manual"""
            selected_items = [data for data in selection_data.values() if data['selected']]

            if not selected_items:
                messagebox.showwarning("Advertencia", "Debes seleccionar al menos una copia para mantener")
                return

            keep_stream_ids = [item['stream_id'] for item in selected_items]
            total_selected = len(selected_items)
            total_to_delete = len(selection_data) - total_selected

            # Confirmación
            message = f"APLICAR SELECCIÓN MANUAL\n\n"
            message += f"Película: {title}\n"
            message += f"Mantener: {total_selected} copias\n"
            message += f"Eliminar: {total_to_delete} copias\n\n"
            message += "¿Aplicar esta selección?"

            if not messagebox.askyesno("Confirmar Selección Manual", message):
                return

            # Aplicar selección
            result = self.db.apply_manual_selection(tmdb_id, keep_stream_ids)

            if result["deleted"] > 0:
                success_message = f"✅ SELECCIÓN MANUAL APLICADA\n\n"
                success_message += f"Copias eliminadas: {result['deleted']}\n"
                success_message += f"Copias mantenidas: {result['kept']}\n"
                success_message += f"Total procesado: {result['total_processed']}\n\n"
                success_message += "¡Selección aplicada exitosamente!"

                messagebox.showinfo("Éxito", success_message)
                detail_dialog.destroy()

                # Refrescar vistas
                self.load_priority_duplicates()
            else:
                messagebox.showerror("Error", "No se pudieron aplicar los cambios")

        ttk.Button(action_frame, text="Seleccionar Todos los Symlinks", command=select_all_symlinks).pack(side="left", padx=(0, 10))
        ttk.Button(action_frame, text="Seleccionar Mejor Calidad", command=select_best_quality).pack(side="left", padx=(0, 10))
        ttk.Button(action_frame, text="APLICAR SELECCIÓN", command=apply_manual_selection, style="Accent.TButton").pack(side="left", padx=(0, 10))
        ttk.Button(action_frame, text="Cancelar", command=detail_dialog.destroy).pack(side="left")

        # Actualizar estadísticas iniciales
        update_selection_stats()

    def load_all_duplicates_for_mass(self):
        """Cargar todos los duplicados para limpieza masiva"""
        if not self.check_connection():
            return

        # Limpiar lista actual
        for item in self.mass_tree.get_children():
            self.mass_tree.delete(item)

        duplicates = self.db.get_duplicate_movies_with_priority()

        # Variable para tracking de selección
        self.mass_selection_data = {}

        for dup in duplicates:
            tmdb_id = dup.get('tmdb_id')

            # Obtener recomendaciones
            recommendations = self.db.get_recommended_deletions(tmdb_id)

            to_keep = len(recommendations['keep'])
            to_delete = len(recommendations['delete'])
            total = recommendations['total_copies']
            reduction = f"{(to_delete/total*100):.1f}%" if total > 0 else "0%"

            # Insertar en el tree
            item_id = self.mass_tree.insert("", "end", values=(
                "☐",  # Checkbox no seleccionado
                tmdb_id,
                dup.get('sample_title', ''),
                total,
                to_keep,
                to_delete,
                reduction
            ))

            # Guardar datos para la selección
            self.mass_selection_data[item_id] = {
                'selected': False,
                'tmdb_id': tmdb_id,
                'title': dup.get('sample_title', ''),
                'recommendations': recommendations
            }

        if duplicates:
            messagebox.showinfo("Resultado", f"Se cargaron {len(duplicates)} grupos de duplicados para limpieza masiva")
            self.update_mass_stats()

    def toggle_mass_selection(self, event):
        """Toggle selección de un elemento en limpieza masiva"""
        item = self.mass_tree.selection()[0] if self.mass_tree.selection() else None
        if not item or item not in self.mass_selection_data:
            return

        # Toggle selección
        current_selected = self.mass_selection_data[item]['selected']
        self.mass_selection_data[item]['selected'] = not current_selected

        # Actualizar checkbox visual
        values = list(self.mass_tree.item(item)['values'])
        values[0] = "☑" if not current_selected else "☐"
        self.mass_tree.item(item, values=values)

        self.update_mass_stats()

    def select_all_mass_duplicates(self):
        """Seleccionar todos los duplicados para limpieza masiva"""
        for item_id in self.mass_selection_data:
            self.mass_selection_data[item_id]['selected'] = True
            values = list(self.mass_tree.item(item_id)['values'])
            values[0] = "☑"
            self.mass_tree.item(item_id, values=values)

        self.update_mass_stats()

    def deselect_all_mass_duplicates(self):
        """Deseleccionar todos los duplicados"""
        for item_id in self.mass_selection_data:
            self.mass_selection_data[item_id]['selected'] = False
            values = list(self.mass_tree.item(item_id)['values'])
            values[0] = "☐"
            self.mass_tree.item(item_id, values=values)

        self.update_mass_stats()

    def update_mass_stats(self):
        """Actualizar estadísticas de selección masiva"""
        if not hasattr(self, 'mass_selection_data'):
            return

        selected_items = [data for data in self.mass_selection_data.values() if data['selected']]

        total_groups = len(selected_items)
        total_to_keep = sum(len(item['recommendations']['keep']) for item in selected_items)
        total_to_delete = sum(len(item['recommendations']['delete']) for item in selected_items)
        total_copies = total_to_keep + total_to_delete

        stats_text = f"Grupos seleccionados: {total_groups}\n"
        stats_text += f"Copias a mantener: {total_to_keep}\n"
        stats_text += f"Copias a eliminar: {total_to_delete}\n"

        if total_copies > 0:
            reduction = (total_to_delete / total_copies) * 100
            stats_text += f"Reducción: {reduction:.1f}%"

        self.mass_stats_text.delete(1.0, tk.END)
        self.mass_stats_text.insert(1.0, stats_text)

    def preview_mass_cleanup(self):
        """Vista previa de la limpieza masiva"""
        if not hasattr(self, 'mass_selection_data'):
            messagebox.showwarning("Advertencia", "Primero carga los duplicados")
            return

        selected_items = [data for data in self.mass_selection_data.values() if data['selected']]

        if not selected_items:
            messagebox.showwarning("Advertencia", "No hay elementos seleccionados")
            return

        # Crear ventana de vista previa
        preview_dialog = tk.Toplevel(self.root)
        preview_dialog.title("Vista Previa de Limpieza Masiva")
        preview_dialog.geometry("700x500")
        preview_dialog.transient(self.root)
        preview_dialog.grab_set()

        # Resumen general
        total_to_delete = sum(len(item['recommendations']['delete']) for item in selected_items)
        total_to_keep = sum(len(item['recommendations']['keep']) for item in selected_items)

        summary_frame = ttk.LabelFrame(preview_dialog, text="Resumen de Limpieza")
        summary_frame.pack(fill="x", padx=10, pady=10)

        summary_text = f"Grupos seleccionados: {len(selected_items)}\n"
        summary_text += f"Copias a mantener: {total_to_keep}\n"
        summary_text += f"Copias a eliminar: {total_to_delete}"

        ttk.Label(summary_frame, text=summary_text, font=('Arial', 11)).pack(padx=10, pady=10)

        # Lista detallada
        details_frame = ttk.LabelFrame(preview_dialog, text="Detalles por Grupo")
        details_frame.pack(fill="both", expand=True, padx=10, pady=10)

        details_text = tk.Text(details_frame, height=15, width=80)
        scrollbar_preview = ttk.Scrollbar(details_frame, orient="vertical", command=details_text.yview)
        details_text.configure(yscrollcommand=scrollbar_preview.set)

        details_content = ""
        for i, item in enumerate(selected_items[:20], 1):  # Mostrar solo los primeros 20
            rec = item['recommendations']
            details_content += f"{i}. {item['title']}\n"
            details_content += f"   Mantener: {len(rec['keep'])} | Eliminar: {len(rec['delete'])}\n"
            details_content += f"   Razón: {rec['reason']}\n\n"

        if len(selected_items) > 20:
            details_content += f"... y {len(selected_items) - 20} grupos más\n"

        details_text.insert(1.0, details_content)
        details_text.config(state="disabled")

        details_text.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        scrollbar_preview.pack(side="right", fill="y", padx=(0, 10), pady=10)

        ttk.Button(preview_dialog, text="Cerrar", command=preview_dialog.destroy).pack(pady=10)

    def execute_mass_cleanup(self):
        """Ejecutar limpieza masiva"""
        if not hasattr(self, 'mass_selection_data'):
            messagebox.showwarning("Advertencia", "Primero carga los duplicados")
            return

        selected_items = [data for data in self.mass_selection_data.values() if data['selected']]

        if not selected_items:
            messagebox.showwarning("Advertencia", "No hay elementos seleccionados")
            return

        total_to_delete = sum(len(item['recommendations']['delete']) for item in selected_items)

        if total_to_delete == 0:
            messagebox.showinfo("Información", "No hay duplicados para eliminar en los elementos seleccionados")
            return

        # Confirmación final
        message = f"EJECUTAR LIMPIEZA MASIVA\n\n"
        message += f"Se procesarán {len(selected_items)} grupos de duplicados\n"
        message += f"Se eliminarán {total_to_delete} copias duplicadas\n\n"
        message += "Esta acción NO se puede deshacer.\n\n"
        message += "¿Continuar con la limpieza masiva?"

        if not messagebox.askyesno("Confirmar Limpieza Masiva", message):
            return

        # Ejecutar limpieza
        tmdb_ids = [item['tmdb_id'] for item in selected_items]
        self.execute_mass_cleanup_process(tmdb_ids)

    def execute_mass_cleanup_process(self, tmdb_ids):
        """Proceso de ejecución de limpieza masiva"""
        # Usar la función existente de limpieza masiva
        selected_groups = []
        for tmdb_id in tmdb_ids:
            # Buscar el item en mass_selection_data
            for item_id, data in self.mass_selection_data.items():
                if data['tmdb_id'] == tmdb_id and data['selected']:
                    selected_groups.append({
                        'tmdb_id': tmdb_id,
                        'title': data['title'],
                        'recommendations': data['recommendations'],
                        'item': item_id
                    })
                    break

        self.apply_mass_cleanup(selected_groups)

    def export_cleanup_report(self):
        """Exportar reporte de limpieza"""
        messagebox.showinfo("Información", "Función de exportar reporte en desarrollo.\n\nPróximamente podrás exportar reportes detallados de la limpieza.")

    def run(self):
        """Ejecutar la aplicación"""
        self.root.mainloop()

def main():
    app = XUIManagerGUI()
    app.run()

if __name__ == "__main__":
    main()
