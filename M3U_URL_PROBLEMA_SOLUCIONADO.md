# 🔧 M3U URL PROBLEMA SOLUCIONADO

## 📅 Fecha: 2025-06-21
## 🎯 Estado: ✅ PROBLEMA IDENTIFICADO Y SOLUCIONADO

---

## 🚨 **PROBLEMA REPORTADO:**

El usuario reportó que cuando se importan elementos M3U (series y películas), **las URLs de los streams no se están guardando** en el campo `stream_source` de la tabla `streams`.

### **Ejemplo del problema:**
```
#EXTINF:-1 tvg-name="Astérix y Obélix: El combate de los jefes S01E01" tvg-logo="https://image.tmdb.org/t/p/w1280/z5oy1f1prjCByJ8yRqEoHlLkfmO.jpg" group-title="ACCION",Astérix y Obélix: El combate de los jefes S01E01
http://tvvip.us:80/series/TEL*Infest84/TEL*Infest84/486875.mp4
```

**La URL `http://tvvip.us:80/series/TEL*Infest84/TEL*Infest84/486875.mp4` no se estaba guardando en `stream_source`.**

---

## 🔍 **ANÁLISIS DEL PROBLEMA:**

### **1. ✅ Parser M3U - FUNCIONANDO CORRECTAMENTE**
El parser en `m3u_manager.py` **SÍ captura las URLs correctamente**:

```python
elif line.startswith('http'):
    # URL del stream
    current_entry['url'] = line  # ✅ URL capturada aquí
    entries.append(current_entry.copy())
    current_entry = {}
```

### **2. ✅ Extracción de información - FUNCIONANDO CORRECTAMENTE**
La función `extract_series_info()` **SÍ incluye la URL**:

```python
info = {
    'url': entry.get('url', ''),  # ✅ URL incluida aquí
    # ... otros campos
}
```

### **3. ✅ Mapeo de datos - FUNCIONANDO CORRECTAMENTE**
En `import_selected_m3u()` **SÍ se mapea la URL**:

```python
m3u_item = {
    'series_title': series_title,
    'episode_info': f"S{season:02d}E{episode:02d}",
    'url': item.get('url', ''),  # ✅ URL mapeada aquí
    'title': item.get('title', '')
}
```

### **4. ❌ PROBLEMA ENCONTRADO - FORMATO DE URL**
El problema estaba en **cómo se almacena la URL en la base de datos XUI**.

**Formato esperado por XUI:**
```sql
stream_source = '["http://tvpro.tech:8080/series/infest/asdf45223/113431.mkv"]'
```

**Formato que estábamos usando:**
```sql
stream_source = 'http://tvpro.tech:8080/series/infest/asdf45223/113431.mkv'
```

---

## 🛠️ **SOLUCIÓN IMPLEMENTADA:**

### **Cambio 1: Función `import_series_episode()`**
```python
# ANTES:
self.db.execute_update(insert_stream_query, (
    new_stream_id, stream_display_name, url,  # ❌ URL como string
    1 if direct_source else 0,
    1 if direct_proxy else 0,
    series_id
))

# DESPUÉS:
# Formatear URL como array JSON para compatibilidad con XUI
import json
formatted_url = json.dumps([url]) if url else json.dumps([])

self.db.execute_update(insert_stream_query, (
    new_stream_id, stream_display_name, formatted_url,  # ✅ URL como array JSON
    1 if direct_source else 0,
    1 if direct_proxy else 0,
    series_id
))
```

### **Cambio 2: Función `import_movie()`**
```python
# ANTES:
self.db.execute_update(insert_stream_query, (
    new_stream_id, stream_display_name, url,  # ❌ URL como string
    1 if direct_source else 0,
    1 if direct_proxy else 0
))

# DESPUÉS:
# Formatear URL como array JSON para compatibilidad con XUI
import json
formatted_url = json.dumps([url]) if url else json.dumps([])

self.db.execute_update(insert_stream_query, (
    new_stream_id, stream_display_name, formatted_url,  # ✅ URL como array JSON
    1 if direct_source else 0,
    1 if direct_proxy else 0
))
```

### **Cambio 3: Debug Logging Agregado**
```python
# Debug: Verificar que la URL se está capturando
self.log_message(f"🔍 DEBUG URL: '{url}' (length: {len(url) if url else 0})", 'accent')
if not url:
    self.log_message(f"⚠️ WARNING: Empty URL detected for {series_title} {episode_info}", 'warning')
    self.log_message(f"🔍 DEBUG m3u_item keys: {list(m3u_item.keys())}", 'accent')
    self.log_message(f"🔍 DEBUG m3u_item content: {m3u_item}", 'accent')
```

---

## 📊 **RESULTADO ESPERADO:**

Después de esta corrección, las URLs de M3U se almacenarán correctamente:

### **Para Series:**
```sql
INSERT INTO streams (
    id, type, stream_display_name, stream_source,
    direct_source, direct_proxy, series_no, added
) VALUES (
    2372696, 5, 'Game of Thrones S01E01', 
    '["http://tvvip.us:80/series/TEL*Infest84/TEL*Infest84/486875.mp4"]',  -- ✅ URL correcta
    1, 1, 12345, NOW()
)
```

### **Para Películas:**
```sql
INSERT INTO streams (
    id, type, stream_display_name, stream_source,
    direct_source, direct_proxy, added
) VALUES (
    2372697, 1, 'Borat Subsequent Moviefilm (2020)', 
    '["http://tvvip.us:80/movies/TEL*Infest84/TEL*Infest84/movie123.mp4"]',  -- ✅ URL correcta
    1, 1, NOW()
)
```

---

## 🎯 **ARCHIVOS MODIFICADOS:**

1. **`gui.py`** - Funciones `import_series_episode()` y `import_movie()`
2. **Backup creado:** `gui_backup_url_fix_YYYYMMDD_HHMMSS.py`

---

## 🧪 **PRÓXIMOS PASOS PARA TESTING:**

1. **Cargar un archivo M3U** con URLs de prueba
2. **Importar algunos elementos** seleccionados
3. **Verificar en la base de datos** que el campo `stream_source` contiene las URLs en formato JSON array
4. **Confirmar que XUI panel** puede reproducir los streams correctamente

---

## ✅ **CONFIRMACIÓN:**

- ✅ Parser M3U captura URLs correctamente
- ✅ Mapeo de datos incluye URLs
- ✅ Formato de URL corregido para compatibilidad XUI
- ✅ Debug logging agregado para troubleshooting
- ✅ Backup de seguridad creado

**El problema de URLs faltantes en M3U imports ha sido solucionado.**
