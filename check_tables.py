#!/usr/bin/env python3
"""
Script para verificar qué tablas existen en la base de datos XUI
"""

import sys
from database import DatabaseManager

def check_tables():
    """Verificar qué tablas existen en la base de datos"""
    print("=== Verificación de Tablas XUI ===\n")
    
    # Solicitar datos de conexión
    host = input("Host (IP del servidor): ").strip()
    user = input("Usuario: ").strip()
    password = input("Contraseña: ").strip()
    database = input("Base de datos [xui]: ").strip() or "xui"
    port_str = input("Puerto [3306]: ").strip() or "3306"
    
    try:
        port = int(port_str)
    except ValueError:
        print("Error: El puerto debe ser un número")
        return False
    
    # Crear instancia del gestor de base de datos
    db = DatabaseManager()
    
    print(f"\nConectando a {host}:{port}...")
    
    # Intentar conexión
    if db.connect(host, user, password, database, port):
        print("✓ Conexión exitosa!")
        
        # Obtener todas las tablas
        print("\n=== TODAS LAS TABLAS EN LA BASE DE DATOS ===")
        tables = db.execute_query("SHOW TABLES")
        if tables:
            print(f"Se encontraron {len(tables)} tablas:")
            table_names = []
            for i, table in enumerate(tables, 1):
                table_name = table[f'Tables_in_{database}']
                table_names.append(table_name)
                print(f"{i:2d}. {table_name}")
            
            print("\n=== BUSCANDO TABLAS RELACIONADAS CON SERIES/STREAMS ===")
            series_related = [t for t in table_names if 'series' in t.lower() or 'stream' in t.lower()]
            if series_related:
                print("Tablas relacionadas con series/streams:")
                for table in series_related:
                    print(f"  - {table}")
                    
                    # Mostrar estructura de cada tabla
                    print(f"    Estructura de {table}:")
                    columns = db.execute_query(f"DESCRIBE {table}")
                    if columns:
                        for col in columns:
                            print(f"      {col['Field']} ({col['Type']})")
                    print()
            else:
                print("No se encontraron tablas relacionadas con series/streams")
            
            print("\n=== TABLAS QUE BUSCA LA APLICACIÓN ===")
            expected_tables = ['streams_series', 'streams_episodes', 'streams', 'streams_type']
            for expected in expected_tables:
                if expected in table_names:
                    print(f"✓ {expected} - EXISTE")
                else:
                    print(f"✗ {expected} - NO EXISTE")
                    # Buscar tablas similares
                    similar = [t for t in table_names if expected.replace('_', '') in t.replace('_', '')]
                    if similar:
                        print(f"    Tablas similares: {', '.join(similar)}")
        
        db.disconnect()
        return True
        
    else:
        print("✗ Error de conexión")
        return False

def main():
    """Función principal"""
    try:
        check_tables()
        input("\nPresiona Enter para salir...")
        
    except KeyboardInterrupt:
        print("\n\nVerificación cancelada por el usuario")
    except Exception as e:
        print(f"\nError inesperado: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
