#!/usr/bin/env python3
"""
Debug script to investigate why only 1 episode copy is shown instead of all
"""

from database import DatabaseManager

def debug_episode_copies():
    """Debug episode copies issue"""
    print("🔍 DEBUG: Episode Copies Issue")
    print("=" * 50)
    
    # Database connection parameters
    host = "*************"
    user = "root"
    password = "mru5"
    database = "xui"
    port = 3306
    
    db = DatabaseManager()
    
    if db.connect(host, user, password, database, port):
        print("✅ Database connected successfully")
        
        try:
            # Step 1: Get duplicate episodes
            print("\n1. Getting duplicate episodes...")
            duplicates = db.get_duplicate_episodes()
            print(f"   Found {len(duplicates)} duplicate episode groups")
            
            if duplicates:
                # Focus on Avatar example
                avatar_episode = None
                for dup in duplicates:
                    if 'Avatar' in str(dup.get('series_title', '')):
                        avatar_episode = dup
                        break
                
                if not avatar_episode:
                    # Use first duplicate as example
                    avatar_episode = duplicates[0]
                
                series_id = avatar_episode['series_id']
                season_num = avatar_episode['season_num']
                episode_num = avatar_episode['episode_num']
                series_title = avatar_episode['series_title']
                duplicate_count = avatar_episode['duplicate_count']
                episode_ids = avatar_episode['episode_ids']
                
                print(f"\n2. Example Episode Details:")
                print(f"   Series: {series_title}")
                print(f"   Episode: S{season_num:02d}E{episode_num:02d}")
                print(f"   Series ID: {series_id}")
                print(f"   Expected Duplicates: {duplicate_count}")
                print(f"   Episode IDs: {episode_ids}")
                
                # Step 2: Test the get_episode_copies_details function
                print(f"\n3. Testing get_episode_copies_details()...")
                copies = db.get_episode_copies_details(series_id, season_num, episode_num)
                
                print(f"   Query returned {len(copies)} copies")
                print(f"   Expected: {duplicate_count} copies")
                
                if len(copies) != duplicate_count:
                    print(f"   ⚠️ MISMATCH: Expected {duplicate_count}, got {len(copies)}")
                else:
                    print(f"   ✅ MATCH: Got expected number of copies")
                
                # Step 3: Show detailed copy information
                if copies:
                    print(f"\n4. Detailed Copy Information:")
                    for i, copy in enumerate(copies):
                        print(f"     Copy {i+1}:")
                        print(f"       Episode ID: {copy['episode_id']}")
                        print(f"       Stream ID: {copy['stream_id']}")
                        print(f"       Title: {copy['title'][:50]}...")
                        print(f"       Symlink: {copy['movie_symlink']}")
                        print(f"       Direct Source: {copy['direct_source']}")
                        print(f"       Direct Proxy: {copy['direct_proxy']}")
                        print(f"       Quality: {copy['detected_quality']}")
                        print(f"       Priority: {copy['priority_type']}")
                        print("")
                else:
                    print(f"   ❌ No copies returned from query")
                
                # Step 4: Manual SQL query to verify
                print(f"\n5. Manual SQL verification...")
                manual_query = """
                SELECT
                    se.id as episode_id,
                    se.stream_id,
                    s.stream_display_name as title,
                    s.movie_symlink,
                    s.direct_source,
                    s.direct_proxy
                FROM streams_episodes se
                JOIN streams s ON se.stream_id = s.id
                WHERE se.series_id = %s
                AND se.season_num = %s
                AND se.episode_num = %s
                """
                
                manual_result = db.execute_query(manual_query, (series_id, season_num, episode_num))
                print(f"   Manual query returned {len(manual_result)} rows")
                
                if manual_result:
                    print(f"   Manual query results:")
                    for i, row in enumerate(manual_result):
                        print(f"     Row {i+1}: Episode ID {row['episode_id']}, Stream ID {row['stream_id']}")
                        print(f"              Title: {row['title'][:40]}...")
                        print(f"              Symlink: {row['movie_symlink']}")
                
                # Step 5: Check if there's a data issue
                print(f"\n6. Data consistency check...")
                episode_ids_list = episode_ids.split(',')
                print(f"   Episode IDs from duplicate query: {episode_ids_list}")
                print(f"   Episode IDs from copies query: {[str(c['episode_id']) for c in copies]}")
                
                missing_ids = []
                for eid in episode_ids_list:
                    eid = eid.strip()
                    if not any(str(c['episode_id']) == eid for c in copies):
                        missing_ids.append(eid)
                
                if missing_ids:
                    print(f"   ⚠️ Missing episode IDs in copies query: {missing_ids}")
                    
                    # Check why these IDs are missing
                    for missing_id in missing_ids:
                        check_query = """
                        SELECT se.id, se.stream_id, s.id as stream_exists
                        FROM streams_episodes se
                        LEFT JOIN streams s ON se.stream_id = s.id
                        WHERE se.id = %s
                        """
                        check_result = db.execute_query(check_query, (missing_id,))
                        if check_result:
                            row = check_result[0]
                            if row['stream_exists'] is None:
                                print(f"     Episode ID {missing_id}: streams_episodes exists but streams.id {row['stream_id']} doesn't exist")
                            else:
                                print(f"     Episode ID {missing_id}: Both tables exist, JOIN should work")
                        else:
                            print(f"     Episode ID {missing_id}: Doesn't exist in streams_episodes")
                else:
                    print(f"   ✅ All episode IDs found in copies query")
                
            else:
                print("   No duplicate episodes found in database")
                
        except Exception as e:
            print(f"❌ Error during debugging: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        db.disconnect()
        print("\n✅ Debug completed!")
        return True
        
    else:
        print("❌ Failed to connect to database")
        return False

if __name__ == "__main__":
    debug_episode_copies()
