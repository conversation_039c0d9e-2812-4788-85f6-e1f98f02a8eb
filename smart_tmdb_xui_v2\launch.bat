@echo off
echo 🚀 Smart TMDB XUI v2 - Nueva Generación
echo =====================================

cd /d "%~dp0"

echo ⚡ Verificando Python...
python --version
if errorlevel 1 (
    echo ❌ Python no está instalado o no está en el PATH
    pause
    exit /b 1
)

echo 📦 Verificando dependencias...
python -c "import tkinter, pymysql, requests; print('✅ Dependencias OK')" 2>nul
if errorlevel 1 (
    echo ⚠️ Instalando dependencias...
    pip install pymysql requests
)

echo 🚀 Iniciando Smart TMDB XUI v2...
python main.py

if errorlevel 1 (
    echo ❌ Error al ejecutar la aplicación
    pause
)
