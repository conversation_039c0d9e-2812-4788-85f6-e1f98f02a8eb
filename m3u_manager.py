#!/usr/bin/env python3
"""
M3U Manager - Gestión de archivos M3U para XUI Database Manager
"""

import re
import logging
from typing import List, Dict, Optional
from urllib.parse import urlparse
import requests

class M3UManager:
    """Gestor de archivos M3U"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'XUI-Database-Manager/1.0'
        })
    
    def parse_m3u_file(self, file_path: str) -> List[Dict]:
        """Parsear archivo M3U local"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return self._parse_m3u_content(content)
        except Exception as e:
            logging.error(f"Error leyendo archivo M3U {file_path}: {e}")
            return []
    
    def parse_m3u_url(self, url: str) -> List[Dict]:
        """Parsear archivo M3U desde URL"""
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return self._parse_m3u_content(response.text)
        except Exception as e:
            logging.error(f"Error descargando M3U desde {url}: {e}")
            return []
    
    def _parse_m3u_content(self, content: str) -> List[Dict]:
        """Parsear contenido M3U"""
        entries = []
        lines = content.strip().split('\n')
        
        current_entry = {}
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('#EXTM3U'):
                continue
            elif line.startswith('#EXTINF:'):
                # Parsear línea EXTINF
                current_entry = self._parse_extinf_line(line)
            elif line.startswith('#EXTGRP:'):
                # Grupo
                current_entry['group'] = line.replace('#EXTGRP:', '').strip()
            elif line.startswith('#EXTVLCOPT:'):
                # Opciones VLC
                if 'vlc_options' not in current_entry:
                    current_entry['vlc_options'] = []
                current_entry['vlc_options'].append(line.replace('#EXTVLCOPT:', '').strip())
            elif line.startswith('http'):
                # URL del stream
                current_entry['url'] = line
                entries.append(current_entry.copy())
                current_entry = {}
        
        return entries
    
    def _parse_extinf_line(self, line: str) -> Dict:
        """Parsear línea EXTINF"""
        entry = {
            'duration': -1,
            'title': '',
            'attributes': {}
        }
        
        # Remover #EXTINF:
        line = line.replace('#EXTINF:', '')
        
        # Buscar duración
        duration_match = re.match(r'^(-?\d+(?:\.\d+)?)', line)
        if duration_match:
            entry['duration'] = float(duration_match.group(1))
            line = line[len(duration_match.group(1)):].strip()
        
        # Buscar atributos entre comas
        if ',' in line:
            parts = line.split(',', 1)
            attributes_part = parts[0].strip()
            title_part = parts[1].strip()
            
            # Parsear atributos
            entry['attributes'] = self._parse_attributes(attributes_part)
            entry['title'] = title_part
        else:
            entry['title'] = line
        
        return entry
    
    def _parse_attributes(self, attr_string: str) -> Dict:
        """Parsear atributos de EXTINF"""
        attributes = {}
        
        # Buscar atributos como key="value" o key=value
        attr_pattern = r'(\w+)=(?:"([^"]*)"|([^\s,]+))'
        matches = re.findall(attr_pattern, attr_string)
        
        for match in matches:
            key = match[0]
            value = match[1] if match[1] else match[2]
            attributes[key] = value
        
        return attributes
    
    def filter_series_entries(self, entries: List[Dict]) -> List[Dict]:
        """Filtrar solo entradas que parecen ser series"""
        series_entries = []
        
        for entry in entries:
            if self._is_series_entry(entry):
                series_entries.append(entry)
        
        return series_entries
    
    def filter_movie_entries(self, entries: List[Dict]) -> List[Dict]:
        """Filtrar solo entradas que parecen ser películas"""
        movie_entries = []
        
        for entry in entries:
            if not self._is_series_entry(entry):
                movie_entries.append(entry)
        
        return movie_entries
    
    def _is_series_entry(self, entry: Dict) -> bool:
        """Detectar si una entrada es una serie"""
        title = entry.get('title', '')
        group = entry.get('group', '')
        
        # Patrones que indican series
        series_patterns = [
            r'S\d+\s*E\d+',  # S01E01 o S01 E01 (con o sin espacio)
            r'Season\s+\d+',  # Season 1
            r'Temporada\s+\d+',  # Temporada 1
            r'\d+x\d+',  # 1x01
            r'Episode\s+\d+',  # Episode 1
            r'Episodio\s+\d+',  # Episodio 1
            r'Cap\.\s*\d+',  # Cap. 1
            r'Capítulo\s+\d+',  # Capítulo 1
        ]
        
        # Grupos que indican series
        series_groups = [
            'series', 'tv shows', 'television', 'tv series',
            'series de tv', 'programas', 'shows'
        ]
        
        # Verificar título
        for pattern in series_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                return True
        
        # Verificar grupo
        for series_group in series_groups:
            if series_group.lower() in group.lower():
                return True
        
        return False
    
    def extract_series_info(self, entry: Dict) -> Dict:
        """Extraer información detallada de serie"""
        title = entry.get('title', '')
        
        info = {
            'original_title': title,
            'series_title': title,
            'season': None,
            'episode': None,
            'episode_title': None,
            'year': None,
            'quality': None,
            'language': None,
            'group': entry.get('group', ''),
            'url': entry.get('url', ''),
            'attributes': entry.get('attributes', {})
        }
        
        # Extraer año
        year_match = re.search(r'\((\d{4})\)', title)
        if year_match:
            info['year'] = int(year_match.group(1))
        
        # Extraer calidad
        quality_patterns = [
            r'4K', r'2160p', r'UHD',
            r'1080p', r'FHD', r'Full HD',
            r'720p', r'HD',
            r'480p', r'SD'
        ]
        for pattern in quality_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                info['quality'] = pattern
                break
        
        # Extraer idioma
        lang_patterns = [
            r'LATINO', r'LAT', r'SPANISH', r'ESP',
            r'ENGLISH', r'ENG', r'SUBTITULADO', r'SUB'
        ]
        for pattern in lang_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                info['language'] = pattern
                break
        
        # Extraer información de temporada y episodio
        info.update(self._extract_episode_info(title))
        
        return info

    def extract_title_info(self, title: str) -> Dict:
        """Extraer información completa del título para análisis inteligente"""
        info = {
            'original_title': title,
            'year': None,
            'quality': None,
            'language': None,
            'season': None,
            'episode': None,
            'series_title': title
        }

        # Extraer año
        year_match = re.search(r'\b(19|20)\d{2}\b', title)
        if year_match:
            info['year'] = int(year_match.group())

        # Extraer calidad
        quality_patterns = [
            r'4K', r'UHD', r'2160p',
            r'1080p', r'FHD', r'Full HD',
            r'720p', r'HD',
            r'480p', r'SD'
        ]
        for pattern in quality_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                info['quality'] = pattern
                break

        # Extraer idioma
        lang_patterns = [
            r'LATINO', r'LAT', r'SPANISH', r'ESP',
            r'ENGLISH', r'ENG', r'SUBTITULADO', r'SUB'
        ]
        for pattern in lang_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                info['language'] = pattern
                break

        # Extraer información de temporada y episodio
        episode_info = self._extract_episode_info(title)
        info.update(episode_info)

        return info

    def _extract_episode_info(self, title: str) -> Dict:
        """Extraer información específica de episodio"""
        info = {
            'series_title': title,
            'season': None,
            'episode': None,
            'episode_title': None
        }
        
        # Patrón S01E01 o S01 E01 (con o sin espacio)
        match = re.search(r'(.+?)\s*S(\d+)\s*E(\d+)(?:\s*-?\s*(.+))?', title, re.IGNORECASE)
        if match:
            info['series_title'] = match.group(1).strip()
            info['season'] = int(match.group(2))
            info['episode'] = int(match.group(3))
            if match.group(4):
                info['episode_title'] = match.group(4).strip()
            return info
        
        # Patrón 1x01
        match = re.search(r'(.+?)\s*(\d+)x(\d+)(?:\s*-?\s*(.+))?', title, re.IGNORECASE)
        if match:
            info['series_title'] = match.group(1).strip()
            info['season'] = int(match.group(2))
            info['episode'] = int(match.group(3))
            if match.group(4):
                info['episode_title'] = match.group(4).strip()
            return info
        
        # Patrón Season/Temporada
        match = re.search(r'(.+?)\s*(?:Season|Temporada)\s+(\d+).*?(?:Episode|Episodio)\s+(\d+)(?:\s*-?\s*(.+))?', title, re.IGNORECASE)
        if match:
            info['series_title'] = match.group(1).strip()
            info['season'] = int(match.group(2))
            info['episode'] = int(match.group(3))
            if match.group(4):
                info['episode_title'] = match.group(4).strip()
            return info
        
        return info
    
    def get_m3u_statistics(self, entries: List[Dict]) -> Dict:
        """Obtener estadísticas del archivo M3U"""
        stats = {
            'total_entries': len(entries),
            'series_entries': 0,
            'movie_entries': 0,
            'groups': set(),
            'qualities': set(),
            'languages': set()
        }
        
        for entry in entries:
            if self._is_series_entry(entry):
                stats['series_entries'] += 1
            else:
                stats['movie_entries'] += 1
            
            # Recopilar grupos
            group = entry.get('group', '')
            if group:
                stats['groups'].add(group)
            
            # Extraer información adicional
            info = self.extract_series_info(entry)
            if info.get('quality'):
                stats['qualities'].add(info['quality'])
            if info.get('language'):
                stats['languages'].add(info['language'])
        
        # Convertir sets a listas para serialización
        stats['groups'] = list(stats['groups'])
        stats['qualities'] = list(stats['qualities'])
        stats['languages'] = list(stats['languages'])
        
        return stats

    def analyze_m3u_content_smart(self, entries: List[Dict]) -> Dict:
        """Análisis inteligente del contenido M3U para categorización automática"""
        analysis = {
            'total_entries': len(entries),
            'movies': [],
            'series': [],
            'live_tv': [],
            'unknown': [],
            'categories_detected': set(),
            'servers_detected': set(),
            'quality_distribution': {},
            'language_distribution': {}
        }

        for entry in entries:
            # Analizar información del título
            title_info = self.extract_title_info(entry.get('title', ''))

            # Detectar tipo de contenido
            content_type = self._detect_content_type(entry, title_info)

            # Detectar categoría automáticamente
            auto_category = self._detect_auto_category(entry, title_info)

            # Detectar servidor basándose en URL
            server_info = self._detect_server_info(entry.get('url', ''))

            # Enriquecer entrada con análisis
            enriched_entry = {
                **entry,
                'content_type': content_type,
                'auto_category': auto_category,
                'server_info': server_info,
                'title_analysis': title_info,
                'quality_score': self._calculate_quality_score(title_info),
                'import_priority': self._calculate_import_priority(entry, title_info)
            }

            # Clasificar por tipo
            if content_type == 'movie':
                analysis['movies'].append(enriched_entry)
            elif content_type == 'series':
                analysis['series'].append(enriched_entry)
            elif content_type == 'live':
                analysis['live_tv'].append(enriched_entry)
            else:
                analysis['unknown'].append(enriched_entry)

            # Recopilar estadísticas
            if auto_category:
                analysis['categories_detected'].add(auto_category)
            if server_info.get('server_name'):
                analysis['servers_detected'].add(server_info['server_name'])

            quality = title_info.get('quality', 'Unknown')
            analysis['quality_distribution'][quality] = analysis['quality_distribution'].get(quality, 0) + 1

            language = title_info.get('language', 'Unknown')
            analysis['language_distribution'][language] = analysis['language_distribution'].get(language, 0) + 1

        # Convertir sets a listas para JSON serialization
        analysis['categories_detected'] = list(analysis['categories_detected'])
        analysis['servers_detected'] = list(analysis['servers_detected'])

        return analysis

    def _detect_content_type(self, entry: Dict, title_info: Dict) -> str:
        """Detectar tipo de contenido (movie, series, live)"""
        title = entry.get('title', '').lower()
        group = entry.get('group', '').lower()

        # Detectar series por patrones de episodio
        if title_info.get('season') is not None and title_info.get('episode') is not None:
            return 'series'

        # Detectar por grupo
        if any(x in group for x in ['series', 'tv shows', 'episodios']):
            return 'series'
        elif any(x in group for x in ['movies', 'peliculas', 'films']):
            return 'movie'
        elif any(x in group for x in ['live', 'tv', 'channels', 'canales']):
            return 'live'

        # Detectar por título
        if any(x in title for x in ['s01e', 's02e', 's03e', '1x01', '2x01']):
            return 'series'
        elif any(x in title for x in ['live', 'canal', 'channel']):
            return 'live'

        # Por defecto, asumir película si no es claramente otra cosa
        return 'movie'

    def _detect_auto_category(self, entry: Dict, title_info: Dict) -> str:
        """Detectar categoría automáticamente basándose en contenido"""
        title = entry.get('title', '').lower()
        group = entry.get('group', '').lower()

        # Categorías por género/tipo
        if any(x in title or x in group for x in ['action', 'accion', 'adventure']):
            return 'Action'
        elif any(x in title or x in group for x in ['comedy', 'comedia', 'humor']):
            return 'Comedy'
        elif any(x in title or x in group for x in ['drama', 'romance']):
            return 'Drama'
        elif any(x in title or x in group for x in ['horror', 'terror', 'scary']):
            return 'Horror'
        elif any(x in title or x in group for x in ['sci-fi', 'science', 'fiction']):
            return 'Sci-Fi'
        elif any(x in title or x in group for x in ['documentary', 'documental']):
            return 'Documentary'
        elif any(x in title or x in group for x in ['kids', 'children', 'infantil', 'niños']):
            return 'Kids'
        elif any(x in title or x in group for x in ['anime', 'manga']):
            return 'Anime'
        elif any(x in title or x in group for x in ['latino', 'spanish', 'español']):
            return 'Latino'
        elif any(x in title or x in group for x in ['4k', 'uhd', '2160p']):
            return '4K Content'

        # Categoría por año si es reciente
        year = title_info.get('year')
        if year and year >= 2020:
            return 'Recent'

        return 'General'

    def _detect_server_info(self, url: str) -> Dict:
        """Detectar información del servidor basándose en la URL"""
        if not url:
            return {'server_name': 'Unknown', 'server_type': 'Unknown'}

        # Extraer dominio
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            domain = parsed.netloc.lower()

            # Detectar tipos de servidor conocidos
            if any(x in domain for x in ['xtream', 'xui']):
                return {'server_name': domain, 'server_type': 'Xtream'}
            elif any(x in domain for x in ['m3u8', 'hls']):
                return {'server_name': domain, 'server_type': 'HLS'}
            elif any(x in domain for x in ['rtmp', 'rtsp']):
                return {'server_name': domain, 'server_type': 'RTMP'}
            else:
                return {'server_name': domain, 'server_type': 'HTTP'}

        except Exception:
            return {'server_name': 'Unknown', 'server_type': 'Unknown'}

    def _calculate_quality_score(self, title_info: Dict) -> int:
        """Calcular score de calidad basándose en información del título"""
        score = 0

        quality = (title_info.get('quality', '') or '').lower()
        if '4k' in quality or '2160p' in quality:
            score += 100
        elif '1080p' in quality or 'fhd' in quality:
            score += 80
        elif '720p' in quality or 'hd' in quality:
            score += 60
        elif '480p' in quality:
            score += 40

        language = title_info.get('language', '') or ''
        if 'latino' in language.lower():
            score += 20

        year = title_info.get('year')
        if year and year >= 2020:
            score += 10

        return score

    def _calculate_import_priority(self, entry: Dict, title_info: Dict) -> str:
        """Calcular prioridad de importación"""
        quality_score = self._calculate_quality_score(title_info)

        if quality_score >= 100:
            return 'HIGH'
        elif quality_score >= 60:
            return 'MEDIUM'
        else:
            return 'LOW'

    def generate_import_recommendations(self, analysis: Dict) -> Dict:
        """Generar recomendaciones inteligentes para importación M3U"""
        recommendations = {
            'high_priority': [],
            'medium_priority': [],
            'low_priority': [],
            'skip_import': [],
            'category_mapping': {},
            'server_assignments': {},
            'duplicate_warnings': []
        }

        # Procesar películas
        for movie in analysis['movies']:
            if movie['import_priority'] == 'HIGH':
                recommendations['high_priority'].append(movie)
            elif movie['import_priority'] == 'MEDIUM':
                recommendations['medium_priority'].append(movie)
            else:
                recommendations['low_priority'].append(movie)

            # Mapear categoría
            category = movie['auto_category']
            if category not in recommendations['category_mapping']:
                recommendations['category_mapping'][category] = []
            recommendations['category_mapping'][category].append(movie['title'])

        # Procesar series
        for series in analysis['series']:
            if series['import_priority'] == 'HIGH':
                recommendations['high_priority'].append(series)
            elif series['import_priority'] == 'MEDIUM':
                recommendations['medium_priority'].append(series)
            else:
                recommendations['low_priority'].append(series)

        # Detectar posibles duplicados por título similar
        all_content = analysis['movies'] + analysis['series']
        for i, content1 in enumerate(all_content):
            for content2 in all_content[i+1:]:
                if self._titles_similar(content1['title'], content2['title']):
                    recommendations['duplicate_warnings'].append({
                        'title1': content1['title'],
                        'title2': content2['title'],
                        'similarity': 'high'
                    })

        return recommendations

    def _titles_similar(self, title1: str, title2: str) -> bool:
        """Detectar si dos títulos son similares (posibles duplicados)"""
        # Normalizar títulos
        t1 = re.sub(r'[^\w\s]', '', title1.lower()).strip()
        t2 = re.sub(r'[^\w\s]', '', title2.lower()).strip()

        # Remover años y calidades
        t1 = re.sub(r'\b(19|20)\d{2}\b', '', t1)
        t2 = re.sub(r'\b(19|20)\d{2}\b', '', t2)
        t1 = re.sub(r'\b(4k|1080p|720p|hd|fhd|uhd)\b', '', t1)
        t2 = re.sub(r'\b(4k|1080p|720p|hd|fhd|uhd)\b', '', t2)

        # Comparar similitud básica
        if t1 == t2:
            return True

        # Verificar si uno contiene al otro (con al menos 5 caracteres)
        if len(t1) >= 5 and len(t2) >= 5:
            if t1 in t2 or t2 in t1:
                return True

        return False
