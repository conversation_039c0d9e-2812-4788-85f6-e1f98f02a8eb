#!/usr/bin/env python3
"""
Script para verificar que la integración de limpieza inteligente funciona correctamente
"""

import sys
import inspect
from gui import XUIManagerGUI

def verificar_integracion():
    """Verificar que todas las funcionalidades están integradas correctamente"""
    print("=== Verificación de Integración de Limpieza Inteligente ===\n")
    
    # Crear instancia de la GUI (sin mostrar)
    try:
        gui = XUIManagerGUI()
        print("✓ GUI creada exitosamente")
    except Exception as e:
        print(f"✗ Error creando GUI: {e}")
        return False
    
    # Verificar que las funciones de limpieza masiva existen
    funciones_masiva = [
        'load_all_duplicates_for_mass',
        'select_all_mass_duplicates', 
        'deselect_all_mass_duplicates',
        'preview_mass_cleanup',
        'execute_mass_cleanup',
        'export_cleanup_report',
        'toggle_mass_selection'
    ]
    
    print("1. Verificando funciones de limpieza masiva:")
    for func_name in funciones_masiva:
        if hasattr(gui, func_name):
            print(f"   ✓ {func_name}")
        else:
            print(f"   ✗ {func_name} - FALTANTE")
    
    # Verificar que las funciones de limpieza individual existen
    funciones_individual = [
        'load_priority_duplicates',
        'select_all_priority_duplicates',
        'apply_auto_cleanup',
        'show_priority_details',
        'apply_selected_recommendations',
        'apply_recommendation',
        'manual_selection'
    ]
    
    print("\n2. Verificando funciones de limpieza individual:")
    for func_name in funciones_individual:
        if hasattr(gui, func_name):
            print(f"   ✓ {func_name}")
        else:
            print(f"   ✗ {func_name} - FALTANTE")
    
    # Verificar que las funciones de selección manual con memoria existen
    funciones_memoria = [
        'save_current_movie_selection',
        'restore_movie_selection',
        'auto_save_current_selection',
        'update_saved_selections_counter',
        'execute_all_saved_selections',
        'debug_memory_status'
    ]
    
    print("\n3. Verificando funciones de memoria:")
    for func_name in funciones_memoria:
        if hasattr(gui, func_name):
            print(f"   ✓ {func_name}")
        else:
            print(f"   ✗ {func_name} - FALTANTE")
    
    # Verificar que los componentes de UI existen
    componentes_ui = [
        'cleanup_notebook',  # Notebook interno
        'priority_tree',     # Árbol de limpieza individual
        'mass_tree',         # Árbol de limpieza masiva
        'mass_stats_text'    # Estadísticas de limpieza masiva
    ]
    
    print("\n4. Verificando componentes de UI:")
    for comp_name in componentes_ui:
        if hasattr(gui, comp_name):
            print(f"   ✓ {comp_name}")
        else:
            print(f"   ✗ {comp_name} - FALTANTE")
    
    # Verificar que las funciones de setup existen
    funciones_setup = [
        'setup_smart_cleanup_tab',
        'setup_individual_cleanup_tab',
        'setup_mass_cleanup_internal_tab'
    ]
    
    print("\n5. Verificando funciones de configuración:")
    for func_name in funciones_setup:
        if hasattr(gui, func_name):
            print(f"   ✓ {func_name}")
        else:
            print(f"   ✗ {func_name} - FALTANTE")
    
    # Verificar que no existe la función antigua
    print("\n6. Verificando eliminación de funciones obsoletas:")
    funciones_obsoletas = [
        'setup_mass_cleanup_tab'  # Esta debería haber sido eliminada
    ]
    
    for func_name in funciones_obsoletas:
        if hasattr(gui, func_name):
            # Verificar si es la función nueva integrada o la antigua
            func = getattr(gui, func_name)
            source = inspect.getsource(func)
            if 'Configurar la pestaña de limpieza masiva' in source and 'ttk.Frame(self.notebook)' in source:
                print(f"   ✗ {func_name} - FUNCIÓN ANTIGUA AÚN EXISTE")
            else:
                print(f"   ✓ {func_name} - Función actualizada correctamente")
        else:
            print(f"   ✓ {func_name} - Función obsoleta eliminada correctamente")
    
    # Verificar estructura del notebook
    print("\n7. Verificando estructura de pestañas:")
    try:
        # Simular setup para verificar estructura
        gui.setup_smart_cleanup_tab()
        
        if hasattr(gui, 'cleanup_notebook'):
            print("   ✓ Notebook interno creado")
            
            # Verificar que tiene las sub-pestañas correctas
            if hasattr(gui.cleanup_notebook, 'tabs'):
                tabs = gui.cleanup_notebook.tabs()
                if len(tabs) >= 2:
                    print(f"   ✓ Sub-pestañas creadas: {len(tabs)}")
                else:
                    print(f"   ✗ Número incorrecto de sub-pestañas: {len(tabs)}")
            else:
                print("   ⚠ No se puede verificar sub-pestañas (normal en modo sin GUI)")
        else:
            print("   ✗ Notebook interno no creado")
            
    except Exception as e:
        print(f"   ⚠ Error verificando estructura: {e}")
    
    # Resumen
    print("\n" + "="*60)
    print("RESUMEN DE VERIFICACIÓN:")
    print("="*60)
    
    # Contar funciones verificadas
    total_funciones = len(funciones_masiva) + len(funciones_individual) + len(funciones_memoria)
    funciones_encontradas = 0
    
    for func_list in [funciones_masiva, funciones_individual, funciones_memoria]:
        for func_name in func_list:
            if hasattr(gui, func_name):
                funciones_encontradas += 1
    
    print(f"Funciones verificadas: {funciones_encontradas}/{total_funciones}")
    
    # Verificar componentes UI
    componentes_encontrados = 0
    for comp_name in componentes_ui:
        if hasattr(gui, comp_name):
            componentes_encontrados += 1
    
    print(f"Componentes UI: {componentes_encontrados}/{len(componentes_ui)}")
    
    # Verificar funciones de setup
    setup_encontradas = 0
    for func_name in funciones_setup:
        if hasattr(gui, func_name):
            setup_encontradas += 1
    
    print(f"Funciones de setup: {setup_encontradas}/{len(funciones_setup)}")
    
    # Resultado final
    if (funciones_encontradas == total_funciones and 
        componentes_encontrados >= len(componentes_ui) - 1 and  # -1 porque cleanup_notebook puede no existir sin GUI
        setup_encontradas == len(funciones_setup)):
        print("\n🎉 ¡INTEGRACIÓN EXITOSA!")
        print("✅ Todas las funcionalidades de limpieza masiva han sido integradas correctamente")
        print("✅ La interfaz unificada está lista para usar")
        print("✅ El sistema de memoria está completamente funcional")
        return True
    else:
        print("\n⚠️ INTEGRACIÓN INCOMPLETA")
        print("❌ Algunas funcionalidades pueden estar faltando")
        print("❌ Revisar los elementos marcados con ✗")
        return False

def main():
    try:
        resultado = verificar_integracion()
        if resultado:
            print("\n🚀 La aplicación está lista para usar con la nueva interfaz unificada!")
        else:
            print("\n🔧 Se requieren ajustes adicionales en la integración.")
        
        input("\nPresiona Enter para salir...")
    except Exception as e:
        print(f"\nError durante la verificación: {e}")

if __name__ == "__main__":
    main()
