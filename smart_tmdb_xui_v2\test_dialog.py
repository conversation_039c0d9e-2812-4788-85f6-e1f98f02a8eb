#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test del diálogo de conexión
Prueba independiente para verificar si los botones funcionan
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Agregar el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from config_manager import ConfigManager
    from connection_dialog import SmartConnectionDialog
    print("✅ Módulos importados correctamente")
except ImportError as e:
    print(f"❌ Error importando módulos: {e}")
    sys.exit(1)

def test_connection_dialog():
    """Probar el diálogo de conexión"""
    print("🧪 Iniciando prueba del diálogo de conexión...")
    
    # Crear ventana principal
    root = tk.Tk()
    root.title("Test Connection Dialog")
    root.geometry("300x200")
    
    # Crear config manager
    config_manager = ConfigManager()
    
    def show_dialog():
        """Mostrar el diálogo"""
        print("📱 Abriendo diálogo de conexión...")
        
        def on_connection(data):
            print(f"✅ Conexión establecida: {data}")
            result_label.config(text=f"Conectado: {data['host']}:{data['database']}")
        
        # Crear el diálogo directamente sin el método show()
        dialog = SmartConnectionDialog(root, config_manager, on_connection)
        print("🎯 Diálogo creado. Debería aparecer una ventana.")
    
    # Interfaz de prueba
    ttk.Label(root, text="🧪 Test del Diálogo de Conexión", 
             font=('Segoe UI', 12, 'bold')).pack(pady=20)
    
    ttk.Button(root, text="🔌 Abrir Diálogo", 
              command=show_dialog).pack(pady=10)
    
    result_label = ttk.Label(root, text="Estado: No conectado")
    result_label.pack(pady=10)
    
    ttk.Button(root, text="❌ Cerrar", 
              command=root.quit).pack(pady=5)
    
    print("🎯 Ventana de prueba creada. Haz clic en 'Abrir Diálogo'")
    root.mainloop()

if __name__ == "__main__":
    test_connection_dialog()
