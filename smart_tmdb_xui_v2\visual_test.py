#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test visual del diálogo - mostrar solo el diálogo para verificar botones
"""

import tkinter as tk
from tkinter import ttk
from config_manager import ConfigManager
from connection_dialog import SmartConnectionDialog

def main():
    print("🧪 Test visual del diálogo de conexión")
    print("📂 Directorio:", __file__)
    
    # Crear ventana temporal (se ocultará)
    temp_root = tk.Tk()
    temp_root.withdraw()  # Ocultar la ventana principal
    
    # Crear config manager
    config_manager = ConfigManager()
    
    def on_connection(data):
        print(f"✅ Conexión exitosa: {data}")
        temp_root.quit()
    
    # Crear el diálogo directamente
    print("📱 Creando diálogo...")
    dialog = SmartConnectionDialog(temp_root, config_manager, on_connection)
    
    print("🎯 Diálogo creado. ¿Puedes ver los botones?")
    print("   - 🧪 Probar Conexión (izquierda)")
    print("   - 🔌 Conectar (derecha)")
    print("   - ❌ Cancelar (derecha)")
    
    # Llenar datos por defecto para testing
    dialog.entries['host'].delete(0, tk.END)
    dialog.entries['host'].insert(0, "**************")
    
    dialog.entries['user'].delete(0, tk.END)
    dialog.entries['user'].insert(0, "infest84")
    
    dialog.entries['password'].delete(0, tk.END)
    dialog.entries['password'].insert(0, "v5XRWkgns4VBcYnxcJmlahbmGg5azpTS7FCVEuEDkk93BG7zFr")
    
    dialog.entries['database'].delete(0, tk.END)
    dialog.entries['database'].insert(0, "xui")
    
    dialog.entries['port'].delete(0, tk.END)
    dialog.entries['port'].insert(0, "3306")
    
    print("📝 Datos pre-llenados. Solo haz clic en '🔌 Conectar'")
    
    temp_root.mainloop()

if __name__ == "__main__":
    main()
