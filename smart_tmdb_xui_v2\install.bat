@echo off
echo.
echo =========================================================
echo    Smart TMDB XUI v2 - Instalacion y Configuracion
echo =========================================================
echo.

REM Verificar Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python no encontrado. Por favor instala Python 3.8+
    pause
    exit /b 1
)

echo ✅ Python encontrado:
python --version

echo.
echo 📦 Instalando dependencias...
pip install pymysql requests Pillow python-dateutil

if errorlevel 1 (
    echo ❌ Error instalando dependencias
    pause
    exit /b 1
)

echo.
echo ✅ Dependencias instaladas correctamente
echo.
echo 🚀 Smart TMDB XUI v2 listo para usar!
echo.
echo Ejecuta launcher.bat para iniciar la aplicacion
echo.
pause
