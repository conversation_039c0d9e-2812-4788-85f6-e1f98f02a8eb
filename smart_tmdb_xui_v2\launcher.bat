@echo off
echo.
echo ===============================================
echo    Smart TMDB XUI v2 - Nueva Generacion
echo ===============================================
echo.
echo Selecciona la version a ejecutar:
echo.
echo 1) Demo App     - Version de demostracion
echo 2) Main App     - Aplicacion completa
echo 3) Salir
echo.
set /p choice="Ingresa tu opcion (1-3): "

if "%choice%"=="1" (
    echo.
    echo Ejecutando Demo App...
    python demo_app.py
    pause
) else if "%choice%"=="2" (
    echo.
    echo Ejecutando Main App...
    python main.py
    pause
) else if "%choice%"=="3" (
    echo.
    echo Saliendo...
    exit
) else (
    echo.
    echo Opcion invalida. Intenta de nuevo.
    pause
    goto :eof
)
