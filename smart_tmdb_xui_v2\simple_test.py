#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple sin GUI para verificar imports y estructura del diálogo
"""

import sys
import os

print(f"📂 Directorio actual: {os.getcwd()}")
print("🔍 Verificando imports...")

try:
    from config_manager import ConfigManager
    print("✅ ConfigManager importado")
    
    from connection_dialog import SmartConnectionDialog
    print("✅ SmartConnectionDialog importado")
    
    # Verificar que el diálogo tiene los métodos necesarios
    methods = dir(SmartConnectionDialog)
    important_methods = ['__init__', 'setup_ui', 'connect', 'test_connection']
    
    print("\n🔧 Verificando métodos del diálogo:")
    for method in important_methods:
        if method in methods:
            print(f"   ✅ {method}")
        else:
            print(f"   ❌ {method} - FALTANTE")
    
    # Crear una instancia de ConfigManager
    config = ConfigManager()
    print(f"✅ ConfigManager creado: {type(config)}")
    
    print("\n🎯 Todos los imports funcionan correctamente")
    print("💡 El problema no está en los imports, debe estar en la GUI o en la lógica del diálogo")
    
except ImportError as e:
    print(f"❌ Error de import: {e}")
except Exception as e:
    print(f"❌ Error general: {e}")

print(f"\n📍 Test completado desde: {os.getcwd()}")
