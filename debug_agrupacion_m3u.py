#!/usr/bin/env python3
"""
Debug script para diagnosticar el problema de agrupación M3U
"""

import sys
import os
from m3u_manager import M3<PERSON>anager

def test_telematch_grouping():
    """Test específico para el problema de Telematch"""
    print("🔍 DEBUGGING M3U GROUPING ISSUE")
    print("=" * 50)
    
    # Crear datos de prueba similares al problema reportado
    telematch_data = [
        {
            'title': 'Telematch (1965) S01 E00',
            'url': 'http://example.com/telematch/s01e00.ts',
            'group': 'Vintage Series',
            'attributes': {}
        },
        {
            'title': 'Telematch (1965) S01 E01',
            'url': 'http://example.com/telematch/s01e01.ts',
            'group': 'Vintage Series',
            'attributes': {}
        },
        {
            'title': 'Telematch (1965) S01 E02',
            'url': 'http://example.com/telematch/s01e02.ts',
            'group': 'Vintage Series',
            'attributes': {}
        },
        {
            'title': 'Telematch (1965) S01 E11',
            'url': 'http://example.com/telematch/s01e11.ts',
            'group': 'Vintage Series',
            'attributes': {}
        }
    ]
    
    # Crear manager M3U
    manager = M3UManager()
    
    print("📋 Test Data:")
    for i, item in enumerate(telematch_data):
        print(f"   {i+1}. {item['title']}")
    
    print("\n🔍 Extracting Series Info:")
    series_groups = {}
    
    for item in telematch_data:
        info = manager.extract_series_info(item)
        series_title = info.get('series_title', '').strip()
        episode_info = f"S{info.get('season', 0):02d}E{info.get('episode', 0):02d}"
        
        print(f"\n📺 Item: {item['title']}")
        print(f"   🎬 Extracted series_title: '{series_title}'")
        print(f"   📅 Season: {info.get('season')}")
        print(f"   📺 Episode: {info.get('episode')}")
        print(f"   🏷️ Episode Info: {episode_info}")
        print(f"   📅 Year: {info.get('year')}")
        
        if series_title:
            if series_title not in series_groups:
                series_groups[series_title] = []
            series_groups[series_title].append({
                'episode_info': episode_info,
                'season': info.get('season'),
                'episode': info.get('episode'),
                'original_item': item
            })
    
    print(f"\n📊 GROUPING RESULTS:")
    print(f"   🎬 Total unique series: {len(series_groups)}")
    
    for series_title, episodes in series_groups.items():
        print(f"\n   📺 Series: '{series_title}'")
        print(f"      📺 Episodes: {len(episodes)}")
        for ep in episodes:
            print(f"         - {ep['episode_info']}")
    
    print(f"\n✅ Expected: 1 series with 4 episodes")
    print(f"🔍 Actual: {len(series_groups)} series")
    
    if len(series_groups) == 1:
        print("✅ GROUPING WORKS CORRECTLY!")
    else:
        print("❌ GROUPING FAILED!")
        print("🔍 Debugging series_title extraction...")
        
        # Debug más detallado
        for item in telematch_data:
            info = manager.extract_series_info(item)
            print(f"\n🔍 Debug: {item['title']}")
            print(f"   Raw info: {info}")

def test_sample_m3u():
    """Test con el archivo M3U de ejemplo"""
    print("\n" + "=" * 50)
    print("🔍 TESTING SAMPLE M3U FILE")
    print("=" * 50)
    
    if not os.path.exists('sample_series.m3u'):
        print("❌ sample_series.m3u not found!")
        return
    
    manager = M3UManager()
    
    try:
        # Cargar M3U
        entries = manager.parse_m3u_file('sample_series.m3u')
        print(f"📋 Loaded {len(entries)} entries from sample_series.m3u")
        
        # Agrupar por series
        series_groups = {}
        
        for entry in entries:
            info = manager.extract_series_info(entry)
            series_title = info.get('series_title', '').strip()
            
            if series_title and manager._is_series_entry(entry):
                if series_title not in series_groups:
                    series_groups[series_title] = []
                series_groups[series_title].append(entry)
        
        print(f"\n📊 SAMPLE M3U GROUPING:")
        print(f"   🎬 Total unique series: {len(series_groups)}")
        
        for series_title, episodes in series_groups.items():
            print(f"\n   📺 Series: '{series_title}'")
            print(f"      📺 Episodes: {len(episodes)}")
            for ep in episodes:
                print(f"         - {ep['title']}")
                
    except Exception as e:
        print(f"❌ Error testing sample M3U: {e}")

if __name__ == "__main__":
    test_telematch_grouping()
    test_sample_m3u()
