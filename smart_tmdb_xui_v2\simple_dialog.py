#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Connection Dialog - Nuevo desde Cero
Guarda credenciales, botones de sesión y checkboxes funcionales
"""

import tkinter as tk
from tkinter import messagebox
import json
import os

class SimpleConnectionDialog:
    def __init__(self, parent=None, config_manager=None, callback=None):
        self.parent = parent
        self.config_manager = config_manager
        self.callback = callback
        self.result = None
        
        # Archivo para guardar credenciales
        self.credentials_file = "connection_credentials.json"
        
        # Gaming Colors
        self.colors = {
            'bg_primary': '#0A0A0A',
            'bg_secondary': '#1A1A1A',
            'bg_input': '#2A2A2A',
            'nvidia_green': '#76B900',
            'nvidia_dark': '#5A8A00',
            'rog_red': '#FF0040',
            'text_white': '#FFFFFF',
            'text_gray': '#CCCCCC',
            'border': '#444444'
        }
        
        self.setup_window()
        self.load_saved_credentials()
        self.create_ui()
        
    def setup_window(self):
        """Configurar ventana principal"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("🎮 Database Login")
        self.window.geometry("500x500")  # Aumentar altura para mostrar botones
        self.window.configure(bg=self.colors['bg_primary'])
        self.window.resizable(False, False)
        
        if self.parent:
            self.window.transient(self.parent)
            self.window.grab_set()
            self.center_window()
            
        self.window.protocol("WM_DELETE_WINDOW", self.cancel)
    
    def center_window(self):
        """Centrar ventana"""
        if self.parent:
            parent_x = self.parent.winfo_rootx()
            parent_y = self.parent.winfo_rooty()
            parent_width = self.parent.winfo_width()
            parent_height = self.parent.winfo_height()
            x = parent_x + (parent_width - 500) // 2
            y = parent_y + (parent_height - 500) // 2  # Ajustar para nueva altura
            self.window.geometry(f"500x500+{x}+{y}")
        else:
            screen_width = self.window.winfo_screenwidth()
            screen_height = self.window.winfo_screenheight()
            x = (screen_width - 500) // 2
            y = (screen_height - 500) // 2  # Ajustar para nueva altura
            self.window.geometry(f"500x500+{x}+{y}")
    
    def create_ui(self):
        """Crear interfaz completa"""
        # Header
        header_frame = tk.Frame(self.window, bg=self.colors['nvidia_green'], height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame,
                              text="🎮 DATABASE CONNECTION",
                              font=('Arial', 16, 'bold'),
                              bg=self.colors['nvidia_green'],
                              fg='black')
        title_label.pack(expand=True)
        
        # Main content frame
        main_frame = tk.Frame(self.window, bg=self.colors['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Credentials form
        self.create_credentials_form(main_frame)
        
        # Checkboxes
        self.create_checkboxes(main_frame)
        
        # Buttons
        self.create_buttons(main_frame)
    
    def create_credentials_form(self, parent):
        """Crear formulario de credenciales"""
        form_frame = tk.LabelFrame(parent,
                                  text="Database Credentials",
                                  font=('Arial', 12, 'bold'),
                                  bg=self.colors['bg_secondary'],
                                  fg=self.colors['nvidia_green'],
                                  bd=2,
                                  relief='solid')
        form_frame.pack(fill='x', pady=(0, 20))
        
        # Grid para organizar campos
        fields = [
            ("Host:", "host", "**************"),
            ("Username:", "username", "infest84"),
            ("Password:", "password", "v5XRWkgns4VBcYnxcJmlahbmGg5azpTS7FCVEuEDkk93BG7zFr"),
            ("Database:", "database", "xui"),
            ("Port:", "port", "3306")
        ]
        
        self.entries = {}
        
        for i, (label_text, key, default_value) in enumerate(fields):
            # Label
            label = tk.Label(form_frame,
                           text=label_text,
                           font=('Arial', 10, 'bold'),
                           bg=self.colors['bg_secondary'],
                           fg=self.colors['text_white'])
            label.grid(row=i, column=0, sticky='w', padx=15, pady=8)
            
            # Entry
            entry = tk.Entry(form_frame,
                           font=('Arial', 10),
                           bg=self.colors['bg_input'],
                           fg=self.colors['text_white'],
                           insertbackground=self.colors['nvidia_green'],
                           bd=1,
                           relief='solid',
                           width=35)
            
            if key == "password":
                entry.config(show='*')
            
            # Insertar valor por defecto
            entry.insert(0, default_value)
                
            entry.grid(row=i, column=1, sticky='ew', padx=(5, 15), pady=8)
            self.entries[key] = entry
        
        # Configurar columnas para que se expandan
        form_frame.columnconfigure(1, weight=1)
    
    def create_checkboxes(self, parent):
        """Crear checkboxes funcionales"""
        checkbox_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        checkbox_frame.pack(fill='x', pady=(0, 20))
        
        # Variables para checkboxes
        self.auto_connect_var = tk.BooleanVar()
        self.remember_credentials_var = tk.BooleanVar()
        
        # Auto connect checkbox
        auto_check = tk.Checkbutton(checkbox_frame,
                                   text="🚀 Conectar automáticamente al iniciar",
                                   variable=self.auto_connect_var,
                                   font=('Arial', 10),
                                   bg=self.colors['bg_primary'],
                                   fg=self.colors['text_white'],
                                   selectcolor=self.colors['bg_input'],
                                   activebackground=self.colors['bg_primary'],
                                   activeforeground=self.colors['nvidia_green'])
        auto_check.pack(anchor='w', pady=5)
        
        # Remember credentials checkbox
        remember_check = tk.Checkbutton(checkbox_frame,
                                       text="💾 Guardar y recordar credenciales",
                                       variable=self.remember_credentials_var,
                                       font=('Arial', 10),
                                       bg=self.colors['bg_primary'],
                                       fg=self.colors['text_white'],
                                       selectcolor=self.colors['bg_input'],
                                       activebackground=self.colors['bg_primary'],
                                       activeforeground=self.colors['nvidia_green'])
        remember_check.pack(anchor='w', pady=5)
    
    def create_buttons(self, parent):
        """Crear botones de acción"""
        print("DEBUG: Creando botones INICIAR SESIÓN y CANCELAR...")
        
        button_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        button_frame.pack(fill='x', pady=30)  # Más padding para visibilidad
        
        # Frame centrado para botones
        center_frame = tk.Frame(button_frame, bg=self.colors['bg_primary'])
        center_frame.pack(expand=True)
        
        print("DEBUG: Creando botón INICIAR SESIÓN...")
        # INICIAR SESIÓN button
        login_btn = tk.Button(center_frame,
                             text="🚀 INICIAR SESIÓN",
                             command=self.login,
                             font=('Arial', 12, 'bold'),
                             bg=self.colors['nvidia_green'],
                             fg='black',
                             activebackground=self.colors['nvidia_dark'],
                             activeforeground='white',
                             relief='raised',
                             bd=3,
                             padx=30,
                             pady=15,
                             cursor='hand2')
        login_btn.pack(side='left', padx=15)
        
        print("DEBUG: Creando botón CANCELAR...")
        # CANCELAR button
        cancel_btn = tk.Button(center_frame,
                              text="❌ CANCELAR",
                              command=self.cancel,
                              font=('Arial', 12, 'bold'),
                              bg=self.colors['rog_red'],
                              fg='white',
                              activebackground='#CC0033',
                              activeforeground='white',
                              relief='raised',
                              bd=3,
                              padx=30,
                              pady=15,
                              cursor='hand2')
        cancel_btn.pack(side='left', padx=15)
        
        print("DEBUG: ✅ Botones creados exitosamente!")
    
    def load_saved_credentials(self):
        """Cargar credenciales guardadas"""
        if os.path.exists(self.credentials_file):
            try:
                with open(self.credentials_file, 'r') as f:
                    self.saved_data = json.load(f)
                print("✅ Credenciales cargadas desde archivo")
            except Exception as e:
                print(f"⚠️ Error cargando credenciales: {e}")
                self.saved_data = {}
        else:
            self.saved_data = {}
    
    def save_credentials(self, data):
        """Guardar credenciales en archivo"""
        try:
            save_data = {
                'credentials': data,
                'auto_connect': self.auto_connect_var.get(),
                'remember_credentials': self.remember_credentials_var.get()
            }
            
            with open(self.credentials_file, 'w') as f:
                json.dump(save_data, f, indent=4)
            print("✅ Credenciales guardadas exitosamente")
            return True
        except Exception as e:
            print(f"❌ Error guardando credenciales: {e}")
            return False
    
    def populate_saved_data(self):
        """Llenar campos con datos guardados"""
        if self.saved_data and 'credentials' in self.saved_data:
            credentials = self.saved_data['credentials']
            
            # Llenar campos
            for key, entry in self.entries.items():
                if key in credentials:
                    entry.delete(0, tk.END)
                    entry.insert(0, credentials[key])
            
            # Configurar checkboxes
            if 'auto_connect' in self.saved_data:
                self.auto_connect_var.set(self.saved_data['auto_connect'])
            if 'remember_credentials' in self.saved_data:
                self.remember_credentials_var.set(self.saved_data['remember_credentials'])
    
    def login(self):
        """Función de inicio de sesión"""
        # Obtener datos
        data = {}
        for key, entry in self.entries.items():
            value = entry.get().strip()
            if not value:
                messagebox.showerror("Error", f"El campo {key} es requerido")
                return
            data[key] = value
        
        # Convertir puerto a entero
        try:
            data['port'] = int(data['port'])
        except ValueError:
            messagebox.showerror("Error", "El puerto debe ser un número")
            return
        
        # Guardar credenciales si está marcado
        if self.remember_credentials_var.get():
            if self.save_credentials(data):
                messagebox.showinfo("Guardado", "📂 Credenciales guardadas exitosamente")
        
        # Configurar resultado
        self.result = {
            'credentials': data,
            'auto_connect': self.auto_connect_var.get(),
            'remember_credentials': self.remember_credentials_var.get()
        }
        
        # Ejecutar callback si existe
        if self.callback:
            try:
                self.callback(data)
                messagebox.showinfo("Éxito", "🎮 ¡Conexión establecida exitosamente!")
            except Exception as e:
                messagebox.showerror("Error de Conexión", f"❌ Error conectando a la base de datos:\n\n{str(e)}")
                return
        
        # Cerrar ventana
        self.window.destroy()
    
    def cancel(self):
        """Cancelar diálogo"""
        self.result = None
        self.window.destroy()
    
    def show(self):
        """Mostrar diálogo y devolver resultado"""
        # Cargar datos guardados si existen
        self.populate_saved_data()
        
        # Auto-conectar si está configurado
        if (self.saved_data and 
            self.saved_data.get('auto_connect', False) and 
            'credentials' in self.saved_data):
            print("🚀 Auto-conectando...")
            self.auto_connect_var.set(True)
            self.remember_credentials_var.set(True)
            # Esperar un momento para que la UI se renderice
            self.window.after(500, self.login)
        
        self.window.mainloop()
        return self.result

# Función de compatibilidad
def show_simple_connection_dialog(parent=None, config_manager=None, callback=None):
    """Función de compatibilidad"""
    dialog = SimpleConnectionDialog(parent, config_manager, callback)
    return dialog.show()

if __name__ == "__main__":
    print("🎮 Simple Connection Dialog - Nuevo desde Cero")
    print("Testing standalone mode...")
    
    def test_callback(data):
        print(f"✅ Callback ejecutado con datos: {data}")
    
    dialog = SimpleConnectionDialog(callback=test_callback)
    result = dialog.show()
    
    if result:
        print(f"✅ Resultado: {result}")
    else:
        print("❌ Diálogo cancelado")
