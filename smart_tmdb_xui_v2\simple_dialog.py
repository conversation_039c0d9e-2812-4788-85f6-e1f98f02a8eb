#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Modern Connection Dialog - Clean & Functional
Sin threading complejo, diseño moderno y limpio
"""

import tkinter as tk
from tkinter import ttk, messagebox

class SimpleConnectionDialog:
    def __init__(self, parent=None, config_manager=None, callback=None):
        self.parent = parent
        self.config_manager = config_manager
        self.callback = callback
        self.result = None
        
        # Modern Color Scheme
        self.colors = {
            'bg_primary': '#1E1E1E',      # Dark background
            'bg_secondary': '#2D2D2D',    # Secondary background
            'bg_input': '#3C3C3C',        # Input background
            'accent_blue': '#007ACC',     # VS Code blue
            'accent_green': '#4CAF50',    # Success green
            'accent_orange': '#FF9800',   # Warning orange
            'accent_red': '#F44336',      # Error red
            'text_primary': '#FFFFFF',    # White text
            'text_secondary': '#CCCCCC',  # Gray text
            'border': '#555555'           # Border color
        }
        
        self.setup_window()
        self.setup_ui()
        
    def setup_window(self):
        """Configurar ventana principal"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("🔗 Modern Database Connection")
        self.window.geometry("480x420")
        self.window.configure(bg=self.colors['bg_primary'])
        self.window.resizable(False, False)
        
        # Hacer modal si hay parent
        if self.parent:
            self.window.transient(self.parent)
            self.window.grab_set()
            # Centrar respecto al parent
            self.center_window()
    
    def center_window(self):
        """Centrar ventana respecto al parent"""
        if self.parent:
            parent_x = self.parent.winfo_rootx()
            parent_y = self.parent.winfo_rooty()
            parent_width = self.parent.winfo_width()
            parent_height = self.parent.winfo_height()
            
            x = parent_x + (parent_width - 480) // 2
            y = parent_y + (parent_height - 420) // 2
            
            self.window.geometry(f"480x420+{x}+{y}")
    
    def setup_ui(self):
        """Configurar interfaz de usuario"""
        # Header
        self.create_header()
        
        # Connection Form
        self.create_connection_form()
        
        # Action Buttons
        self.create_action_buttons()
        
        # Status Bar
        self.create_status_bar()
    
    def create_header(self):
        """Crear header moderno"""
        header_frame = tk.Frame(self.window, bg=self.colors['bg_primary'], height=80)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # Título principal
        title = tk.Label(header_frame, 
                        text="🔗 Database Connection", 
                        font=('Segoe UI', 18, 'bold'),
                        bg=self.colors['bg_primary'],
                        fg=self.colors['text_primary'])
        title.pack(pady=(20, 5))
        
        # Subtítulo
        subtitle = tk.Label(header_frame, 
                           text="Connect to XUI Database Server", 
                           font=('Segoe UI', 10),
                           bg=self.colors['bg_primary'],
                           fg=self.colors['text_secondary'])
        subtitle.pack()
    
    def create_connection_form(self):
        """Crear formulario de conexión"""
        # Frame principal del formulario
        form_frame = tk.Frame(self.window, bg=self.colors['bg_secondary'])
        form_frame.pack(fill='both', expand=True, padx=20, pady=(10, 0))
        
        # Título del formulario
        form_title = tk.Label(form_frame, 
                             text="⚙️ Connection Parameters", 
                             font=('Segoe UI', 12, 'bold'),
                             bg=self.colors['bg_secondary'],
                             fg=self.colors['accent_blue'])
        form_title.pack(pady=(15, 10))
        
        # Frame para los campos
        fields_frame = tk.Frame(form_frame, bg=self.colors['bg_secondary'])
        fields_frame.pack(padx=20, pady=10, fill='x')
        
        # Definir campos
        fields = [
            ("🌐 Host Address:", "**************"),
            ("👤 Username:", "infest84"),
            ("🔐 Password:", "v5XRWkgns4VBcYnxcJmlahbmGg5azpTS7FCVEuEDkk93BG7zFr"),
            ("💾 Database:", "xui"),
            ("🔌 Port:", "3306")
        ]
        
        self.entries = {}
        
        for i, (label, default) in enumerate(fields):
            # Frame para cada campo
            field_frame = tk.Frame(fields_frame, bg=self.colors['bg_secondary'])
            field_frame.pack(fill='x', pady=8)
            
            # Label
            lbl = tk.Label(field_frame, 
                          text=label, 
                          font=('Segoe UI', 9, 'bold'),
                          bg=self.colors['bg_secondary'],
                          fg=self.colors['text_primary'],
                          width=15, 
                          anchor='w')
            lbl.pack(side='left')
            
            # Entry con estilo moderno
            entry = tk.Entry(field_frame, 
                           font=('Consolas', 9),
                           bg=self.colors['bg_input'],
                           fg=self.colors['text_primary'],
                           insertbackground=self.colors['accent_blue'],
                           relief='flat',
                           bd=0,
                           width=35)
            
            if "Password" in label:
                entry.config(show='●')
            
            entry.pack(side='right', ipady=5)
            entry.insert(0, default)
            
            # Mapear keys para compatibilidad
            key_map = {
                'host address': 'host',
                'username': 'username', 
                'password': 'password',
                'database': 'database',
                'port': 'port'
            }
            
            raw_key = label.lower().replace("🌐 ", "").replace("👤 ", "").replace("🔐 ", "").replace("💾 ", "").replace("🔌 ", "").replace(":", "")
            mapped_key = key_map.get(raw_key, raw_key)
            self.entries[mapped_key] = entry
    
    def create_action_buttons(self):
        """Crear botones de acción"""
        button_frame = tk.Frame(self.window, bg=self.colors['bg_primary'], height=70)
        button_frame.pack(fill='x', padx=20, pady=10)
        button_frame.pack_propagate(False)
        
        # Frame centrado para botones
        btn_container = tk.Frame(button_frame, bg=self.colors['bg_primary'])
        btn_container.pack(expand=True)
        
        # Test Button
        test_btn = tk.Button(btn_container,
                            text="⚡ Test Connection",
                            command=self.test_connection,
                            font=('Segoe UI', 10, 'bold'),
                            bg=self.colors['accent_orange'],
                            fg='white',
                            relief='flat',
                            bd=0,
                            padx=20,
                            pady=8,
                            cursor='hand2')
        test_btn.pack(side='left', padx=(0, 10))
        
        # Connect Button
        connect_btn = tk.Button(btn_container,
                               text="🔗 Connect Now",
                               command=self.connect,
                               font=('Segoe UI', 10, 'bold'),
                               bg=self.colors['accent_green'],
                               fg='white',
                               relief='flat',
                               bd=0,
                               padx=20,
                               pady=8,
                               cursor='hand2')
        connect_btn.pack(side='left', padx=5)
        
        # Cancel Button
        cancel_btn = tk.Button(btn_container,
                              text="❌ Cancel",
                              command=self.cancel,
                              font=('Segoe UI', 10, 'bold'),
                              bg=self.colors['accent_red'],
                              fg='white',
                              relief='flat',
                              bd=0,
                              padx=20,
                              pady=8,
                              cursor='hand2')
        cancel_btn.pack(side='left', padx=(10, 0))
    
    def create_status_bar(self):
        """Crear barra de estado"""
        status_frame = tk.Frame(self.window, bg=self.colors['bg_secondary'], height=35)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(status_frame,
                                   text="🔰 Ready to connect - Fill credentials and click Connect Now",
                                   font=('Segoe UI', 8),
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['text_secondary'])
        self.status_label.pack(pady=8)
    
    def update_status(self, message, color=None):
        """Actualizar mensaje de estado"""
        if color is None:
            color = self.colors['text_secondary']
        self.status_label.config(text=message, fg=color)
        self.window.update()
    
    def test_connection(self):
        """Probar conexión (simulado, sin threading)"""
        self.update_status("⏳ Testing connection...", self.colors['accent_orange'])
        
        # Simular delay sin bloquear UI
        self.window.after(1000, self._test_connection_result)
    
    def _test_connection_result(self):
        """Resultado del test de conexión"""
        # Validar campos
        if not all(entry.get().strip() for entry in self.entries.values()):
            messagebox.showerror("❌ Validation Error", 
                               "Please fill all required fields before testing")
            self.update_status("❌ Test failed - Missing fields", self.colors['accent_red'])
            return
        
        # Simular test exitoso
        messagebox.showinfo("✅ Connection Test", 
                           "✅ Connection Successful!\n\n"
                           "• Host is reachable\n"
                           "• Credentials are valid\n"
                           "• Database is accessible\n\n"
                           "Ready to connect!")
        
        self.update_status("✅ Test successful - Ready to connect", self.colors['accent_green'])
    
    def connect(self):
        """Conectar a la base de datos"""
        self.update_status("🔗 Establishing connection...", self.colors['accent_blue'])
        
        # Obtener datos
        data = {}
        for key, entry in self.entries.items():
            data[key] = entry.get().strip()
        
        # Validar campos requeridos
        if not all(data.values()):
            messagebox.showerror("❌ Validation Error", 
                               "Please fill all required fields")
            self.update_status("❌ Connection failed - Missing fields", self.colors['accent_red'])
            return
        
        # Convertir puerto a int
        try:
            data['port'] = int(data['port'])
        except ValueError:
            messagebox.showerror("❌ Invalid Port", 
                               "Port must be a valid number")
            self.update_status("❌ Connection failed - Invalid port", self.colors['accent_red'])
            return
        
        self.result = data
        
        # Ejecutar callback si existe
        if self.callback:
            try:
                self.callback(data)
            except Exception as e:
                messagebox.showerror("❌ Connection Error", f"Connection failed:\n{str(e)}")
                self.update_status("❌ Connection failed - See error dialog", self.colors['accent_red'])
                return
        
        self.update_status("✅ Connected successfully!", self.colors['accent_green'])
        self.window.after(500, self.window.destroy)
    
    def cancel(self):
        """Cancelar conexión"""
        self.result = None
        self.window.destroy()
    
    def show(self):
        """Mostrar diálogo y retornar resultado"""
        self.window.mainloop()
        return self.result

# Función de compatibilidad
def show_simple_connection_dialog(parent=None, config_manager=None, callback=None):
    """Función de compatibilidad para mostrar diálogo"""
    dialog = SimpleConnectionDialog(parent, config_manager, callback)
    return dialog.show()

if __name__ == "__main__":
    print("🔗 Modern Simple Connection Dialog")
    print("Testing standalone mode...")
    
    dialog = SimpleConnectionDialog()
    result = dialog.show()
    
    if result:
        print(f"✅ Connection established: {result}")
    else:
        print("❌ Connection cancelled")
