# Guía de Instalación - XUI Database Manager

## Requisitos Previos

### 1. Instalar Python 3.7 o superior

**Para Windows:**
1. Visita https://www.python.org/downloads/
2. Descarga la última versión de Python 3.x
3. Durante la instalación, **IMPORTANTE**: <PERSON><PERSON> casilla "Add Python to PATH"
4. Completa la instalación

**Para verificar la instalación:**
```cmd
python --version
```
o
```cmd
py --version
```

### 2. Verificar pip (gestor de paquetes)
```cmd
pip --version
```

## Instalación de la Aplicación

### Opción 1: Instalación Automática (Windows)
1. Ejecuta el archivo `install.bat` como administrador
2. El script instalará automáticamente todas las dependencias

### Opción 2: Instalación Manual

1. **Abrir terminal/cmd en el directorio del proyecto**

2. **Instalar dependencias:**
```cmd
pip install -r requirements.txt
```

3. **Verificar instalación:**
```cmd
python test_connection.py
```

## Generar Ejecutable

### Opción 1: Script Automático
```cmd
python build_exe.py
```

### Opción 2: Manual con PyInstaller
```cmd
pip install pyinstaller
pyinstaller xui_manager.spec
```

El ejecutable se generará en: `dist/XUI_Database_Manager.exe`

## Estructura de Archivos

```
XUI_Database_Manager/
├── main.py                 # Archivo principal
├── gui.py                  # Interfaz gráfica
├── database.py             # Gestión de base de datos
├── requirements.txt        # Dependencias Python
├── build_exe.py           # Script para generar ejecutable
├── test_connection.py     # Script de prueba de conexión
├── install.bat            # Instalador automático (Windows)
├── xui_manager.spec       # Configuración PyInstaller
├── version_info.txt       # Información de versión
├── config_example.ini     # Ejemplo de configuración
├── README.md              # Documentación principal
├── INSTALACION.md         # Esta guía
└── dist/                  # Directorio del ejecutable (después de build)
    └── XUI_Database_Manager.exe
```

## Uso de la Aplicación

### 1. Ejecutar desde código fuente
```cmd
python main.py
```

### 2. Ejecutar desde ejecutable
Doble clic en `XUI_Database_Manager.exe`

### 3. Probar conexión a base de datos
```cmd
python test_connection.py
```

## Configuración de Base de Datos

La aplicación se conecta a MariaDB con los siguientes parámetros:

- **Host**: IP del servidor MariaDB
- **Puerto**: 3306 (por defecto)
- **Usuario**: Tu usuario de base de datos
- **Contraseña**: Tu contraseña
- **Base de Datos**: xui (por defecto)

### Tablas Requeridas

La aplicación espera encontrar estas tablas en la base de datos XUI:

1. `streams_series` - Series (id, title)
2. `streams_episodes` - Episodios (streams_id, series_id)
3. `streams` - Streams (id, type)
4. `streams_types` - Tipos de contenido (type_id, type_name)

## Solución de Problemas

### Error: "python no se reconoce como comando"
- Python no está instalado o no está en el PATH
- Reinstala Python marcando "Add to PATH"
- Reinicia la terminal/cmd

### Error de conexión a base de datos
- Verifica que MariaDB esté ejecutándose
- Comprueba las credenciales
- Verifica que el puerto 3306 esté abierto
- Confirma que la base de datos 'xui' existe

### Error al instalar dependencias
```cmd
pip install --upgrade pip
pip install -r requirements.txt --user
```

### Error al generar ejecutable
```cmd
pip install --upgrade pyinstaller
python build_exe.py
```

### Problemas de permisos
- Ejecuta cmd como administrador
- En Linux/Mac: usa `sudo` cuando sea necesario

## Funcionalidades Principales

1. **Episodios Huérfanos**: Detecta y gestiona episodios sin serie asignada
2. **Series Vacías**: Encuentra series sin episodios
3. **Gestión de Series**: Visualiza series con conteo de episodios
4. **Películas Duplicadas**: Detecta contenido duplicado

## Operaciones Disponibles

- **Asignar episodios** a series existentes
- **Crear nuevas series** para episodios huérfanos
- **Eliminar episodios** huérfanos
- **Eliminar series** vacías
- **Visualizar estadísticas** de la base de datos

## Seguridad

- Las credenciales no se almacenan permanentemente
- Todas las operaciones destructivas requieren confirmación
- Se mantiene un log de operaciones en `xui_manager.log`

## Soporte

Para problemas o preguntas:
1. Revisa esta guía de instalación
2. Ejecuta `python test_connection.py` para diagnosticar problemas de conexión
3. Revisa el archivo de log `xui_manager.log`
4. Verifica que todas las dependencias estén instaladas correctamente
