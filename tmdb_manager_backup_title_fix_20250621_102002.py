#!/usr/bin/env python3
"""
Gestor TMDB para XUI Database Manager
Maneja la integración con TMDB API y detección de duplicados
VERSIÓN TEMPORAL SIN REQUESTS - Para pruebas de interfaz
"""

import requests
import json
import logging
from typing import Dict, List, Optional
import time

class TMDBManager:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.themoviedb.org/3"
        # self.session = requests.Session()  # Comentado temporalmente
        # No usar headers de autorización, usar api_key en parámetros

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.25  # 4 requests per second max
    
    def _rate_limit(self):
        """Aplicar rate limiting para respetar límites de TMDB API"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last)
        self.last_request_time = time.time()
    
    def get_movie_details(self, tmdb_id: int) -> Optional[Dict]:
        """Obtener detalles de película desde TMDB - VERSIÓN MOCK"""
        try:
            self._rate_limit()
            # MOCK: Retornar datos simulados para pruebas
            return {
                'id': tmdb_id,
                'title': f'Mock Movie {tmdb_id}',
                'original_title': f'Mock Movie {tmdb_id}',
                'release_date': '2020-01-01',
                'overview': 'Mock movie details for testing interface',
                'poster_path': '/mock_poster.jpg',
                'backdrop_path': '/mock_backdrop.jpg',
                'vote_average': 7.5,
                'vote_count': 1000,
                'runtime': 120,
                'genres': [{'name': 'Action'}, {'name': 'Drama'}],
                'production_countries': [{'name': 'United States'}]
            }

        except Exception as e:
            logging.error(f"Error inesperado con TMDB {tmdb_id}: {e}")
            return None
    
    def get_tv_details(self, tmdb_id: int) -> Optional[Dict]:
        """Obtener detalles de serie desde TMDB"""
        try:
            self._rate_limit()

            # Obtener detalles básicos de la serie
            url = f"{self.base_url}/tv/{tmdb_id}"
            params = {
                'api_key': self.api_key,
                'language': 'es-MX',
                'append_to_response': 'credits,videos,similar'
            }

            logging.info(f"Obteniendo detalles TMDB para serie ID: {tmdb_id}")
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()

            return response.json()

        except requests.exceptions.RequestException as e:
            logging.error(f"Error de conexión obteniendo detalles de serie {tmdb_id}: {e}")
            # Fallback a datos mock
            return self._get_mock_tv_details(tmdb_id)
        except Exception as e:
            logging.error(f"Error inesperado con TMDB {tmdb_id}: {e}")
            return self._get_mock_tv_details(tmdb_id)

    def _get_mock_tv_details(self, tmdb_id: int) -> Dict:
        """Datos mock de fallback para series"""
        poster_path = f"/mock_poster_{tmdb_id}.jpg"
        backdrop_path = f"/mock_backdrop_{tmdb_id}.jpg"

        return {
                'id': tmdb_id,
                'name': f'Mock Series {tmdb_id}',
                'original_name': f'Mock Series {tmdb_id}',
                'first_air_date': '2020-01-01',
                'last_air_date': '2023-01-01',
                'overview': f'Mock series details for testing interface. This is a comprehensive description for series {tmdb_id} with detailed plot information.',
                'poster_path': poster_path,
                'backdrop_path': backdrop_path,
                'vote_average': 8.2,
                'vote_count': 2000,
                'number_of_seasons': 3,
                'number_of_episodes': 30,
                'episode_run_time': [45, 50],
                'genres': [
                    {'id': 18, 'name': 'Drama'},
                    {'id': 53, 'name': 'Thriller'},
                    {'id': 80, 'name': 'Crime'}
                ],
                'production_countries': [{'iso_3166_1': 'US', 'name': 'United States'}],
                'created_by': [{'name': 'Mock Creator'}],
                'networks': [{'name': 'Mock Network'}],
                'status': 'Ended',
                'type': 'Scripted',
                'languages': ['en'],
                'origin_country': ['US'],
                'popularity': 85.5,
                'seasons': [
                    {
                        'air_date': '2020-01-01',
                        'episode_count': 10,
                        'id': tmdb_id * 100 + 1,
                        'name': 'Season 1',
                        'overview': 'First season overview',
                        'season_number': 1,
                        'vote_average': 8.3,
                        'poster_path': poster_path
                    },
                    {
                        'air_date': '2021-01-01',
                        'episode_count': 10,
                        'id': tmdb_id * 100 + 2,
                        'name': 'Season 2',
                        'overview': 'Second season overview',
                        'season_number': 2,
                        'vote_average': 8.1,
                        'poster_path': poster_path
                    },
                    {
                        'air_date': '2022-01-01',
                        'episode_count': 10,
                        'id': tmdb_id * 100 + 3,
                        'name': 'Season 3',
                        'overview': 'Third season overview',
                        'season_number': 3,
                        'vote_average': 8.5,
                        'poster_path': poster_path
                    }
                ],
                'videos': {
                    'results': [
                        {
                            'key': f'mock_trailer_{tmdb_id}',
                            'name': f'Mock Trailer {tmdb_id}',
                            'site': 'YouTube',
                            'type': 'Trailer'
                        }
                    ]
                }
            }
    
    def search_movie(self, title: str, year: Optional[int] = None) -> List[Dict]:
        """Buscar película en TMDB - VERSIÓN MOCK"""
        try:
            self._rate_limit()
            # MOCK: Retornar resultados simulados para pruebas
            return [
                {
                    'id': 12345,
                    'title': f'{title} (Mock Result 1)',
                    'original_title': f'{title} Original',
                    'release_date': f'{year or 2020}-01-01',
                    'overview': f'Mock search result for "{title}". This is a test result.',
                    'poster_path': '/mock_poster1.jpg',
                    'backdrop_path': '/mock_backdrop1.jpg',
                    'vote_average': 8.5,
                    'popularity': 100.0
                },
                {
                    'id': 67890,
                    'title': f'{title} 2 (Mock Result 2)',
                    'original_title': f'{title} 2 Original',
                    'release_date': f'{(year or 2020) + 1}-01-01',
                    'overview': f'Another mock search result for "{title}". Second test result.',
                    'poster_path': '/mock_poster2.jpg',
                    'backdrop_path': '/mock_backdrop2.jpg',
                    'vote_average': 7.2,
                    'popularity': 85.5
                }
            ]

        except Exception as e:
            logging.error(f"Error inesperado buscando '{title}': {e}")
            return []
    
    def search_tv(self, title: str, year: Optional[int] = None) -> List[Dict]:
        """Buscar serie en TMDB"""
        try:
            self._rate_limit()

            # Limpiar el título para búsqueda
            clean_title = self._clean_search_title(title)

            # Debug: mostrar limpieza del título
            if title != clean_title:
                logging.info(f"Título limpiado: '{title}' → '{clean_title}'")

            url = f"{self.base_url}/search/tv"
            params = {
                'api_key': self.api_key,
                'query': clean_title,
                'language': 'es-MX',
                'include_adult': False
            }

            if year:
                params['first_air_date_year'] = year

            logging.info(f"Buscando serie en TMDB: '{clean_title}'" + (f" (año: {year})" if year else ""))
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            results = data.get('results', [])

            # Filtrar y ordenar resultados por relevancia
            filtered_results = []
            for result in results[:10]:  # Limitar a 10 resultados
                # Calcular score de relevancia
                relevance_score = self._calculate_relevance_score(clean_title, result)
                result['relevance_score'] = relevance_score
                filtered_results.append(result)

            # Ordenar por score de relevancia
            filtered_results.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)

            logging.info(f"Encontrados {len(filtered_results)} resultados para '{clean_title}'")
            return filtered_results[:5]  # Retornar top 5

        except requests.exceptions.RequestException as e:
            logging.error(f"Error de conexión buscando '{title}': {e}")
            return []
        except Exception as e:
            logging.error(f"Error inesperado buscando '{title}': {e}")
            return []

    def _clean_search_title(self, title: str) -> str:
        """Limpiar título para búsqueda más efectiva"""
        import re

        # PRIMERO: Remover información TMDB del título
        clean = re.sub(r'\(TMDB:\s*\d+\)', '', title)

        # Remover caracteres especiales y números de temporada/episodio
        clean = re.sub(r'[^\w\s]', ' ', clean)
        clean = re.sub(r'\b(S\d+|Season\s+\d+|Temporada\s+\d+)\b', '', clean, flags=re.IGNORECASE)
        clean = re.sub(r'\b(E\d+|Episode\s+\d+|Episodio\s+\d+)\b', '', clean, flags=re.IGNORECASE)
        clean = re.sub(r'\b\d{4}\b', '', clean)  # Remover años
        clean = re.sub(r'\s+', ' ', clean).strip()

        return clean

    def _calculate_relevance_score(self, search_title: str, result: Dict) -> float:
        """Calcular score de relevancia para ordenar resultados"""
        score = 0.0

        result_name = result.get('name', '').lower()
        original_name = result.get('original_name', '').lower()
        search_lower = search_title.lower()

        # Coincidencia exacta del título
        if search_lower == result_name:
            score += 100
        elif search_lower == original_name:
            score += 95

        # Coincidencia parcial
        if search_lower in result_name:
            score += 50
        elif search_lower in original_name:
            score += 45

        # Palabras clave coincidentes
        search_words = set(search_lower.split())
        result_words = set(result_name.split())
        common_words = search_words.intersection(result_words)
        score += len(common_words) * 10

        # Bonus por popularidad y rating
        score += result.get('popularity', 0) * 0.1
        score += result.get('vote_average', 0) * 2

        # Penalty por resultados muy antiguos o muy nuevos
        first_air_date = result.get('first_air_date', '')
        if first_air_date:
            try:
                from datetime import datetime
                air_year = int(first_air_date[:4])
                current_year = datetime.now().year
                year_diff = abs(current_year - air_year)
                if year_diff > 20:
                    score -= year_diff * 0.5
            except:
                pass

        return score

    def get_tv_season_details(self, tmdb_id: int, season_number: int) -> Optional[Dict]:
        """Obtener detalles de temporada desde TMDB"""
        try:
            self._rate_limit()

            url = f"{self.base_url}/tv/{tmdb_id}/season/{season_number}"
            params = {
                'api_key': self.api_key,
                'language': 'es-MX'
            }

            logging.info(f"Obteniendo temporada {season_number} de serie TMDB ID: {tmdb_id}")
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()

            return response.json()

        except requests.exceptions.RequestException as e:
            logging.error(f"Error de conexión obteniendo temporada {season_number} de {tmdb_id}: {e}")
            # Fallback a datos mock
            return self._get_mock_season_details(tmdb_id, season_number)
        except Exception as e:
            logging.error(f"Error inesperado obteniendo temporada {season_number} de {tmdb_id}: {e}")
            return self._get_mock_season_details(tmdb_id, season_number)

    def _get_mock_season_details(self, tmdb_id: int, season_number: int) -> Dict:
        """Datos mock de fallback para temporadas"""
        episodes = []
        for ep_num in range(1, 11):  # 10 episodios por temporada
            episodes.append({
                'id': tmdb_id * 1000 + season_number * 100 + ep_num,
                'episode_number': ep_num,
                'season_number': season_number,
                'name': f'Episode {ep_num}',
                'overview': f'Mock episode {ep_num} of season {season_number}',
                'air_date': f'2020-{season_number:02d}-{ep_num:02d}',
                'runtime': 45,
                'vote_average': 7.5 + (ep_num * 0.1)
            })

        return {
            'id': tmdb_id * 100 + season_number,
            'season_number': season_number,
            'name': f'Season {season_number}',
            'overview': f'Mock season {season_number} overview',
            'air_date': f'2020-{season_number:02d}-01',
            'episode_count': len(episodes),
            'episodes': episodes
        }

    def get_all_tv_episodes(self, tmdb_id: int) -> List[Dict]:
        """Obtener todos los episodios de una serie desde TMDB"""
        try:
            # Primero obtener detalles de la serie para saber cuántas temporadas tiene
            tv_details = self.get_tv_details(tmdb_id)
            if not tv_details:
                return []

            # Obtener número real de temporadas
            seasons = tv_details.get('seasons', [])
            all_episodes = []

            for season in seasons:
                season_number = season.get('season_number', 0)

                # Skip season 0 (specials) por ahora
                if season_number == 0:
                    continue

                logging.info(f"Obteniendo episodios de temporada {season_number} para serie {tmdb_id}")
                season_details = self.get_tv_season_details(tmdb_id, season_number)

                if season_details and 'episodes' in season_details:
                    episodes = season_details['episodes']
                    logging.info(f"Encontrados {len(episodes)} episodios en temporada {season_number}")
                    all_episodes.extend(episodes)

            logging.info(f"Total de episodios obtenidos para serie {tmdb_id}: {len(all_episodes)}")
            return all_episodes

        except Exception as e:
            logging.error(f"Error obteniendo todos los episodios de {tmdb_id}: {e}")
            return []

    def get_image_url(self, path: str, size: str = "w600_and_h900_bestv2") -> str:
        """Construir URL completa de imagen TMDB"""
        if not path:
            return ""

        # Para mock, simular URLs de TMDB
        base_url = "https://image.tmdb.org/t/p/"
        return f"{base_url}{size}{path}"

    def get_backdrop_image_url(self, path: str, size: str = "w1280") -> str:
        """Construir URL completa de backdrop TMDB"""
        if not path:
            return ""

        # Para mock, simular URLs de TMDB
        base_url = "https://image.tmdb.org/t/p/"
        return f"{base_url}{size}{path}"
    
    def get_movie_poster_url(self, poster_path: str, size: str = "w500") -> str:
        """Obtener URL completa del poster"""
        if not poster_path:
            return ""
        return f"https://image.tmdb.org/t/p/{size}{poster_path}"
    
    def get_backdrop_url(self, backdrop_path: str, size: str = "w1280") -> str:
        """Obtener URL completa del backdrop"""
        if not backdrop_path:
            return ""
        return f"https://image.tmdb.org/t/p/{size}{backdrop_path}"
    
    def extract_tmdb_id_from_properties(self, movie_properties: str) -> Optional[int]:
        """Extraer TMDB ID desde movie_properties JSON"""
        try:
            if not movie_properties:
                return None
            
            props = json.loads(movie_properties)
            
            # Buscar TMDB ID en diferentes campos posibles
            tmdb_id = props.get('tmdb_id') or props.get('tmdb') or props.get('id')
            
            if tmdb_id:
                return int(tmdb_id)
            
            return None
            
        except (json.JSONDecodeError, ValueError, TypeError):
            return None
    
    def format_movie_info(self, tmdb_data: Dict) -> Dict:
        """Formatear información de película para mostrar"""
        return {
            'tmdb_id': tmdb_data.get('id'),
            'title': tmdb_data.get('title', ''),
            'original_title': tmdb_data.get('original_title', ''),
            'release_date': tmdb_data.get('release_date', ''),
            'overview': tmdb_data.get('overview', ''),
            'poster_url': self.get_movie_poster_url(tmdb_data.get('poster_path', '')),
            'backdrop_url': self.get_backdrop_url(tmdb_data.get('backdrop_path', '')),
            'vote_average': tmdb_data.get('vote_average', 0),
            'vote_count': tmdb_data.get('vote_count', 0),
            'runtime': tmdb_data.get('runtime', 0),
            'genres': [g['name'] for g in tmdb_data.get('genres', [])],
            'production_countries': [c['name'] for c in tmdb_data.get('production_countries', [])]
        }
    
    def format_tv_info(self, tmdb_data: Dict) -> Dict:
        """Formatear información de serie para mostrar"""
        return {
            'tmdb_id': tmdb_data.get('id'),
            'name': tmdb_data.get('name', ''),
            'original_name': tmdb_data.get('original_name', ''),
            'first_air_date': tmdb_data.get('first_air_date', ''),
            'last_air_date': tmdb_data.get('last_air_date', ''),
            'overview': tmdb_data.get('overview', ''),
            'poster_url': self.get_movie_poster_url(tmdb_data.get('poster_path', '')),
            'backdrop_url': self.get_backdrop_url(tmdb_data.get('backdrop_path', '')),
            'vote_average': tmdb_data.get('vote_average', 0),
            'vote_count': tmdb_data.get('vote_count', 0),
            'number_of_seasons': tmdb_data.get('number_of_seasons', 0),
            'number_of_episodes': tmdb_data.get('number_of_episodes', 0),
            'genres': [g['name'] for g in tmdb_data.get('genres', [])],
            'production_countries': [c['name'] for c in tmdb_data.get('production_countries', [])]
        }
