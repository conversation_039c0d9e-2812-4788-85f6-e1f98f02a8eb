# Manual de Gestión de Películas - XUI Database Manager

## 🎬 Funcionalidades de Gestión de Películas

### **Pestaña: "Gestión de Películas"**

Esta pestaña te permite gestionar completamente las películas en tu base de datos XUI.

---

## 🔍 **Funciones Disponibles**

### **1. Actualizar Lista**
- **Función**: Carga todas las películas de la base de datos
- **Uso**: Click en "Actualizar Lista"
- **Resultado**: Muestra todas las 23,638+ películas con ID, título, tipo ID y tipo

### **2. Buscar Películas**
- **Función**: Busca películas por nombre (búsqueda parcial)
- **Uso**: 
  1. Escribe el término en el campo "Buscar película"
  2. Click en "Buscar"
- **Ejemplos**:
  - "<PERSON>" → Encuentra "Robin Hood", "Batman & Robin", etc.
  - "Matrix" → Encuentra "Matrix", "Animatrix", etc.
  - "Avengers" → Encuentra todas las películas de Avengers

### **3. Ver Duplicados**
- **Función**: Muestra todas las copias de una película específica
- **Uso**:
  1. Selecciona una película en la lista
  2. Click en "Ver Duplicados"
- **Resultado**: Ventana emergente con todas las copias de esa película

### **4. Películas Sin Título** ⭐ **NUEVA FUNCIONALIDAD**
- **Función**: Muestra las 1,273+ películas sin título para limpieza
- **Uso**: Click en "Películas Sin Título"
- **Resultado**: Lista de películas que necesitan limpieza

---

## 🗑️ **Eliminación de Películas**

### **Selección Múltiple** ⭐ **NUEVA FUNCIONALIDAD**

#### **Cómo Seleccionar:**
- **Una película**: Click normal
- **Múltiples películas**: 
  - `Ctrl + Click` para seleccionar películas individuales
  - `Shift + Click` para seleccionar un rango
- **Todas las películas**: Click en "Seleccionar Todas"

#### **Cómo Eliminar:**
1. Selecciona las películas que quieres eliminar
2. Click en "Eliminar Seleccionadas"
3. Confirma la eliminación
4. El sistema eliminará automáticamente

### **Eliminación Inteligente:**
- **1-10 películas**: Eliminación individual (más detallada)
- **11+ películas**: Eliminación en lote (más rápida)
- **Verificación**: Solo elimina películas válidas (tipo Movies)
- **Reporte**: Muestra cuántas se eliminaron exitosamente

---

## 📊 **Estadísticas Actuales**

### **Por Tipo de Contenido:**
- **TV Series**: 132,464 (0 sin título)
- **Movies**: 23,638 (1,273 sin título) ⚠️
- **Live Streams**: 1,434 (0 sin título)
- **Radio Stations**: 3 (0 sin título)

### **Duplicados Identificados:**
- **Robin Hood**: 9 copias
- **Lego**: 8 copias
- **Matrix**: 3 copias
- **Avengers**: 22+ resultados

---

## 🎯 **Casos de Uso Recomendados**

### **1. Limpieza de Películas Sin Título**
```
1. Click en "Películas Sin Título"
2. Click en "Seleccionar Todas" (o selecciona manualmente)
3. Click en "Eliminar Seleccionadas"
4. Confirma la eliminación
5. ¡1,273 películas basura eliminadas!
```

### **2. Eliminar Duplicados**
```
1. Busca una película (ej: "Robin Hood")
2. Selecciona las copias que no necesitas
3. Click en "Eliminar Seleccionadas"
4. Mantén solo la mejor calidad
```

### **3. Búsqueda y Limpieza**
```
1. Busca por término (ej: "test", "sample")
2. Selecciona películas de prueba/basura
3. Elimina en lote
4. Base de datos más limpia
```

---

## ⚠️ **Precauciones**

### **Antes de Eliminar:**
- ✅ **Haz backup** de tu base de datos
- ✅ **Verifica** que seleccionaste las películas correctas
- ✅ **Confirma** que no necesitas esas películas

### **La Eliminación es Permanente:**
- ❌ No se puede deshacer
- ❌ Los archivos físicos no se eliminan (solo registros de BD)
- ✅ Solo elimina registros de la tabla `streams`

---

## 🚀 **Beneficios**

### **Base de Datos Más Limpia:**
- Menos películas duplicadas
- Sin películas sin título
- Mejor organización
- Búsquedas más rápidas

### **Gestión Eficiente:**
- Selección múltiple
- Eliminación en lote
- Verificación automática
- Reportes detallados

---

## 📞 **Soporte**

Si tienes problemas:
1. Verifica la conexión a la base de datos
2. Revisa los logs en `xui_manager.log`
3. Haz backup antes de operaciones masivas
4. Prueba con pocas películas primero

**¡Disfruta de tu base de datos XUI más limpia y organizada!** 🎉
