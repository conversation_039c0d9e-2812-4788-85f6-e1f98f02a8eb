#!/usr/bin/env python3
"""
Debug script para analizar M3U real y verificar detección
"""

import sys
import os
from m3u_manager import M3<PERSON><PERSON><PERSON>

def analyze_real_m3u():
    """Analizar M3U real para debug"""
    print("🔍 ANALYZING REAL M3U FILE")
    print("=" * 50)
    
    # Buscar archivos M3U en el directorio
    m3u_files = []
    for file in os.listdir('.'):
        if file.endswith('.m3u'):
            m3u_files.append(file)
    
    if not m3u_files:
        print("❌ No M3U files found in current directory")
        return
    
    print(f"📁 Found M3U files: {m3u_files}")
    
    # Usar el primer archivo M3U encontrado
    m3u_file = m3u_files[0]
    print(f"📁 Analyzing: {m3u_file}")
    
    manager = M3UManager()
    
    try:
        # Cargar M3U
        entries = manager.parse_m3u_file(m3u_file)
        print(f"📋 Loaded {len(entries)} entries")
        
        # Obtener estadísticas
        stats = manager.get_m3u_statistics(entries)
        
        print(f"\n📊 STATISTICS:")
        print(f"   📺 Total Entries: {stats['total_entries']}")
        print(f"   🎬 Series Entries: {stats['series_entries']}")
        print(f"   🎭 Movie Entries: {stats['movie_entries']}")
        print(f"   📁 Groups: {len(stats['groups'])}")
        
        # Mostrar primeras 10 entradas con detección
        print(f"\n📋 FIRST 10 ENTRIES WITH DETECTION:")
        print("-" * 60)
        
        for i, entry in enumerate(entries[:10]):
            title = entry.get('title', 'N/A')
            is_series = manager._is_series_entry(entry)
            detected = 'SERIES' if is_series else 'MOVIE'
            
            print(f"{i+1:2d}. {detected:6s} | {title}")
            
            if is_series:
                info = manager.extract_series_info(entry)
                series_title = info.get('series_title', 'N/A')
                season = info.get('season', 'N/A')
                episode = info.get('episode', 'N/A')
                if season != 'N/A' and episode != 'N/A':
                    print(f"      Series: '{series_title}' S{season:02d}E{episode:02d}")
                else:
                    print(f"      Series: '{series_title}' (no episode info)")
        
        # Verificar si hay problemas de detección
        print(f"\n🔍 DETECTION ANALYSIS:")
        
        # Contar manualmente
        manual_series = 0
        manual_movies = 0
        
        for entry in entries:
            title = entry.get('title', '')
            
            # Buscar patrones de series manualmente
            has_season_episode = any([
                'S0' in title and 'E0' in title,
                'S1' in title and 'E' in title,
                'x0' in title,
                'Season' in title,
                'Episode' in title
            ])
            
            if has_season_episode:
                manual_series += 1
            else:
                manual_movies += 1
        
        print(f"   🤖 Manager Detection: {stats['series_entries']} series, {stats['movie_entries']} movies")
        print(f"   👤 Manual Detection: {manual_series} series, {manual_movies} movies")
        
        if stats['series_entries'] != manual_series:
            print(f"   ⚠️  MISMATCH in series detection!")
        else:
            print(f"   ✅ Series detection matches")
            
        if stats['movie_entries'] != manual_movies:
            print(f"   ⚠️  MISMATCH in movie detection!")
        else:
            print(f"   ✅ Movie detection matches")
        
        # Mostrar grupos encontrados
        if stats['groups']:
            print(f"\n📁 GROUPS FOUND:")
            for group in stats['groups']:
                print(f"   - {group}")
        
        # Buscar casos problemáticos
        print(f"\n🔍 POTENTIAL ISSUES:")
        issues_found = False
        
        for i, entry in enumerate(entries):
            title = entry.get('title', '')
            is_series = manager._is_series_entry(entry)
            
            # Buscar títulos que podrían ser mal clasificados
            if not is_series and ('S0' in title or 'E0' in title):
                print(f"   ❌ Possible missed series: {title}")
                issues_found = True
            elif is_series and not any(['S0' in title, 'E0' in title, 'x0' in title, 'Season' in title]):
                print(f"   ❌ Possible false series: {title}")
                issues_found = True
        
        if not issues_found:
            print(f"   ✅ No obvious detection issues found")
            
    except Exception as e:
        print(f"❌ Error analyzing M3U: {e}")

if __name__ == "__main__":
    analyze_real_m3u()
