#!/usr/bin/env python3
"""
Script para debuggear películas y duplicados
"""

import sys
from database import DatabaseManager

def debug_movies():
    """Debuggear películas"""
    print("=== Debug Películas ===\n")
    
    # Usar las credenciales que ya funcionaron
    host = "**************"
    user = "infest84"
    password = "GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP"
    database = "xui"
    port = 3306
    
    db = DatabaseManager()
    
    if db.connect(host, user, password, database, port):
        print("✓ Conexión exitosa!")
        
        # 1. Verificar tipos de stream disponibles
        print("\n1. Tipos de stream disponibles:")
        types = db.execute_query("SELECT type_id, type_name FROM streams_types ORDER BY type_id")
        for t in types:
            print(f"   {t['type_id']}: {t['type_name']}")
        
        # 2. Contar streams por tipo
        print("\n2. Conteo de streams por tipo:")
        count_by_type = db.execute_query("""
        SELECT s.type, st.type_name, COUNT(*) as count 
        FROM streams s 
        LEFT JOIN streams_types st ON s.type = st.type_id 
        GROUP BY s.type, st.type_name 
        ORDER BY s.type
        """)
        for row in count_by_type:
            print(f"   Tipo {row['type']} ({row['type_name']}): {row['count']} streams")
        
        # 3. Buscar películas (tipo 2)
        print("\n3. Películas (tipo 2):")
        movies_count = db.execute_query("SELECT COUNT(*) as count FROM streams WHERE type = 2")
        if movies_count:
            print(f"   Total películas: {movies_count[0]['count']}")
        
        # 4. Ejemplos de películas
        print("\n4. Ejemplos de películas (primeras 10):")
        movie_examples = db.execute_query("""
        SELECT id, stream_display_name, type 
        FROM streams 
        WHERE type = 2 
        LIMIT 10
        """)
        for movie in movie_examples:
            print(f"   ID: {movie['id']}, Título: {movie['stream_display_name']}")
        
        # 5. Buscar duplicados por nombre
        print("\n5. Buscando duplicados por stream_display_name:")
        duplicates_query = """
        SELECT stream_display_name, COUNT(*) as count 
        FROM streams 
        WHERE type = 2 AND stream_display_name IS NOT NULL AND stream_display_name != ''
        GROUP BY stream_display_name 
        HAVING COUNT(*) > 1 
        ORDER BY count DESC 
        LIMIT 10
        """
        duplicates = db.execute_query(duplicates_query)
        if duplicates:
            print(f"   Encontrados {len(duplicates)} grupos de películas con nombres duplicados:")
            for dup in duplicates:
                print(f"     '{dup['stream_display_name']}': {dup['count']} copias")
        else:
            print("   No se encontraron películas con nombres duplicados")
        
        # 6. Verificar si hay streams con nombres vacíos o NULL
        print("\n6. Streams con nombres vacíos o NULL:")
        empty_names = db.execute_query("""
        SELECT COUNT(*) as count 
        FROM streams 
        WHERE type = 2 AND (stream_display_name IS NULL OR stream_display_name = '')
        """)
        if empty_names:
            print(f"   Películas sin nombre: {empty_names[0]['count']}")
        
        # 7. Probar la consulta actual de duplicados
        print("\n7. Probando consulta actual de duplicados:")
        try:
            current_duplicates = db.get_duplicate_movies()
            print(f"   Resultado de get_duplicate_movies(): {len(current_duplicates)} grupos")
            if current_duplicates:
                for dup in current_duplicates[:5]:
                    print(f"     Stream ID: {dup.get('stream_id')}, Tipo: {dup.get('type')}, Duplicados: {dup.get('duplicate_count')}")
        except Exception as e:
            print(f"   Error en get_duplicate_movies(): {e}")
        
        db.disconnect()
        return True
        
    else:
        print("✗ Error de conexión")
        return False

def main():
    try:
        debug_movies()
        input("\nPresiona Enter para salir...")
    except Exception as e:
        print(f"\nError: {e}")

if __name__ == "__main__":
    main()
