# XUI Database Manager

Aplicación de escritorio para gestionar la base de datos XUI, permitiendo administrar series, episodios y películas de manera eficiente.

## Características

- **Conexión a MariaDB**: Conecta a tu servidor remoto MariaDB en el puerto 3306
- **Gestión de Episodios Huérfanos**: Detecta y gestiona episodios sin serie asignada
- **Series Sin Episodios**: Identifica series vacías que pueden ser eliminadas
- **Gestión de Series**: Visualiza todas las series con sus episodios
- **Películas Duplicadas**: Detecta películas duplicadas en el sistema
- **Interfaz Gráfica**: Interfaz intuitiva y fácil de usar

## Estructura de Base de Datos

La aplicación trabaja con las siguientes tablas de XUI:

- `streams_series`: Contiene las series (id, title)
- `streams_episodes`: Relaciona episodios con series (streams_id, series_id)
- `streams`: Contiene los streams (id, type)
- `streams_types`: Define los tipos de contenido (type_id, type_name)

## Instalación y Uso

### Opción 1: Ejecutar desde código fuente

1. **Instalar Python 3.7+**
2. **Instalar dependencias**:
   ```bash
   pip install -r requirements.txt
   ```
3. **Ejecutar la aplicación**:
   ```bash
   python main.py
   ```

### Opción 2: Generar ejecutable

1. **Ejecutar el script de construcción**:
   ```bash
   python build_exe.py
   ```
2. **El ejecutable se generará en**: `dist/XUI_Database_Manager.exe`

## Funcionalidades Principales

### 1. Conexión a Base de Datos
- Host: IP del servidor MariaDB
- Usuario: Usuario de la base de datos
- Contraseña: Contraseña del usuario
- Base de Datos: Nombre de la base de datos (por defecto: xui)
- Puerto: Puerto de conexión (por defecto: 3306)

### 2. Episodios Huérfanos
- **Detectar**: Encuentra episodios sin serie asignada
- **Asignar**: Asigna episodios a series existentes
- **Crear Serie**: Crea nueva serie y asigna el episodio
- **Eliminar**: Elimina episodios huérfanos

### 3. Series Sin Episodios
- **Detectar**: Encuentra series que no tienen episodios
- **Eliminar**: Elimina series vacías

### 4. Gestión de Series
- **Visualizar**: Muestra todas las series con conteo de episodios
- **Filtrar**: Por tipo de contenido (TV Shows, Movies, Live)

### 5. Películas Duplicadas
- **Detectar**: Encuentra películas con títulos duplicados
- **Gestionar**: Permite identificar y gestionar duplicados

## Casos de Uso

### Limpieza de Base de Datos
1. **Episodios Huérfanos**: Asigna episodios perdidos a sus series correspondientes
2. **Series Vacías**: Elimina series que no tienen episodios
3. **Duplicados**: Identifica contenido duplicado para limpieza manual

### Gestión de Contenido Nuevo
1. **Nuevas Series**: Crea series para episodios nuevos
2. **Organización**: Mantiene la estructura correcta de la base de datos
3. **Validación**: Asegura que todos los episodios estén correctamente asignados

## Estructura del Proyecto

```
XUI_Database_Manager/
├── main.py              # Archivo principal
├── gui.py               # Interfaz gráfica
├── database.py          # Gestión de base de datos
├── requirements.txt     # Dependencias
├── build_exe.py         # Script para generar ejecutable
├── README.md           # Este archivo
└── dist/               # Directorio del ejecutable (después de build)
    └── XUI_Database_Manager.exe
```

## Dependencias

- `PyMySQL`: Conexión a MariaDB/MySQL
- `tkinter`: Interfaz gráfica (incluido en Python)
- `Pillow`: Manejo de imágenes
- `pyinstaller`: Generación de ejecutables

## Seguridad

- Las credenciales de base de datos no se almacenan
- Conexión segura a través de PyMySQL
- Validación de entrada de datos
- Confirmación para operaciones destructivas

## Solución de Problemas

### Error de Conexión
- Verificar que el servidor MariaDB esté accesible
- Comprobar credenciales de usuario
- Verificar que el puerto 3306 esté abierto
- Confirmar que la base de datos 'xui' existe

### Error de Permisos
- El usuario debe tener permisos SELECT, INSERT, UPDATE, DELETE
- Verificar permisos en las tablas específicas de XUI

### Problemas de Interfaz
- Asegurar que tkinter esté instalado (viene con Python)
- En Linux, instalar: `sudo apt-get install python3-tk`

## Contribución

Para contribuir al proyecto:
1. Fork el repositorio
2. Crea una rama para tu feature
3. Realiza tus cambios
4. Envía un pull request

## Licencia

Este proyecto es de código abierto y está disponible bajo la licencia MIT.

## Soporte

Para reportar bugs o solicitar features, crea un issue en el repositorio del proyecto.
