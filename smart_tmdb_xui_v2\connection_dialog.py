#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Smart Connection Dialog for TMDB XUI v2
Diálogo de conexión mejorado con validación y guardado automático
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Optional, Callable

class SmartConnectionDialog:
    def __init__(self, parent, config_manager, callback: Optional[Callable] = None):
        self.parent = parent
        self.config_manager = config_manager
        self.callback = callback
        self.result = None
        
        # Paleta Gaming Oscura
        self.colors = {
            'bg': '#0A0A0A',
            'surface': '#161616',
            'surface_hover': '#202020',
            'fg': '#B0B0B0',           # Gris claro, no blanco
            'fg_secondary': '#808080',  # Gris medio
            'nvidia_green': '#76B900',
            'rog_red': '#CC0033',      # Rojo más sutil
            'gemini_purple': '#7B1FA2',
            'border': '#333333',       # Bordes más sutiles
            'card_bg': '#121212',
            'text_muted': '#606060'
        }
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("🔌 Conexión a Base de Datos - Gaming Edition")
        self.dialog.geometry("500x450")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Aplicar colores gaming
        self.dialog.configure(bg=self.colors['bg'])
        
        # Centrar el diálogo
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))
        
        self.setup_ui()
        self.load_saved_connections()
    
    def setup_ui(self):
        """Configurar la interfaz del diálogo"""
        
        # Header
        header_frame = ttk.Frame(self.dialog)
        header_frame.pack(fill='x', padx=20, pady=10)
        
        ttk.Label(header_frame, 
                 text="🔌 Conexión a Base de Datos XUI",
                 font=('Segoe UI', 12, 'bold')).pack()
        
        ttk.Label(header_frame,
                 text="Configura la conexión a tu servidor MySQL/MariaDB",
                 font=('Segoe UI', 9)).pack(pady=(5, 0))
        
        # Conexiones guardadas
        saved_frame = ttk.LabelFrame(self.dialog, text="💾 Conexiones Guardadas")
        saved_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        self.connections_combo = ttk.Combobox(saved_frame, state='readonly')
        self.connections_combo.pack(fill='x', padx=10, pady=10)
        self.connections_combo.bind('<<ComboboxSelected>>', self.on_connection_selected)
        
        # Formulario de conexión
        form_frame = ttk.LabelFrame(self.dialog, text="🔧 Configuración de Conexión")
        form_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # Grid para el formulario
        form_grid = ttk.Frame(form_frame)
        form_grid.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Campos del formulario
        fields = [
            ('🌐 Host:', 'host', '**************'),
            ('👤 Usuario:', 'user', 'infest84'),
            ('🔑 Contraseña:', 'password', '', True),
            ('🗄️ Base de Datos:', 'database', 'xui'),
            ('🔌 Puerto:', 'port', '3306')
        ]
        
        self.entries = {}
        
        for i, (label, key, default, *is_password) in enumerate(fields):
            ttk.Label(form_grid, text=label).grid(row=i, column=0, sticky='w', pady=5)
            
            if is_password:
                entry = ttk.Entry(form_grid, show='*', width=30)
            else:
                entry = ttk.Entry(form_grid, width=30)
            
            entry.grid(row=i, column=1, sticky='ew', padx=(10, 0), pady=5)
            entry.insert(0, default)
            self.entries[key] = entry
        
        form_grid.columnconfigure(1, weight=1)
        
        # Opciones
        options_frame = ttk.Frame(form_frame)
        options_frame.pack(fill='x', padx=10, pady=(10, 0))
        
        self.save_connection_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, 
                       text="💾 Guardar esta conexión",
                       variable=self.save_connection_var).pack(side='left')
        
        self.auto_connect_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_frame,
                       text="🔄 Conectar automáticamente",
                       variable=self.auto_connect_var).pack(side='right')
        
        # Botones
        buttons_frame = ttk.Frame(self.dialog)
        buttons_frame.pack(fill='x', padx=20, pady=(10, 0))
        
        ttk.Button(buttons_frame,
                  text="🧪 Probar Conexión",
                  command=self.test_connection).pack(side='left')
        
        ttk.Button(buttons_frame,
                  text="❌ Cancelar",
                  command=self.cancel).pack(side='right', padx=(5, 0))
        
        self.connect_btn = ttk.Button(buttons_frame,
                                     text="🔌 Conectar",
                                     command=self.connect)
        self.connect_btn.pack(side='right')
        
        # Status en frame separado
        status_frame = ttk.Frame(self.dialog)
        status_frame.pack(fill='x', padx=20, pady=(10, 20))
        
        self.status_label = ttk.Label(status_frame,
                                     text="📝 Completa los datos y haz clic en Conectar",
                                     font=('Segoe UI', 8))
        self.status_label.pack()
    
    def load_saved_connections(self):
        """Cargar conexiones guardadas"""
        connections = self.config_manager.load_connections()
        
        if connections:
            connection_names = []
            for conn in connections:
                name = f"{conn.get('user', '')}@{conn.get('host', '')}:{conn.get('database', '')}"
                connection_names.append(name)
            
            self.connections_combo['values'] = connection_names
            self.saved_connections = connections
        else:
            self.connections_combo['values'] = []
            self.saved_connections = []
    
    def on_connection_selected(self, event):
        """Manejar selección de conexión guardada"""
        selection = self.connections_combo.current()
        
        if selection >= 0 and selection < len(self.saved_connections):
            conn = self.saved_connections[selection]
            
            # Rellenar campos
            self.entries['host'].delete(0, 'end')
            self.entries['host'].insert(0, conn.get('host', ''))
            
            self.entries['user'].delete(0, 'end')
            self.entries['user'].insert(0, conn.get('user', ''))
            
            self.entries['password'].delete(0, 'end')
            self.entries['password'].insert(0, conn.get('password', ''))
            
            self.entries['database'].delete(0, 'end')
            self.entries['database'].insert(0, conn.get('database', ''))
            
            self.entries['port'].delete(0, 'end')
            self.entries['port'].insert(0, str(conn.get('port', 3306)))
            
            self.auto_connect_var.set(conn.get('auto_connect', False))
    
    def get_connection_data(self) -> dict:
        """Obtener datos de conexión del formulario"""
        return {
            'host': self.entries['host'].get().strip(),
            'user': self.entries['user'].get().strip(),
            'password': self.entries['password'].get(),
            'database': self.entries['database'].get().strip(),
            'port': int(self.entries['port'].get().strip() or 3306),
            'auto_connect': self.auto_connect_var.get()
        }
    
    def test_connection(self):
        """Probar la conexión sin conectar permanentemente"""
        try:
            connection_data = self.get_connection_data()
            
            # Validar datos básicos
            if not all([connection_data['host'], connection_data['user'], 
                       connection_data['database']]):
                self.update_status("⚠️ Por favor completa todos los campos requeridos", 'warning')
                return
            
            self.update_status("🔄 Probando conexión...", 'info')
            self.connect_btn.configure(state='disabled')
            
            # Probar conexión en hilo separado
            threading.Thread(target=self._test_connection_thread,
                           args=(connection_data,), daemon=True).start()
            
        except ValueError as e:
            self.update_status(f"⚠️ Puerto inválido: {e}", 'error')
        except Exception as e:
            self.update_status(f"❌ Error: {e}", 'error')
    
    def _test_connection_thread(self, connection_data):
        """Probar conexión en hilo separado"""
        try:
            import pymysql
            
            # Intentar conexión
            connection = pymysql.connect(
                host=connection_data['host'],
                user=connection_data['user'],
                password=connection_data['password'],
                database=connection_data['database'],
                port=connection_data['port'],
                connect_timeout=5
            )
            
            if connection.open:
                # Probar una consulta simple
                with connection.cursor() as cursor:
                    cursor.execute("SELECT COUNT(*) FROM streams LIMIT 1")
                    result = cursor.fetchone()
                
                connection.close()
                
                # Actualizar UI en hilo principal
                self.dialog.after(0, lambda: self.update_status("✅ Conexión exitosa", 'success'))
                self.dialog.after(0, lambda: self.connect_btn.configure(state='normal'))
            
        except Exception as e:
            error_msg = f"❌ Error de conexión: {str(e)}"
            self.dialog.after(0, lambda: self.update_status(error_msg, 'error'))
            self.dialog.after(0, lambda: self.connect_btn.configure(state='normal'))
    
    def connect(self):
        """Conectar a la base de datos"""
        try:
            connection_data = self.get_connection_data()
            
            # Validar datos
            if not all([connection_data['host'], connection_data['user'], 
                       connection_data['database']]):
                self.update_status("⚠️ Por favor completa todos los campos requeridos", 'warning')
                return
            
            # Guardar conexión si se solicita
            if self.save_connection_var.get():
                self.config_manager.save_connection(connection_data)
            
            # Establecer resultado
            self.result = connection_data
            
            # Llamar callback si existe
            if self.callback:
                self.callback(connection_data)
            
            self.dialog.destroy()
            
        except ValueError as e:
            self.update_status(f"⚠️ Puerto inválido: {e}", 'error')
        except Exception as e:
            self.update_status(f"❌ Error: {e}", 'error')
    
    def cancel(self):
        """Cancelar el diálogo"""
        self.result = None
        self.dialog.destroy()
    
    def update_status(self, message: str, level: str = 'info'):
        """Actualizar mensaje de estado"""
        colors = {
            'info': 'blue',
            'success': 'green', 
            'warning': 'orange',
            'error': 'red'
        }
        
        # Nota: En tkinter estándar no podemos cambiar colores fácilmente
        # Esta es una versión simplificada
        self.status_label.configure(text=message)
    
    def show(self) -> Optional[dict]:
        """Mostrar el diálogo y retornar el resultado"""
        self.dialog.wait_window()
        return self.result
