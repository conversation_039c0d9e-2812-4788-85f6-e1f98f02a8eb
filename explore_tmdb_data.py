#!/usr/bin/env python3
"""
Script para explorar datos TMDB en la base de datos XUI
"""

import sys
import json
from database import DatabaseManager

def explore_tmdb_data():
    """Explorar datos TMDB en streams y streams_series"""
    print("=== Exploración de Datos TMDB ===\n")
    
    host = "**************"
    user = "infest84"
    password = "GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP"
    database = "xui"
    port = 3306
    
    db = DatabaseManager()
    
    if db.connect(host, user, password, database, port):
        print("✓ Conexión exitosa!")
        
        # 1. Explorar movie_properties en streams
        print("\n1. Explorando movie_properties en streams:")
        try:
            movie_props_query = """
            SELECT 
                s.id,
                s.stream_display_name,
                s.movie_properties,
                s.tmdb_id,
                st.type_name
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies' 
            AND s.movie_properties IS NOT NULL 
            AND s.movie_properties != ''
            LIMIT 10
            """
            
            movies_with_props = db.execute_query(movie_props_query)
            print(f"   Películas con movie_properties: {len(movies_with_props)}")
            
            for movie in movies_with_props:
                print(f"\n   ID: {movie['id']}")
                print(f"   Título: {movie['stream_display_name']}")
                print(f"   TMDB ID directo: {movie['tmdb_id']}")
                
                # Intentar parsear movie_properties
                try:
                    if movie['movie_properties']:
                        props = json.loads(movie['movie_properties'])
                        tmdb_from_props = props.get('tmdb_id') or props.get('tmdb') or props.get('id')
                        print(f"   TMDB desde properties: {tmdb_from_props}")
                        print(f"   Properties keys: {list(props.keys())}")
                        
                        # Mostrar algunos campos importantes
                        for key in ['title', 'original_title', 'release_date', 'overview']:
                            if key in props:
                                value = str(props[key])[:50] + "..." if len(str(props[key])) > 50 else props[key]
                                print(f"   {key}: {value}")
                except json.JSONDecodeError:
                    print(f"   ⚠️ Error parseando JSON en movie_properties")
                except Exception as e:
                    print(f"   ⚠️ Error: {e}")
                
                print("   " + "-"*50)
                
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        # 2. Explorar tmdb_id en streams_series
        print("\n2. Explorando tmdb_id en streams_series:")
        try:
            series_tmdb_query = """
            SELECT 
                id,
                title,
                tmdb_id,
                genre,
                release_date,
                rating
            FROM streams_series
            WHERE tmdb_id IS NOT NULL 
            AND tmdb_id != 0
            LIMIT 10
            """
            
            series_with_tmdb = db.execute_query(series_tmdb_query)
            print(f"   Series con TMDB ID: {len(series_with_tmdb)}")
            
            for series in series_with_tmdb:
                print(f"\n   ID: {series['id']}")
                print(f"   Título: {series['title']}")
                print(f"   TMDB ID: {series['tmdb_id']}")
                print(f"   Género: {series['genre']}")
                print(f"   Fecha: {series['release_date']}")
                print(f"   Rating: {series['rating']}")
                print("   " + "-"*30)
                
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        # 3. Estadísticas TMDB
        print("\n3. Estadísticas TMDB:")
        try:
            # Películas con TMDB
            movies_tmdb_stats = db.execute_query("""
            SELECT 
                COUNT(*) as total_movies,
                SUM(CASE WHEN tmdb_id IS NOT NULL AND tmdb_id != 0 THEN 1 ELSE 0 END) as with_tmdb_direct,
                SUM(CASE WHEN movie_properties IS NOT NULL AND movie_properties != '' THEN 1 ELSE 0 END) as with_properties
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies'
            """)
            
            if movies_tmdb_stats:
                stats = movies_tmdb_stats[0]
                print(f"   Películas totales: {stats['total_movies']}")
                print(f"   Con TMDB ID directo: {stats['with_tmdb_direct']}")
                print(f"   Con movie_properties: {stats['with_properties']}")
            
            # Series con TMDB
            series_tmdb_stats = db.execute_query("""
            SELECT 
                COUNT(*) as total_series,
                SUM(CASE WHEN tmdb_id IS NOT NULL AND tmdb_id != 0 THEN 1 ELSE 0 END) as with_tmdb
            FROM streams_series
            """)
            
            if series_tmdb_stats:
                stats = series_tmdb_stats[0]
                print(f"   Series totales: {stats['total_series']}")
                print(f"   Con TMDB ID: {stats['with_tmdb']}")
                
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        # 4. Buscar duplicados por TMDB ID
        print("\n4. Buscando duplicados por TMDB ID:")
        try:
            # Duplicados en películas por TMDB
            movie_duplicates_tmdb = db.execute_query("""
            SELECT 
                tmdb_id,
                COUNT(*) as count,
                GROUP_CONCAT(id) as stream_ids,
                GROUP_CONCAT(stream_display_name) as titles
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies' 
            AND tmdb_id IS NOT NULL 
            AND tmdb_id != 0
            GROUP BY tmdb_id
            HAVING COUNT(*) > 1
            ORDER BY count DESC
            LIMIT 5
            """)
            
            print(f"   Duplicados de películas por TMDB ID: {len(movie_duplicates_tmdb)}")
            for dup in movie_duplicates_tmdb:
                titles = dup['titles'].split(',')
                print(f"     TMDB {dup['tmdb_id']}: {dup['count']} copias")
                print(f"       Títulos: {titles[0]}...")
                print(f"       IDs: {dup['stream_ids']}")
            
            # Duplicados en series por TMDB
            series_duplicates_tmdb = db.execute_query("""
            SELECT 
                tmdb_id,
                COUNT(*) as count,
                GROUP_CONCAT(id) as series_ids,
                GROUP_CONCAT(title) as titles
            FROM streams_series
            WHERE tmdb_id IS NOT NULL 
            AND tmdb_id != 0
            GROUP BY tmdb_id
            HAVING COUNT(*) > 1
            ORDER BY count DESC
            LIMIT 5
            """)
            
            print(f"   Duplicados de series por TMDB ID: {len(series_duplicates_tmdb)}")
            for dup in series_duplicates_tmdb:
                titles = dup['titles'].split(',')
                print(f"     TMDB {dup['tmdb_id']}: {dup['count']} copias")
                print(f"       Títulos: {titles[0]}...")
                print(f"       IDs: {dup['series_ids']}")
                
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        db.disconnect()
        return True
        
    else:
        print("✗ Error de conexión")
        return False

def main():
    try:
        explore_tmdb_data()
        input("\nPresiona Enter para salir...")
    except Exception as e:
        print(f"\nError: {e}")

if __name__ == "__main__":
    main()
