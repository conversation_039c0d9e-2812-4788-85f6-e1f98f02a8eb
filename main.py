#!/usr/bin/env python3
"""
XUI Database Manager
Aplicación para gestionar base de datos XUI - Series, Episodios y Películas
"""

import sys
import os
import logging
from gui import main

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('xui_manager.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

def setup_environment():
    """Configurar el entorno de la aplicación"""
    # Asegurar que el directorio actual esté en el path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

if __name__ == "__main__":
    try:
        setup_environment()
        logging.info("Iniciando XUI Database Manager")
        main()
    except Exception as e:
        logging.error(f"Error al iniciar la aplicación: {e}")
        sys.exit(1)
