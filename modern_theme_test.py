#!/usr/bin/env python3
"""
Test de temas modernos que realmente funcionen
"""

import tkinter as tk
from tkinter import ttk
import sys

class ModernApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎨 Test de Tema Moderno Real")
        self.root.geometry("800x600")
        
        # Tema VS Code Dark real
        self.theme = {
            'bg': '#1e1e1e',
            'fg': '#d4d4d4', 
            'accent': '#007acc',
            'button_bg': '#0e639c',
            'button_hover': '#1177bb',
            'select_bg': '#264f78',
            'select_fg': '#ffffff',
            'border': '#3e3e42',
            'sidebar_bg': '#252526'
        }
        
        self.setup_theme()
        self.create_widgets()
    
    def setup_theme(self):
        """Configurar tema moderno que realmente funcione"""
        # Configurar ventana principal
        self.root.configure(bg=self.theme['bg'])
        
        # Configurar ttk style
        self.style = ttk.Style()
        
        # Usar tema base más compatible
        try:
            if 'clam' in self.style.theme_names():
                self.style.theme_use('clam')
            print(f"Tema base: {self.style.theme_use()}")
        except:
            pass
        
        # Configurar estilos específicos
        self.configure_styles()
    
    def configure_styles(self):
        """Configurar estilos específicos"""
        theme = self.theme
        
        # Frame
        self.style.configure('Dark.TFrame',
                           background=theme['bg'])
        
        # Label
        self.style.configure('Dark.TLabel',
                           background=theme['bg'],
                           foreground=theme['fg'],
                           font=('Segoe UI', 10))
        
        # Button
        self.style.configure('Dark.TButton',
                           background=theme['button_bg'],
                           foreground='white',
                           font=('Segoe UI', 10),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none')
        
        self.style.map('Dark.TButton',
                      background=[('active', theme['button_hover']),
                                ('pressed', theme['accent'])])
        
        # Accent Button
        self.style.configure('Accent.TButton',
                           background=theme['accent'],
                           foreground='white',
                           font=('Segoe UI', 10, 'bold'),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none')
        
        self.style.map('Accent.TButton',
                      background=[('active', theme['button_hover']),
                                ('pressed', theme['button_bg'])])
        
        # Entry
        self.style.configure('Dark.TEntry',
                           fieldbackground=theme['sidebar_bg'],
                           foreground=theme['fg'],
                           bordercolor=theme['border'],
                           insertcolor=theme['fg'],
                           relief='solid',
                           borderwidth=1)
        
        self.style.map('Dark.TEntry',
                      bordercolor=[('focus', theme['accent'])])
        
        # Notebook
        self.style.configure('Dark.TNotebook',
                           background=theme['bg'],
                           borderwidth=0)
        
        self.style.configure('Dark.TNotebook.Tab',
                           background=theme['sidebar_bg'],
                           foreground=theme['fg'],
                           padding=[15, 8],
                           borderwidth=0,
                           focuscolor='none')
        
        self.style.map('Dark.TNotebook.Tab',
                      background=[('selected', theme['bg']),
                                ('active', theme['button_hover'])],
                      foreground=[('selected', theme['fg']),
                                ('active', 'white')])
        
        # Treeview
        self.style.configure('Dark.Treeview',
                           background=theme['bg'],
                           foreground=theme['fg'],
                           fieldbackground=theme['bg'],
                           borderwidth=1,
                           relief='solid')
        
        self.style.configure('Dark.Treeview.Heading',
                           background=theme['sidebar_bg'],
                           foreground=theme['fg'],
                           relief='flat')
        
        self.style.map('Dark.Treeview',
                      background=[('selected', theme['select_bg'])],
                      foreground=[('selected', theme['select_fg'])])
    
    def create_widgets(self):
        """Crear widgets de prueba"""
        # Frame principal
        main_frame = ttk.Frame(self.root, style='Dark.TFrame', padding=20)
        main_frame.pack(fill='both', expand=True)
        
        # Título
        title = ttk.Label(main_frame, 
                         text="🎨 Tema Moderno VS Code Dark - ¡Funcional!",
                         style='Dark.TLabel',
                         font=('Segoe UI', 16, 'bold'))
        title.pack(pady=(0, 20))
        
        # Descripción
        desc = ttk.Label(main_frame,
                        text="Este es un ejemplo de tema moderno que realmente funciona.\nSin apariencia de Windows 98 😄",
                        style='Dark.TLabel',
                        font=('Segoe UI', 11))
        desc.pack(pady=(0, 20))
        
        # Frame de botones
        button_frame = ttk.Frame(main_frame, style='Dark.TFrame')
        button_frame.pack(fill='x', pady=10)
        
        # Botones de prueba
        ttk.Button(button_frame, text="🔵 Botón Normal", style='Dark.TButton').pack(side='left', padx=5)
        ttk.Button(button_frame, text="⭐ Botón Acento", style='Accent.TButton').pack(side='left', padx=5)
        ttk.Button(button_frame, text="🎯 Otro Botón", style='Dark.TButton').pack(side='left', padx=5)
        
        # Campo de entrada
        entry_frame = ttk.Frame(main_frame, style='Dark.TFrame')
        entry_frame.pack(fill='x', pady=10)
        
        ttk.Label(entry_frame, text="Campo de prueba:", style='Dark.TLabel').pack(side='left', padx=(0, 10))
        entry = ttk.Entry(entry_frame, style='Dark.TEntry', width=30)
        entry.pack(side='left')
        entry.insert(0, "Texto de ejemplo con tema moderno")
        
        # Notebook de prueba
        notebook = ttk.Notebook(main_frame, style='Dark.TNotebook')
        notebook.pack(fill='both', expand=True, pady=20)
        
        # Pestaña 1
        tab1 = ttk.Frame(notebook, style='Dark.TFrame')
        notebook.add(tab1, text="🎬 Pestaña 1")
        
        ttk.Label(tab1, text="Contenido de la pestaña 1 con tema moderno", 
                 style='Dark.TLabel').pack(pady=20)
        
        # Treeview de prueba
        tree_frame = ttk.Frame(tab1, style='Dark.TFrame')
        tree_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        columns = ('ID', 'Nombre', 'Tipo')
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', style='Dark.Treeview')
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)
        
        # Datos de ejemplo
        tree.insert('', 'end', values=('1', 'Película Ejemplo', '4K'))
        tree.insert('', 'end', values=('2', 'Serie Ejemplo', 'FHD'))
        tree.insert('', 'end', values=('3', 'Documental', 'HD'))
        
        tree.pack(fill='both', expand=True)
        
        # Pestaña 2
        tab2 = ttk.Frame(notebook, style='Dark.TFrame')
        notebook.add(tab2, text="⚙️ Pestaña 2")
        
        ttk.Label(tab2, text="Segunda pestaña con tema consistente", 
                 style='Dark.TLabel').pack(pady=20)
        
        # Texto de resultado
        result_frame = ttk.Frame(main_frame, style='Dark.TFrame')
        result_frame.pack(fill='x', pady=10)
        
        result_text = tk.Text(result_frame, 
                             height=4, 
                             bg=self.theme['sidebar_bg'],
                             fg=self.theme['fg'],
                             insertbackground=self.theme['fg'],
                             selectbackground=self.theme['select_bg'],
                             selectforeground=self.theme['select_fg'],
                             font=('Consolas', 10),
                             relief='solid',
                             borderwidth=1)
        result_text.pack(fill='x')
        result_text.insert('1.0', "✅ Tema moderno aplicado correctamente!\n")
        result_text.insert('end', "🎨 Colores: VS Code Dark\n")
        result_text.insert('end', "🔤 Fuentes: Segoe UI, Consolas\n")
        result_text.insert('end', "🚀 ¡Ya no parece Windows 98!")
    
    def run(self):
        """Ejecutar la aplicación"""
        self.root.mainloop()

def main():
    print("🎨 Probando tema moderno real...")
    print("Si esto se ve bien, aplicaremos el mismo enfoque a la app principal")
    
    app = ModernApp()
    app.run()

if __name__ == "__main__":
    main()
