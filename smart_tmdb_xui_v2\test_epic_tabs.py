#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de pestañas épicas - Smart TMDB XUI v2
Prueba visual de las nuevas pestañas gaming
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from demo_app import SmartTMDBAppSimple
    print("✅ Demo app importada")
except ImportError as e:
    print(f"❌ Error: {e}")
    try:
        from main import SmartTMDBApp
        SmartTMDBAppSimple = SmartTMDBApp
        print("✅ Main app importada como fallback")
    except ImportError as e2:
        print(f"❌ Error con main: {e2}")
        sys.exit(1)

def test_epic_tabs():
    """Probar las pestañas épicas"""
    print("🎮 Iniciando test de pestañas épicas...")
    print("📊 Pestañas que verás:")
    print("   🚀 DASHBOARD      - Panel principal")
    print("   📺 SERIES TV      - Gestión de series")
    print("   🎬 PELÍCULAS      - Gestión de películas")
    print("   ⚙️ MAINTENANCE    - Herramientas de mantenimiento")
    print("   🛠️ TOOLS          - Herramientas avanzadas")
    
    print("\n🎨 Características visuales:")
    print("   ✅ Texto en MAYÚSCULAS para impacto")
    print("   ✅ Iconos llamativos")
    print("   ✅ Pestaña activa en verde NVIDIA")
    print("   ✅ Hover effects sutiles")
    print("   ✅ Fondo ultra oscuro")
    
    # Crear aplicación
    app = SmartTMDBAppSimple()
    
    print("\n🚀 Aplicación iniciada. ¡Haz clic en las pestañas para probarlas!")
    app.root.mainloop()

if __name__ == "__main__":
    test_epic_tabs()
