#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de pestañas gaming ultra modernas
Sistema de pestañas custom con efectos épicos
"""

import tkinter as tk

class ModernTabsDemo:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎮 PESTAÑAS GAMING ULTRA MODERNAS")
        self.root.geometry("1200x800")
        
        # Colores épicos
        self.colors = {
            'bg': '#0A0A0A',
            'surface': '#161616',
            'surface_hover': '#202020',
            'fg': '#B0B0B0',
            'nvidia_green': '#76B900',
            'gemini_purple': '#7B1FA2',
            'rog_red': '#CC0033',
            'gemini_deep': '#4A148C'
        }
        
        self.root.configure(bg=self.colors['bg'])
        
        # Título principal
        title = tk.Label(self.root, 
                        text="🚀 SMART TMDB XUI v2 - GAMING EDITION",
                        font=('Segoe UI', 20, 'bold'),
                        fg=self.colors['nvidia_green'],
                        bg=self.colors['bg'])
        title.pack(pady=20)
        
        self.create_modern_tabs()
    
    def create_modern_tabs(self):
        """Crear pestañas ultra modernas"""
        # Contenedor para pestañas
        tabs_container = tk.Frame(self.root, bg=self.colors['bg'])
        tabs_container.pack(fill='x', padx=20, pady=10)
        
        # Frame para contenido
        self.content_frame = tk.Frame(self.root, bg=self.colors['bg'])
        self.content_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Datos de pestañas épicas
        self.tabs_data = [
            {'name': 'dashboard', 'text': '🚀 DASHBOARD', 'color': self.colors['nvidia_green']},
            {'name': 'series', 'text': '📺 SERIES TV', 'color': self.colors['gemini_purple']},
            {'name': 'movies', 'text': '🎬 PELÍCULAS', 'color': self.colors['rog_red']},
            {'name': 'maintenance', 'text': '⚙️ MAINTENANCE', 'color': self.colors['nvidia_green']},
            {'name': 'tools', 'text': '🛠️ TOOLS', 'color': self.colors['gemini_deep']}
        ]
        
        self.tab_buttons = {}
        self.current_tab = 'dashboard'
        
        # Crear botones épicos
        for tab in self.tabs_data:
            self.create_epic_button(tabs_container, tab)
        
        # Crear contenido inicial
        self.show_content('dashboard')
    
    def create_epic_button(self, parent, tab_data):
        """Crear botón épico con efectos"""
        name = tab_data['name']
        text = tab_data['text']
        color = tab_data['color']
        
        btn = tk.Button(parent, 
                       text=text,
                       font=('Segoe UI', 12, 'bold'),
                       bg=self.colors['surface'],
                       fg=self.colors['fg'],
                       activebackground=color,
                       activeforeground=self.colors['bg'],
                       relief='flat',
                       bd=0,
                       padx=25, pady=12,
                       cursor='hand2',
                       command=lambda: self.switch_tab(name))
        
        btn.pack(side='left', padx=5)
        self.tab_buttons[name] = btn
        
        # Efectos hover épicos
        def on_enter(e):
            if name != self.current_tab:
                btn.config(bg=color, fg=self.colors['bg'], relief='solid', bd=2)
        
        def on_leave(e):
            if name != self.current_tab:
                btn.config(bg=self.colors['surface'], fg=self.colors['fg'], relief='flat', bd=0)
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
    
    def switch_tab(self, tab_name):
        """Cambiar pestaña con efectos"""
        self.current_tab = tab_name
        
        # Actualizar botones
        for name, btn in self.tab_buttons.items():
            if name == tab_name:
                color = next(t['color'] for t in self.tabs_data if t['name'] == name)
                btn.config(bg=color, fg=self.colors['bg'], relief='solid', bd=3)
            else:
                btn.config(bg=self.colors['surface'], fg=self.colors['fg'], relief='flat', bd=0)
        
        # Mostrar contenido
        self.show_content(tab_name)
    
    def show_content(self, tab_name):
        """Mostrar contenido de la pestaña"""
        # Limpiar contenido
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # Crear contenido épico
        content_data = {
            'dashboard': {
                'title': '🚀 DASHBOARD CONTROL CENTER',
                'subtitle': 'Centro de control principal',
                'color': self.colors['nvidia_green'],
                'stats': [
                    ('🎬 PELÍCULAS', '28,509'),
                    ('📺 EPISODIOS', '14,702'),
                    ('⚡ OPTIMIZADAS', '100%'),
                    ('🔥 STATUS', 'GAMING')
                ]
            },
            'series': {
                'title': '📺 SERIES MANAGEMENT',
                'subtitle': 'Gestión avanzada de series',
                'color': self.colors['gemini_purple'],
                'stats': [
                    ('📚 SERIES', '1,247'),
                    ('🎭 GÉNEROS', '42'),
                    ('⭐ RATING', '9.2/10'),
                    ('🔄 SYNC', 'ACTIVO')
                ]
            },
            'movies': {
                'title': '🎬 MOVIES COMMAND',
                'subtitle': 'Centro de comando de películas',
                'color': self.colors['rog_red'],
                'stats': [
                    ('🍿 MOVIES', '28,509'),
                    ('🏆 AWARDS', '156'),
                    ('📈 TRENDING', '+12%'),
                    ('🔥 HOT', '847')
                ]
            },
            'maintenance': {
                'title': '⚙️ MAINTENANCE HUB',
                'subtitle': 'Herramientas de mantenimiento',
                'color': self.colors['nvidia_green'],
                'stats': [
                    ('🔧 FIXES', '2,847'),
                    ('⚡ SPEED', '+300%'),
                    ('🛡️ CHECKS', 'OK'),
                    ('🚀 READY', '100%')
                ]
            },
            'tools': {
                'title': '🛠️ ADVANCED TOOLS',
                'subtitle': 'Herramientas de desarrollo',
                'color': self.colors['gemini_deep'],
                'stats': [
                    ('🧰 TOOLS', '24'),
                    ('🤖 AI', 'ACTIVE'),
                    ('📊 ANALYTICS', 'ON'),
                    ('🎯 PRECISION', '99.8%')
                ]
            }
        }
        
        data = content_data.get(tab_name, content_data['dashboard'])
        
        # Título épico
        title = tk.Label(self.content_frame,
                        text=data['title'],
                        font=('Segoe UI', 24, 'bold'),
                        fg=data['color'],
                        bg=self.colors['bg'])
        title.pack(pady=(20, 5))
        
        # Subtítulo
        subtitle = tk.Label(self.content_frame,
                           text=data['subtitle'],
                           font=('Segoe UI', 12),
                           fg=self.colors['fg'],
                           bg=self.colors['bg'])
        subtitle.pack(pady=(0, 30))
        
        # Grid de estadísticas épicas
        stats_frame = tk.Frame(self.content_frame, bg=self.colors['bg'])
        stats_frame.pack(expand=True)
        
        for i, (label, value) in enumerate(data['stats']):
            row = i // 2
            col = i % 2
            
            card = tk.Frame(stats_frame, bg=self.colors['surface'], 
                           relief='solid', bd=2, width=200, height=100)
            card.grid(row=row, column=col, padx=20, pady=15, sticky='nsew')
            card.pack_propagate(False)
            
            tk.Label(card, text=label, font=('Segoe UI', 11, 'bold'),
                    fg=self.colors['fg'], bg=self.colors['surface']).pack(pady=8)
            tk.Label(card, text=value, font=('Segoe UI', 16, 'bold'),
                    fg=data['color'], bg=self.colors['surface']).pack()
            tk.Label(card, text="●●●●●", font=('Segoe UI', 8),
                    fg=data['color'], bg=self.colors['surface']).pack(pady=8)

def main():
    print("🎮 Iniciando demo de pestañas gaming ultra modernas...")
    print("✨ Efectos incluidos:")
    print("   🔥 Hover effects épicos")
    print("   🌈 Colores por pestaña")
    print("   ⚡ Transiciones suaves")
    print("   🎨 Estadísticas dinámicas")
    
    app = ModernTabsDemo()
    app.root.mainloop()

if __name__ == "__main__":
    main()
