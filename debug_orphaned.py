#!/usr/bin/env python3
"""
Script para debuggear episodios huérfanos
"""

import sys
from database import DatabaseManager

def debug_orphaned():
    """Debuggear episodios huérfanos"""
    print("=== Debug Episodios Huérfanos ===\n")
    
    # Usar las credenciales que ya funcionaron
    host = "**************"
    user = "infest84"
    password = "GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP"
    database = "xui"
    port = 3306
    
    db = DatabaseManager()
    
    if db.connect(host, user, password, database, port):
        print("✓ Conexión exitosa!")
        
        # 1. Verificar estructura de streams_episodes
        print("\n1. Estructura de streams_episodes:")
        columns = db.execute_query("DESCRIBE streams_episodes")
        for col in columns:
            print(f"   {col['Field']} ({col['Type']})")
        
        # 2. Contar total de episodios
        print("\n2. Total de episodios:")
        total_episodes = db.execute_query("SELECT COUNT(*) as count FROM streams_episodes")
        if total_episodes:
            print(f"   Total: {total_episodes[0]['count']} episodios")
        
        # 3. Verificar episodios con series_id NULL
        print("\n3. Episodios con series_id NULL:")
        null_series = db.execute_query("SELECT COUNT(*) as count FROM streams_episodes WHERE series_id IS NULL")
        if null_series:
            print(f"   Con series_id NULL: {null_series[0]['count']} episodios")
        
        # 4. Verificar episodios con series_id = 0
        print("\n4. Episodios con series_id = 0:")
        zero_series = db.execute_query("SELECT COUNT(*) as count FROM streams_episodes WHERE series_id = 0")
        if zero_series:
            print(f"   Con series_id = 0: {zero_series[0]['count']} episodios")
        
        # 5. Mostrar algunos ejemplos de episodios
        print("\n5. Ejemplos de episodios (primeros 10):")
        examples = db.execute_query("SELECT id, season_num, episode_num, series_id, stream_id FROM streams_episodes LIMIT 10")
        for ep in examples:
            print(f"   ID: {ep['id']}, Season: {ep['season_num']}, Episode: {ep['episode_num']}, Series_ID: {ep['series_id']}, Stream_ID: {ep['stream_id']}")
        
        # 6. Verificar si existen series_id que no están en streams_series
        print("\n6. Episodios con series_id que no existen en streams_series:")
        orphaned_query = """
        SELECT COUNT(*) as count 
        FROM streams_episodes se 
        LEFT JOIN streams_series ss ON se.series_id = ss.id 
        WHERE se.series_id IS NOT NULL AND se.series_id != 0 AND ss.id IS NULL
        """
        orphaned_count = db.execute_query(orphaned_query)
        if orphaned_count:
            print(f"   Episodios con series_id inexistente: {orphaned_count[0]['count']}")
        
        # 7. Mostrar ejemplos de estos episodios huérfanos
        if orphaned_count and orphaned_count[0]['count'] > 0:
            print("\n7. Ejemplos de episodios huérfanos:")
            orphaned_examples = db.execute_query("""
            SELECT se.id, se.series_id, se.stream_id, se.season_num, se.episode_num
            FROM streams_episodes se 
            LEFT JOIN streams_series ss ON se.series_id = ss.id 
            WHERE se.series_id IS NOT NULL AND se.series_id != 0 AND ss.id IS NULL
            LIMIT 10
            """)
            for ep in orphaned_examples:
                print(f"   Episode ID: {ep['id']}, Series_ID inexistente: {ep['series_id']}, Stream_ID: {ep['stream_id']}")
        
        db.disconnect()
        return True
        
    else:
        print("✗ Error de conexión")
        return False

def main():
    try:
        debug_orphaned()
        input("\nPresiona Enter para salir...")
    except Exception as e:
        print(f"\nError: {e}")

if __name__ == "__main__":
    main()
