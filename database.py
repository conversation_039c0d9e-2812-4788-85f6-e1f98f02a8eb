import pymysql
from typing import List, Dict, Optional, Tuple
import logging

class DatabaseManager:
    def __init__(self):
        self.connection = None
        self.host = None
        self.user = None
        self.password = None
        self.database = None
        self.port = 3306
        
    def connect(self, host: str, user: str, password: str, database: str, port: int = 3306) -> bool:
        """Conectar a la base de datos MariaDB"""
        try:
            self.host = host
            self.user = user
            self.password = password
            self.database = database
            self.port = port
            
            self.connection = pymysql.connect(
                host=host,
                user=user,
                password=password,
                database=database,
                port=port,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            return True
        except Exception as e:
            logging.error(f"Error conectando a la base de datos: {e}")
            return False
    
    def disconnect(self):
        """Desconectar de la base de datos"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def is_connected(self) -> bool:
        """Verificar si está conectado"""
        try:
            if self.connection:
                self.connection.ping(reconnect=True)
                return True
        except:
            pass
        return False
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """Ejecutar una consulta SELECT"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchall()
        except Exception as e:
            logging.error(f"Error ejecutando consulta: {e}")
            return []
    
    def execute_update(self, query: str, params: tuple = None) -> bool:
        """Ejecutar una consulta INSERT/UPDATE/DELETE"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, params)
                self.connection.commit()
                return True
        except Exception as e:
            logging.error(f"Error ejecutando actualización: {e}")
            self.connection.rollback()
            return False
    
    def get_series_with_episodes(self) -> List[Dict]:
        """Obtener todas las series con sus episodios"""
        query = """
        SELECT 
            ss.id as series_id,
            ss.title as series_title,
            s.id as stream_id,
            s.type,
            st.type_name,
            COUNT(se.id) as episode_count
        FROM streams_series ss
        LEFT JOIN streams_episodes se ON ss.id = se.series_id
        LEFT JOIN streams s ON se.stream_id = s.id
        LEFT JOIN streams_types st ON s.type = st.type_id
        GROUP BY ss.id, ss.title, s.type, st.type_name
        ORDER BY ss.title
        """
        return self.execute_query(query)
    
    def get_orphaned_episodes(self) -> List[Dict]:
        """Obtener episodios huérfanos (sin serie asignada)"""
        query = """
        SELECT 
            se.id as episode_id,
            se.stream_id,
            se.series_id,
            s.id as stream_id,
            s.type,
            st.type_name
        FROM streams_episodes se
        LEFT JOIN streams_series ss ON se.series_id = ss.id
        LEFT JOIN streams s ON se.stream_id = s.id
        LEFT JOIN streams_types st ON s.type = st.type_id
        WHERE ss.id IS NULL
        ORDER BY se.id
        """
        return self.execute_query(query)
    
    def get_series_without_episodes(self) -> List[Dict]:
        """Obtener series sin episodios"""
        query = """
        SELECT 
            ss.id as series_id,
            ss.title as series_title
        FROM streams_series ss
        LEFT JOIN streams_episodes se ON ss.id = se.series_id
        WHERE se.id IS NULL
        ORDER BY ss.title
        """
        return self.execute_query(query)

    def get_series_without_episodes_detailed(self) -> List[Dict]:
        """Obtener series sin episodios con información detallada"""
        query = """
        SELECT
            ss.id as series_id,
            ss.title as series_title,
            ss.tmdb_id,
            ss.year,
            ss.genre,
            ss.rating,
            ss.last_modified,
            CASE
                WHEN ss.tmdb_id IS NULL OR ss.tmdb_id = 0 THEN 'No TMDB assigned'
                WHEN ss.last_modified IS NULL THEN 'Never modified'
                WHEN ss.last_modified < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 6 MONTH)) THEN 'Old series (6+ months)'
                ELSE 'Recent series'
            END as series_status,
            CASE
                WHEN ss.tmdb_id IS NOT NULL AND ss.tmdb_id != 0 THEN 'KEEP (Has TMDB)'
                WHEN ss.year IS NOT NULL AND ss.year > YEAR(NOW()) - 2 THEN 'KEEP (Recent)'
                ELSE 'CONSIDER DELETE (Old/No TMDB)'
            END as recommendation
        FROM streams_series ss
        LEFT JOIN streams_episodes se ON ss.id = se.series_id
        WHERE se.id IS NULL
        ORDER BY
            CASE
                WHEN ss.tmdb_id IS NOT NULL AND ss.tmdb_id != 0 THEN 1
                WHEN ss.year IS NOT NULL AND ss.year > YEAR(NOW()) - 2 THEN 2
                ELSE 3
            END,
            ss.title
        """
        return self.execute_query(query)

    def get_stream_types(self) -> List[Dict]:
        """Obtener todos los tipos de stream"""
        query = "SELECT type_id, type_name FROM streams_types ORDER BY type_name"
        return self.execute_query(query)
    
    def create_series(self, title: str) -> Optional[int]:
        """Crear una nueva serie"""
        query = "INSERT INTO streams_series (title) VALUES (%s)"
        if self.execute_update(query, (title,)):
            # Obtener el ID de la serie recién creada
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT LAST_INSERT_ID()")
                result = cursor.fetchone()
                return result['LAST_INSERT_ID()'] if result else None
        return None
    
    def assign_episode_to_series(self, episode_id: int, series_id: int) -> bool:
        """Asignar un episodio a una serie"""
        query = "UPDATE streams_episodes SET series_id = %s WHERE id = %s"
        return self.execute_update(query, (series_id, episode_id))
    
    def delete_episode(self, episode_id: int) -> bool:
        """Eliminar un episodio"""
        query = "DELETE FROM streams_episodes WHERE id = %s"
        return self.execute_update(query, (episode_id,))
    
    def delete_series(self, series_id: int) -> bool:
        """Eliminar una serie"""
        query = "DELETE FROM streams_series WHERE id = %s"
        return self.execute_update(query, (series_id,))
    
    def get_duplicate_movies(self) -> List[Dict]:
        """Obtener películas duplicadas (optimizada)"""
        query = """
        SELECT
            stream_display_name as title,
            COUNT(*) as duplicate_count,
            GROUP_CONCAT(id) as stream_ids
        FROM streams
        WHERE type = 2 AND stream_display_name IS NOT NULL AND stream_display_name != ''
        GROUP BY stream_display_name
        HAVING COUNT(*) > 1
        ORDER BY duplicate_count DESC, stream_display_name
        LIMIT 5000
        """
        return self.execute_query(query)

    def get_all_movies(self) -> List[Dict]:
        """Obtener todas las películas con información completa"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.type as type_id,
            st.type_name,
            CASE
                WHEN s.stream_display_name IS NULL OR s.stream_display_name = '' THEN '[Sin Título]'
                ELSE s.stream_display_name
            END as display_title
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        ORDER BY display_title
        """
        return self.execute_query(query)

    def get_movies_by_name(self, movie_name: str) -> List[Dict]:
        """Obtener todas las películas con un nombre específico"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.type as type_id,
            st.type_name,
            st.type_id as streams_type_id
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies' AND s.stream_display_name = %s
        ORDER BY s.id
        """
        return self.execute_query(query, (movie_name,))

    def search_movies_by_name(self, search_term: str) -> List[Dict]:
        """Buscar películas que contengan el término en el nombre"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.type as type_id,
            st.type_name,
            s.stream_display_name as display_title
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        AND s.stream_display_name IS NOT NULL
        AND s.stream_display_name != ''
        AND s.stream_display_name LIKE %s
        ORDER BY s.stream_display_name
        LIMIT 500
        """
        search_pattern = f"%{search_term}%"
        return self.execute_query(query, (search_pattern,))

    def get_movies_without_title(self) -> List[Dict]:
        """Obtener películas sin título (para limpieza)"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.type as type_id,
            st.type_name,
            '[Sin Título]' as display_title
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        AND (s.stream_display_name IS NULL OR s.stream_display_name = '')
        ORDER BY s.id
        LIMIT 1000
        """
        return self.execute_query(query)

    def delete_movie(self, stream_id: int) -> bool:
        """Eliminar una película (verificando que sea tipo Movies)"""
        # Primero verificar que sea una película
        verify_query = """
        SELECT s.id, st.type_name
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE s.id = %s AND st.type_name = 'Movies'
        """
        movie_check = self.execute_query(verify_query, (stream_id,))

        if not movie_check:
            return False  # No es una película o no existe

        # Eliminar la película
        query = "DELETE FROM streams WHERE id = %s"
        return self.execute_update(query, (stream_id,))

    def delete_stream(self, stream_id: int) -> bool:
        """Eliminar un stream (película, serie, etc.) sin verificar tipo"""
        try:
            query = "DELETE FROM streams WHERE id = %s"
            result = self.execute_update(query, (stream_id,))
            if result:
                logging.info(f"Stream {stream_id} eliminado exitosamente")
            else:
                logging.warning(f"No se pudo eliminar stream {stream_id} (puede que no exista)")
            return result
        except Exception as e:
            logging.error(f"Error eliminando stream {stream_id}: {e}")
            return False

    def get_movie_details(self, stream_id: int) -> Dict:
        """Obtener detalles completos de una película"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.type as type_id,
            st.type_name,
            st.type_id as streams_type_id,
            s.stream_source,
            s.stream_icon,
            s.notes
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE s.id = %s AND st.type_name = 'Movies'
        """
        result = self.execute_query(query, (stream_id,))
        return result[0] if result else {}

    def delete_multiple_movies(self, stream_ids: List[int]) -> Dict[str, int]:
        """Eliminar múltiples películas de manera eficiente"""
        if not stream_ids:
            return {"deleted": 0, "failed": 0}

        deleted_count = 0
        failed_count = 0

        # Verificar que todos sean películas antes de eliminar
        placeholders = ','.join(['%s'] * len(stream_ids))
        verify_query = f"""
        SELECT s.id
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE s.id IN ({placeholders}) AND st.type_name = 'Movies'
        """

        valid_movies = self.execute_query(verify_query, tuple(stream_ids))
        valid_ids = [movie['id'] for movie in valid_movies]

        if not valid_ids:
            return {"deleted": 0, "failed": len(stream_ids)}

        # Eliminar en lotes para mejor rendimiento
        batch_size = 100
        for i in range(0, len(valid_ids), batch_size):
            batch = valid_ids[i:i + batch_size]
            batch_placeholders = ','.join(['%s'] * len(batch))
            delete_query = f"DELETE FROM streams WHERE id IN ({batch_placeholders})"

            try:
                if self.execute_update(delete_query, tuple(batch)):
                    deleted_count += len(batch)
                else:
                    failed_count += len(batch)
            except Exception as e:
                logging.error(f"Error eliminando lote de películas: {e}")
                failed_count += len(batch)

        # Contar IDs que no eran películas válidas
        invalid_count = len(stream_ids) - len(valid_ids)
        failed_count += invalid_count

        return {"deleted": deleted_count, "failed": failed_count}

    def get_duplicate_movies_by_tmdb(self) -> List[Dict]:
        """Obtener películas duplicadas basándose en TMDB ID"""
        query = """
        SELECT
            s.tmdb_id,
            COUNT(*) as duplicate_count,
            GROUP_CONCAT(s.id) as stream_ids,
            GROUP_CONCAT(s.stream_display_name) as titles,
            MAX(s.stream_display_name) as sample_title
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        AND s.tmdb_id IS NOT NULL
        AND s.tmdb_id != 0
        GROUP BY s.tmdb_id
        HAVING COUNT(*) > 1
        ORDER BY duplicate_count DESC, sample_title
        LIMIT 5000
        """
        return self.execute_query(query)

    def get_duplicate_series_by_tmdb(self) -> List[Dict]:
        """Obtener series duplicadas basándose en TMDB ID"""
        query = """
        SELECT
            tmdb_id,
            COUNT(*) as duplicate_count,
            GROUP_CONCAT(id) as series_ids,
            GROUP_CONCAT(title) as titles,
            MAX(title) as sample_title
        FROM streams_series
        WHERE tmdb_id IS NOT NULL
        AND tmdb_id != 0
        GROUP BY tmdb_id
        HAVING COUNT(*) > 1
        ORDER BY duplicate_count DESC, sample_title
        LIMIT 5000
        """
        return self.execute_query(query)

    def get_movie_tmdb_details(self, stream_id: int) -> Dict:
        """Obtener detalles TMDB de una película específica"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.tmdb_id,
            s.movie_properties,
            s.stream_source,
            s.stream_icon,
            s.notes,
            s.year,
            s.rating,
            st.type_name
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE s.id = %s AND st.type_name = 'Movies'
        """
        result = self.execute_query(query, (stream_id,))
        return result[0] if result else {}

    def get_series_tmdb_details(self, series_id: int) -> Dict:
        """Obtener detalles TMDB de una serie específica"""
        query = """
        SELECT
            id as series_id,
            title,
            tmdb_id,
            genre,
            plot,
            cast,
            rating,
            director,
            release_date,
            year,
            cover,
            cover_big,
            backdrop_path,
            youtube_trailer
        FROM streams_series
        WHERE id = %s
        """
        result = self.execute_query(query, (series_id,))
        return result[0] if result else {}

    def update_movie_from_tmdb(self, stream_id: int, tmdb_data: Dict) -> bool:
        """Actualizar película con datos de TMDB"""
        try:
            query = """
            UPDATE streams
            SET
                stream_display_name = %s,
                tmdb_id = %s,
                year = %s,
                rating = %s,
                updated = CURRENT_TIMESTAMP
            WHERE id = %s
            """

            title = tmdb_data.get('title', '')
            tmdb_id = tmdb_data.get('tmdb_id', 0)
            year = tmdb_data.get('release_date', '')[:4] if tmdb_data.get('release_date') else None
            rating = tmdb_data.get('vote_average', 0)

            return self.execute_update(query, (title, tmdb_id, year, rating, stream_id))

        except Exception as e:
            logging.error(f"Error actualizando película {stream_id} con TMDB: {e}")
            return False

    def update_series_from_tmdb(self, series_id: int, tmdb_data: Dict) -> bool:
        """Actualizar serie con datos de TMDB"""
        try:
            query = """
            UPDATE streams_series
            SET
                title = %s,
                tmdb_id = %s,
                genre = %s,
                plot = %s,
                rating = %s,
                release_date = %s,
                year = %s,
                added = UNIX_TIMESTAMP()
            WHERE id = %s
            """

            title = tmdb_data.get('name', '')
            tmdb_id = tmdb_data.get('tmdb_id', 0)
            genres = ', '.join(tmdb_data.get('genres', []))
            plot = tmdb_data.get('overview', '')
            rating = int(tmdb_data.get('vote_average', 0))
            release_date = tmdb_data.get('first_air_date', '')
            year = release_date[:4] if release_date else None

            return self.execute_update(query, (title, tmdb_id, genres, plot, rating, release_date, year, series_id))

        except Exception as e:
            logging.error(f"Error actualizando serie {series_id} con TMDB: {e}")
            return False

    def create_orphaned_episode(self, stream_id: int) -> bool:
        """Crear un episodio huérfano (sin serie asignada) para testing"""
        # Esto es solo para testing - crear un episodio sin series_id válido
        query = "INSERT INTO streams_episodes (season_num, episode_num, series_id, stream_id) VALUES (1, 1, 99999, %s)"
        return self.execute_update(query, (stream_id,))

    def get_duplicate_episodes(self) -> List[Dict]:
        """Obtener episodios duplicados basándose en series_id, season_num y episode_num"""
        query = """
        SELECT
            se.series_id,
            se.season_num,
            se.episode_num,
            COUNT(*) as duplicate_count,
            GROUP_CONCAT(se.id) as episode_ids,
            GROUP_CONCAT(se.stream_id) as stream_ids,
            ss.title as series_title,
            GROUP_CONCAT(s.stream_display_name) as episode_titles
        FROM streams_episodes se
        LEFT JOIN streams_series ss ON se.series_id = ss.id
        LEFT JOIN streams s ON se.stream_id = s.id
        WHERE se.series_id IS NOT NULL
        AND se.series_id != 0
        AND se.season_num IS NOT NULL
        AND se.episode_num IS NOT NULL
        GROUP BY se.series_id, se.season_num, se.episode_num
        HAVING COUNT(*) > 1
        ORDER BY duplicate_count DESC, ss.title, se.season_num, se.episode_num
        LIMIT 5000
        """
        return self.execute_query(query)

    def get_duplicate_episodes_detailed(self) -> List[Dict]:
        """Obtener episodios duplicados con información detallada de calidad y prioridad"""
        query = """
        SELECT
            se.series_id,
            se.season_num,
            se.episode_num,
            COUNT(*) as duplicate_count,
            ss.title as series_title,
            GROUP_CONCAT(
                CONCAT(se.id, ':', se.stream_id, ':',
                       IFNULL(s.movie_symlink, 0), ':',
                       IFNULL(s.direct_source, 0), ':',
                       IFNULL(s.direct_proxy, 0), ':',
                       IFNULL(s.stream_display_name, ''))
                ORDER BY s.movie_symlink DESC, s.id ASC
                SEPARATOR '|'
            ) as episodes_info,
            SUM(CASE WHEN s.movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_count,
            SUM(CASE WHEN s.direct_source = 1 OR s.direct_proxy = 1 THEN 1 ELSE 0 END) as direct_count,
            SUM(CASE WHEN IFNULL(s.movie_symlink, 0) = 0 AND IFNULL(s.direct_source, 0) = 0 AND IFNULL(s.direct_proxy, 0) = 0 THEN 1 ELSE 0 END) as other_count
        FROM streams_episodes se
        LEFT JOIN streams_series ss ON se.series_id = ss.id
        LEFT JOIN streams s ON se.stream_id = s.id
        WHERE se.series_id IS NOT NULL
        AND se.series_id != 0
        AND se.season_num IS NOT NULL
        AND se.episode_num IS NOT NULL
        GROUP BY se.series_id, se.season_num, se.episode_num
        HAVING COUNT(*) > 1
        ORDER BY duplicate_count DESC, ss.title, se.season_num, se.episode_num
        LIMIT 5000
        """
        return self.execute_query(query)

    def get_intelligent_episode_duplicates(self) -> List[Dict]:
        """Detección inteligente de episodios duplicados con análisis avanzado de similitud"""
        try:
            query = """
            SELECT
                se.series_id,
                se.season_num,
                se.episode_num,
                COUNT(*) as duplicate_count,
                ss.title as series_title,
                GROUP_CONCAT(DISTINCT se.stream_id ORDER BY s.movie_symlink DESC, se.id ASC) as stream_ids,
                SUM(CASE WHEN s.movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_count,
                SUM(CASE WHEN s.direct_source = 1 OR s.direct_proxy = 1 THEN 1 ELSE 0 END) as direct_count,
                SUM(CASE WHEN s.movie_symlink = 0 AND s.direct_source = 0 AND s.direct_proxy = 0 THEN 1 ELSE 0 END) as other_count,
                GROUP_CONCAT(
                    DISTINCT CASE
                        WHEN LOWER(s.stream_display_name) LIKE '%4k%' OR LOWER(s.stream_display_name) LIKE '%2160p%' THEN '4K'
                        WHEN LOWER(s.stream_display_name) LIKE '%60fps%' THEN '60FPS'
                        WHEN LOWER(s.stream_display_name) LIKE '%1080p%' OR LOWER(s.stream_display_name) LIKE '%fhd%' THEN 'FHD'
                        WHEN LOWER(s.stream_display_name) LIKE '%720p%' OR LOWER(s.stream_display_name) LIKE '%hd%' THEN 'HD'
                        WHEN LOWER(s.stream_display_name) LIKE '%480p%' OR LOWER(s.stream_display_name) LIKE '%sd%' THEN 'SD'
                        ELSE 'STD'
                    END
                ) as quality_variations,
                GROUP_CONCAT(
                    DISTINCT CASE
                        WHEN LOWER(s.stream_display_name) LIKE '%latino%' OR LOWER(s.stream_display_name) LIKE '%spanish%' THEN 'ES'
                        WHEN LOWER(s.stream_display_name) LIKE '%english%' THEN 'EN'
                        WHEN LOWER(s.stream_display_name) LIKE '%subtitulado%' OR LOWER(s.stream_display_name) LIKE '%sub%' THEN 'SUB'
                        ELSE 'UNK'
                    END
                ) as language_variations,
                CASE
                    WHEN SUM(CASE WHEN s.movie_symlink = 1 THEN 1 ELSE 0 END) > 0
                         AND SUM(CASE WHEN s.direct_source = 1 OR s.direct_proxy = 1 THEN 1 ELSE 0 END) > 0 THEN 'MIXED'
                    WHEN SUM(CASE WHEN s.movie_symlink = 1 THEN 1 ELSE 0 END) > 1 THEN 'MULTI_SYMLINK'
                    WHEN SUM(CASE WHEN s.direct_source = 1 OR s.direct_proxy = 1 THEN 1 ELSE 0 END) > 1 THEN 'MULTI_DIRECT'
                    ELSE 'SINGLE'
                END as duplicate_type,
                CASE
                    WHEN SUM(CASE WHEN s.movie_symlink = 1 THEN 1 ELSE 0 END) > 0 THEN 'AUTO_SAFE'
                    WHEN COUNT(*) <= 3 THEN 'MANUAL_REVIEW'
                    ELSE 'BULK_CLEANUP'
                END as recommended_action,
                ROUND(AVG(s.added), 0) as avg_added_timestamp,
                MAX(s.added) as newest_added,
                MIN(s.added) as oldest_added
            FROM streams_episodes se
            LEFT JOIN streams_series ss ON se.series_id = ss.id
            LEFT JOIN streams s ON se.stream_id = s.id
            WHERE se.series_id IS NOT NULL
            AND se.series_id != 0
            AND se.season_num IS NOT NULL
            AND se.episode_num IS NOT NULL
            AND s.stream_display_name IS NOT NULL
            AND s.stream_display_name != ''
            GROUP BY se.series_id, se.season_num, se.episode_num
            HAVING COUNT(*) > 1
            ORDER BY
                CASE
                    WHEN duplicate_type = 'MIXED' THEN 1
                    WHEN duplicate_type = 'MULTI_DIRECT' THEN 2
                    WHEN duplicate_type = 'MULTI_SYMLINK' THEN 3
                    ELSE 4
                END,
                duplicate_count DESC,
                ss.title,
                se.season_num,
                se.episode_num
            LIMIT 5000
            """

            results = self.execute_query(query)

            if results:
                logging.info(f"Intelligent episode duplicates analysis completed: {len(results)} groups found")
                return results
            else:
                logging.info("No intelligent episode duplicates found")
                return []

        except Exception as e:
            logging.error(f"Error in intelligent episode duplicates detection: {e}")
            return []

    def get_episode_copies_details(self, series_id: int, season_num: int, episode_num: int) -> List[Dict]:
        """Obtener detalles de todas las copias de un episodio duplicado"""
        try:
            query = """
            SELECT
                se.id as episode_id,
                se.stream_id,
                s.stream_display_name as title,
                s.movie_symlink,
                s.direct_source,
                s.direct_proxy,
                s.stream_source,
                s.added,
                CASE
                    WHEN s.movie_symlink = 1 THEN 'Symlink (ALTA)'
                    WHEN s.direct_source = 1 AND s.direct_proxy = 1 THEN 'Direct Source/Proxy (BAJA)'
                    ELSE 'Otro'
                END as priority_type,
                CASE
                    WHEN s.movie_symlink = 1 THEN 1
                    WHEN s.direct_source = 1 AND s.direct_proxy = 1 THEN 3
                    ELSE 2
                END as priority_order,
                COALESCE(
                    CASE
                        WHEN LOWER(s.stream_display_name) LIKE '%%4k%%' OR LOWER(s.stream_display_name) LIKE '%%2160p%%' THEN '4K'
                        WHEN LOWER(s.stream_display_name) LIKE '%%fhd%%' OR LOWER(s.stream_display_name) LIKE '%%1080p%%' THEN 'FHD'
                        WHEN LOWER(s.stream_display_name) LIKE '%%hd%%' OR LOWER(s.stream_display_name) LIKE '%%720p%%' THEN 'HD'
                        WHEN LOWER(s.stream_display_name) LIKE '%%60fps%%' THEN '60FPS'
                        WHEN LOWER(s.stream_display_name) LIKE '%%extended%%' THEN 'EXTENDED'
                        ELSE 'STANDARD'
                    END, 'STANDARD'
                ) as detected_quality,
                ss.title as series_title
            FROM streams_episodes se
            JOIN streams s ON se.stream_id = s.id
            JOIN streams_series ss ON se.series_id = ss.id
            WHERE se.series_id = %s
            AND se.season_num = %s
            AND se.episode_num = %s
            ORDER BY priority_order ASC, s.id DESC
            """

            result = self.execute_query(query, (series_id, season_num, episode_num))

            if result:
                copies = []
                for row in result:
                    copies.append({
                        'episode_id': row['episode_id'],
                        'stream_id': row['stream_id'],
                        'title': row['title'] or f"Episode {row['stream_id']}",
                        'movie_symlink': row['movie_symlink'],
                        'direct_source': row['direct_source'],
                        'direct_proxy': row['direct_proxy'],
                        'stream_source': row['stream_source'],
                        'priority_type': row['priority_type'],
                        'priority_order': row['priority_order'],
                        'detected_quality': row['detected_quality'],
                        'series_title': row['series_title'],
                        'added': row['added']
                    })
                return copies

            return []

        except Exception as e:
            print(f"Error getting episode copies details: {e}")
            return []

    def get_series_episodes_detailed(self, series_id: int) -> List[Dict]:
        """Obtener todos los episodios de una serie con detalles completos"""
        query = """
        SELECT
            se.id as episode_id,
            se.season_num,
            se.episode_num,
            se.stream_id,
            s.stream_display_name as episode_title,
            s.type,
            st.type_name,
            s.movie_symlink,
            s.direct_source,
            s.direct_proxy,
            s.added
        FROM streams_episodes se
        LEFT JOIN streams s ON se.stream_id = s.id
        LEFT JOIN streams_types st ON s.type = st.type_id
        WHERE se.series_id = %s
        ORDER BY se.season_num, se.episode_num, se.id
        """
        return self.execute_query(query, (series_id,))

    def get_orphaned_episodes_detailed(self) -> List[Dict]:
        """Obtener episodios huérfanos con información detallada"""
        query = """
        SELECT
            se.id as episode_id,
            se.stream_id,
            se.series_id,
            se.season_num,
            se.episode_num,
            s.stream_display_name as episode_title,
            s.type,
            st.type_name,
            s.added,
            CASE
                WHEN se.series_id IS NULL THEN 'NULL series_id'
                WHEN se.series_id = 0 THEN 'Zero series_id'
                WHEN ss.id IS NULL THEN 'Invalid series_id'
                ELSE 'Unknown'
            END as orphan_reason
        FROM streams_episodes se
        LEFT JOIN streams_series ss ON se.series_id = ss.id
        LEFT JOIN streams s ON se.stream_id = s.id
        LEFT JOIN streams_types st ON s.type = st.type_id
        WHERE (se.series_id IS NULL OR se.series_id = 0 OR ss.id IS NULL)
        ORDER BY se.id
        LIMIT 500
        """
        return self.execute_query(query)

    def get_orphaned_episodes_smart(self) -> List[Dict]:
        """Obtener episodios huérfanos con análisis inteligente de razones"""
        query = """
        SELECT
            se.id as episode_id,
            se.stream_id,
            se.series_id,
            se.season_num,
            se.episode_num,
            s.stream_display_name as episode_title,
            s.type,
            st.type_name,
            s.added,
            s.movie_symlink,
            s.direct_source,
            s.direct_proxy,
            CASE
                WHEN se.series_id IS NULL THEN 'NULL series_id'
                WHEN se.series_id = 0 THEN 'Zero series_id'
                WHEN ss.id IS NULL THEN 'Invalid series_id (series deleted)'
                WHEN s.id IS NULL THEN 'Stream deleted but episode exists'
                ELSE 'Unknown orphan type'
            END as orphan_reason,
            CASE
                WHEN s.movie_symlink = 1 THEN 'HIGH (Symlink)'
                WHEN s.direct_source = 1 OR s.direct_proxy = 1 THEN 'LOW (Direct)'
                ELSE 'MEDIUM (Other)'
            END as quality_priority
        FROM streams_episodes se
        LEFT JOIN streams_series ss ON se.series_id = ss.id
        LEFT JOIN streams s ON se.stream_id = s.id
        LEFT JOIN streams_types st ON s.type = st.type_id
        WHERE (se.series_id IS NULL OR se.series_id = 0 OR ss.id IS NULL OR s.id IS NULL)
        ORDER BY
            CASE
                WHEN s.movie_symlink = 1 THEN 1
                WHEN s.direct_source = 1 OR s.direct_proxy = 1 THEN 3
                ELSE 2
            END ASC,
            se.id
        LIMIT 500
        """
        return self.execute_query(query)

    def fix_orphaned_episode(self, episode_id: int, new_series_id: int) -> bool:
        """Reparar un episodio huérfano asignándolo a una serie"""
        query = "UPDATE streams_episodes SET series_id = %s WHERE id = %s"
        return self.execute_update(query, (new_series_id, episode_id))

    def delete_duplicate_episode(self, episode_id: int) -> bool:
        """Eliminar un episodio duplicado"""
        query = "DELETE FROM streams_episodes WHERE id = %s"
        return self.execute_update(query, (episode_id,))

    def clean_orphaned_episode_references(self, progress_callback=None) -> Dict[str, int]:
        """Limpiar referencias huérfanas en streams_episodes que apuntan a streams.id inexistentes"""
        try:
            # Primero, encontrar registros huérfanos
            find_orphans_query = """
            SELECT se.id, se.stream_id, se.series_id, se.season_num, se.episode_num
            FROM streams_episodes se
            LEFT JOIN streams s ON se.stream_id = s.id
            WHERE s.id IS NULL
            """

            orphaned_refs = self.execute_query(find_orphans_query)

            if not orphaned_refs:
                return {"found": 0, "deleted": 0, "failed": 0}

            # Eliminar registros huérfanos con progreso
            deleted_count = 0
            failed_count = 0
            total_refs = len(orphaned_refs)

            for i, orphan in enumerate(orphaned_refs):
                delete_query = "DELETE FROM streams_episodes WHERE id = %s"
                if self.execute_update(delete_query, (orphan['id'],)):
                    deleted_count += 1
                else:
                    failed_count += 1

                # Reportar progreso cada 10 registros o al final
                if progress_callback and (i % 10 == 0 or i == total_refs - 1):
                    progress_percent = ((i + 1) / total_refs) * 100
                    progress_callback(i + 1, total_refs, progress_percent, deleted_count, failed_count)

            return {
                "found": len(orphaned_refs),
                "deleted": deleted_count,
                "failed": failed_count
            }

        except Exception as e:
            logging.error(f"Error cleaning orphaned episode references: {e}")
            return {"found": 0, "deleted": 0, "failed": 0, "error": str(e)}

    def get_orphaned_episode_references(self) -> List[Dict]:
        """Obtener lista detallada de referencias huérfanas en streams_episodes"""
        query = """
        SELECT
            se.id as episode_ref_id,
            se.stream_id as missing_stream_id,
            se.series_id,
            se.season_num,
            se.episode_num,
            ss.title as series_title
        FROM streams_episodes se
        LEFT JOIN streams s ON se.stream_id = s.id
        LEFT JOIN streams_series ss ON se.series_id = ss.id
        WHERE s.id IS NULL
        ORDER BY ss.title, se.season_num, se.episode_num
        LIMIT 500
        """
        return self.execute_query(query)

    def get_series_by_title_pattern(self, title_pattern: str) -> List[Dict]:
        """Buscar series por patrón de título"""
        query = """
        SELECT
            id as series_id,
            title,
            tmdb_id,
            year,
            genre,
            rating
        FROM streams_series
        WHERE title LIKE %s
        ORDER BY title
        LIMIT 50
        """
        return self.execute_query(query, (f"%{title_pattern}%",))

    def analyze_m3u_content(self, m3u_entries: List[Dict]) -> Dict:
        """Analizar contenido M3U contra la base de datos existente"""
        analysis = {
            'total_entries': len(m3u_entries),
            'existing_series': [],
            'missing_series': [],
            'existing_episodes': [],
            'missing_episodes': [],
            'potential_matches': []
        }

        for entry in m3u_entries:
            title = entry.get('title', '')

            # Detectar si es serie o película
            if self._is_series_entry(title):
                series_info = self._extract_series_info(title)

                # Buscar serie existente
                existing_series = self.get_series_by_title_pattern(series_info['series_title'])

                if existing_series:
                    analysis['existing_series'].append({
                        'entry': entry,
                        'series_info': series_info,
                        'matches': existing_series
                    })

                    # Verificar si el episodio específico existe
                    for series in existing_series:
                        episodes = self.get_series_episodes_detailed(series['series_id'])
                        episode_exists = any(
                            ep['season_num'] == series_info.get('season') and
                            ep['episode_num'] == series_info.get('episode')
                            for ep in episodes
                        )

                        if episode_exists:
                            analysis['existing_episodes'].append({
                                'entry': entry,
                                'series_info': series_info,
                                'series': series
                            })
                        else:
                            analysis['missing_episodes'].append({
                                'entry': entry,
                                'series_info': series_info,
                                'series': series
                            })
                else:
                    analysis['missing_series'].append({
                        'entry': entry,
                        'series_info': series_info
                    })

        return analysis

    def _is_series_entry(self, title: str) -> bool:
        """Detectar si un título corresponde a una serie"""
        series_patterns = [
            r'S\d+E\d+',  # S01E01
            r'Season\s+\d+',  # Season 1
            r'Temporada\s+\d+',  # Temporada 1
            r'\d+x\d+',  # 1x01
            r'Episode\s+\d+',  # Episode 1
            r'Episodio\s+\d+',  # Episodio 1
        ]

        import re
        for pattern in series_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                return True
        return False

    def _extract_series_info(self, title: str) -> Dict:
        """Extraer información de serie del título"""
        import re

        info = {
            'series_title': title,
            'season': None,
            'episode': None,
            'episode_title': None
        }

        # Patrón S01E01
        match = re.search(r'(.+?)\s*S(\d+)E(\d+)(?:\s*-?\s*(.+))?', title, re.IGNORECASE)
        if match:
            info['series_title'] = match.group(1).strip()
            info['season'] = int(match.group(2))
            info['episode'] = int(match.group(3))
            if match.group(4):
                info['episode_title'] = match.group(4).strip()
            return info

        # Patrón 1x01
        match = re.search(r'(.+?)\s*(\d+)x(\d+)(?:\s*-?\s*(.+))?', title, re.IGNORECASE)
        if match:
            info['series_title'] = match.group(1).strip()
            info['season'] = int(match.group(2))
            info['episode'] = int(match.group(3))
            if match.group(4):
                info['episode_title'] = match.group(4).strip()
            return info

        # Patrón Season/Temporada
        match = re.search(r'(.+?)\s*(?:Season|Temporada)\s+(\d+).*?(?:Episode|Episodio)\s+(\d+)(?:\s*-?\s*(.+))?', title, re.IGNORECASE)
        if match:
            info['series_title'] = match.group(1).strip()
            info['season'] = int(match.group(2))
            info['episode'] = int(match.group(3))
            if match.group(4):
                info['episode_title'] = match.group(4).strip()
            return info

        return info

    def get_series_statistics(self) -> Dict:
        """Obtener estadísticas de series y episodios"""
        stats = {}

        # Total de series
        series_count = self.execute_query("SELECT COUNT(*) as count FROM streams_series")
        stats['total_series'] = series_count[0]['count'] if series_count else 0

        # Total de episodios
        episodes_count = self.execute_query("SELECT COUNT(*) as count FROM streams_episodes")
        stats['total_episodes'] = episodes_count[0]['count'] if episodes_count else 0

        # Series sin episodios
        series_no_episodes = self.execute_query("""
            SELECT COUNT(*) as count
            FROM streams_series ss
            LEFT JOIN streams_episodes se ON ss.id = se.series_id
            WHERE se.id IS NULL
        """)
        stats['series_without_episodes'] = series_no_episodes[0]['count'] if series_no_episodes else 0

        # Episodios huérfanos
        orphaned = self.execute_query("""
            SELECT COUNT(*) as count
            FROM streams_episodes se
            LEFT JOIN streams_series ss ON se.series_id = ss.id
            WHERE se.series_id IS NULL OR se.series_id = 0 OR ss.id IS NULL
        """)
        stats['orphaned_episodes'] = orphaned[0]['count'] if orphaned else 0

        # Episodios duplicados
        duplicates = self.execute_query("""
            SELECT COUNT(*) as count FROM (
                SELECT series_id, season_num, episode_num
                FROM streams_episodes
                WHERE series_id IS NOT NULL AND series_id != 0
                GROUP BY series_id, season_num, episode_num
                HAVING COUNT(*) > 1
            ) as dup
        """)
        stats['duplicate_episodes'] = duplicates[0]['count'] if duplicates else 0

        return stats

    def get_duplicate_movies_with_priority(self) -> List[Dict]:
        """Obtener películas duplicadas con información de prioridad"""
        query = """
        SELECT
            s.tmdb_id,
            COUNT(*) as duplicate_count,
            MAX(s.stream_display_name) as sample_title,
            GROUP_CONCAT(CONCAT(s.id, ':', s.movie_symlink, ':', s.direct_source, ':', s.direct_proxy)
                        ORDER BY s.movie_symlink DESC, s.direct_source ASC, s.direct_proxy ASC) as copies_info,
            SUM(CASE WHEN s.movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_count,
            SUM(CASE WHEN s.direct_source = 1 AND s.direct_proxy = 1 THEN 1 ELSE 0 END) as direct_count,
            SUM(CASE WHEN s.movie_symlink = 0 AND s.direct_source = 0 AND s.direct_proxy = 0 THEN 1 ELSE 0 END) as other_count
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        AND s.tmdb_id IS NOT NULL
        AND s.tmdb_id != 0
        GROUP BY s.tmdb_id
        HAVING COUNT(*) > 1
        ORDER BY duplicate_count DESC, sample_title
        LIMIT 5000
        """
        return self.execute_query(query)

    def get_intelligent_movie_duplicates(self) -> List[Dict]:
        """Detección inteligente de películas duplicadas con análisis de similitud avanzado"""
        try:
            query = """
            SELECT
                s.tmdb_id,
                COUNT(*) as duplicate_count,
                MAX(s.stream_display_name) as sample_title,
                GROUP_CONCAT(DISTINCT s.id ORDER BY s.movie_symlink DESC, s.id ASC) as stream_ids,
                SUM(CASE WHEN s.movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_count,
                SUM(CASE WHEN s.direct_source = 1 OR s.direct_proxy = 1 THEN 1 ELSE 0 END) as direct_count,
                SUM(CASE WHEN s.movie_symlink = 0 AND s.direct_source = 0 AND s.direct_proxy = 0 THEN 1 ELSE 0 END) as other_count,
                GROUP_CONCAT(
                    DISTINCT CASE
                        WHEN LOWER(s.stream_display_name) LIKE '%4k%' OR LOWER(s.stream_display_name) LIKE '%2160p%' THEN '4K'
                        WHEN LOWER(s.stream_display_name) LIKE '%60fps%' THEN '60FPS'
                        WHEN LOWER(s.stream_display_name) LIKE '%1080p%' OR LOWER(s.stream_display_name) LIKE '%fhd%' THEN 'FHD'
                        WHEN LOWER(s.stream_display_name) LIKE '%720p%' OR LOWER(s.stream_display_name) LIKE '%hd%' THEN 'HD'
                        WHEN LOWER(s.stream_display_name) LIKE '%480p%' OR LOWER(s.stream_display_name) LIKE '%sd%' THEN 'SD'
                        ELSE 'STD'
                    END
                ) as quality_variations,
                GROUP_CONCAT(
                    DISTINCT CASE
                        WHEN LOWER(s.stream_display_name) LIKE '%latino%' OR LOWER(s.stream_display_name) LIKE '%spanish%' THEN 'ES'
                        WHEN LOWER(s.stream_display_name) LIKE '%english%' THEN 'EN'
                        WHEN LOWER(s.stream_display_name) LIKE '%subtitulado%' OR LOWER(s.stream_display_name) LIKE '%sub%' THEN 'SUB'
                        ELSE 'UNK'
                    END
                ) as language_variations,
                CASE
                    WHEN SUM(CASE WHEN s.movie_symlink = 1 THEN 1 ELSE 0 END) > 0
                         AND SUM(CASE WHEN s.direct_source = 1 OR s.direct_proxy = 1 THEN 1 ELSE 0 END) > 0 THEN 'MIXED'
                    WHEN SUM(CASE WHEN s.movie_symlink = 1 THEN 1 ELSE 0 END) > 1 THEN 'MULTI_SYMLINK'
                    WHEN SUM(CASE WHEN s.direct_source = 1 OR s.direct_proxy = 1 THEN 1 ELSE 0 END) > 1 THEN 'MULTI_DIRECT'
                    ELSE 'SINGLE'
                END as duplicate_type,
                CASE
                    WHEN SUM(CASE WHEN s.movie_symlink = 1 THEN 1 ELSE 0 END) > 0 THEN 'AUTO_SAFE'
                    WHEN COUNT(*) <= 3 THEN 'MANUAL_REVIEW'
                    ELSE 'BULK_CLEANUP'
                END as recommended_action
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies'
            AND s.tmdb_id IS NOT NULL
            AND s.tmdb_id != 0
            AND s.stream_display_name IS NOT NULL
            AND s.stream_display_name != ''
            GROUP BY s.tmdb_id
            HAVING COUNT(*) > 1
            ORDER BY
                CASE
                    WHEN duplicate_type = 'MIXED' THEN 1
                    WHEN duplicate_type = 'MULTI_DIRECT' THEN 2
                    WHEN duplicate_type = 'MULTI_SYMLINK' THEN 3
                    ELSE 4
                END,
                duplicate_count DESC,
                sample_title
            LIMIT 5000
            """

            results = self.execute_query(query)

            if results:
                logging.info(f"Intelligent movie duplicates analysis completed: {len(results)} groups found")
                return results
            else:
                logging.info("No intelligent movie duplicates found")
                return []

        except Exception as e:
            logging.error(f"Error in intelligent movie duplicates detection: {e}")
            return []

    def get_similar_movies_by_title(self, similarity_threshold: float = 0.8) -> List[Dict]:
        """Detectar películas similares por título usando fuzzy matching"""
        try:
            # Obtener todas las películas para análisis de similitud
            query = """
            SELECT
                s.id as stream_id,
                s.stream_display_name as title,
                s.tmdb_id,
                s.movie_symlink,
                s.direct_source,
                s.direct_proxy,
                s.year,
                CASE
                    WHEN LOWER(s.stream_display_name) LIKE '%4k%' OR LOWER(s.stream_display_name) LIKE '%2160p%' THEN '4K'
                    WHEN LOWER(s.stream_display_name) LIKE '%60fps%' THEN '60FPS'
                    WHEN LOWER(s.stream_display_name) LIKE '%1080p%' OR LOWER(s.stream_display_name) LIKE '%fhd%' THEN 'FHD'
                    WHEN LOWER(s.stream_display_name) LIKE '%720p%' OR LOWER(s.stream_display_name) LIKE '%hd%' THEN 'HD'
                    WHEN LOWER(s.stream_display_name) LIKE '%480p%' OR LOWER(s.stream_display_name) LIKE '%sd%' THEN 'SD'
                    ELSE 'STD'
                END as detected_quality
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies'
            AND s.stream_display_name IS NOT NULL
            AND s.stream_display_name != ''
            AND LENGTH(s.stream_display_name) > 5
            ORDER BY s.stream_display_name
            """

            movies = self.execute_query(query)

            if not movies:
                return []

            similar_groups = []
            processed_ids = set()

            for i, movie1 in enumerate(movies):
                if movie1['stream_id'] in processed_ids:
                    continue

                similar_movies = [movie1]
                processed_ids.add(movie1['stream_id'])

                # Comparar con películas restantes
                for j, movie2 in enumerate(movies[i+1:], i+1):
                    if movie2['stream_id'] in processed_ids:
                        continue

                    # Calcular similitud de títulos
                    similarity = self._calculate_title_similarity(movie1['title'], movie2['title'])

                    if similarity >= similarity_threshold:
                        similar_movies.append(movie2)
                        processed_ids.add(movie2['stream_id'])

                # Si encontramos similares, agregar al grupo
                if len(similar_movies) > 1:
                    group = {
                        'group_id': len(similar_groups) + 1,
                        'movies': similar_movies,
                        'count': len(similar_movies),
                        'base_title': self._extract_base_title(movie1['title']),
                        'symlink_count': sum(1 for m in similar_movies if m['movie_symlink'] == 1),
                        'direct_count': sum(1 for m in similar_movies if m['direct_source'] == 1 or m['direct_proxy'] == 1),
                        'quality_variations': list(set(m['detected_quality'] for m in similar_movies)),
                        'tmdb_variations': list(set(str(m['tmdb_id']) for m in similar_movies if m['tmdb_id'])),
                        'similarity_score': max(self._calculate_title_similarity(movie1['title'], m['title']) for m in similar_movies[1:]) if len(similar_movies) > 1 else 1.0
                    }
                    similar_groups.append(group)

            # Ordenar por relevancia
            similar_groups.sort(key=lambda x: (x['count'], x['similarity_score']), reverse=True)

            logging.info(f"Similar movies by title analysis completed: {len(similar_groups)} groups found")
            return similar_groups[:100]  # Limitar a top 100

        except Exception as e:
            logging.error(f"Error in similar movies by title detection: {e}")
            return []

    def _calculate_title_similarity(self, title1: str, title2: str) -> float:
        """Calcular similitud entre dos títulos usando algoritmo simple"""
        try:
            # Normalizar títulos
            norm1 = self._normalize_title(title1)
            norm2 = self._normalize_title(title2)

            if not norm1 or not norm2:
                return 0.0

            # Similitud exacta
            if norm1 == norm2:
                return 1.0

            # Similitud por contenido (uno contiene al otro)
            if norm1 in norm2 or norm2 in norm1:
                return 0.9

            # Similitud por palabras comunes
            words1 = set(norm1.split())
            words2 = set(norm2.split())

            if not words1 or not words2:
                return 0.0

            common_words = words1.intersection(words2)
            total_words = words1.union(words2)

            if len(total_words) == 0:
                return 0.0

            return len(common_words) / len(total_words)

        except Exception:
            return 0.0

    def _normalize_title(self, title: str) -> str:
        """Normalizar título para comparación"""
        try:
            import re

            # Convertir a minúsculas
            normalized = title.lower()

            # Remover años
            normalized = re.sub(r'\b(19|20)\d{2}\b', '', normalized)

            # Remover calidades
            quality_patterns = [
                r'\b(4k|uhd|2160p|1080p|720p|480p|fhd|hd|sd)\b',
                r'\b(60fps|120fps)\b',
                r'\b(hdr|dolby|atmos)\b',
                r'\b(extended|director|remastered|uncut)\b',
                r'\b(latino|spanish|english|subtitulado|sub)\b'
            ]

            for pattern in quality_patterns:
                normalized = re.sub(pattern, '', normalized)

            # Remover caracteres especiales
            normalized = re.sub(r'[^\w\s]', ' ', normalized)

            # Remover espacios múltiples
            normalized = re.sub(r'\s+', ' ', normalized)

            return normalized.strip()

        except Exception:
            return title.lower()

    def _extract_base_title(self, title: str) -> str:
        """Extraer título base sin calidades ni años"""
        normalized = self._normalize_title(title)
        words = normalized.split()

        # Tomar las primeras 3-4 palabras como título base
        if len(words) >= 3:
            return ' '.join(words[:3])
        else:
            return normalized

    def get_movie_copies_details(self, tmdb_id: int) -> List[Dict]:
        """Obtener detalles de todas las copias de una película por TMDB ID"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.movie_symlink,
            s.direct_source,
            s.direct_proxy,
            s.stream_source,
            s.year,
            s.rating,
            CASE
                WHEN s.movie_symlink = 1 THEN 'Symlink (ALTA)'
                WHEN s.direct_source = 1 AND s.direct_proxy = 1 THEN 'Direct Source/Proxy (BAJA)'
                ELSE 'Otro'
            END as priority_type,
            CASE
                WHEN s.movie_symlink = 1 THEN 1
                WHEN s.direct_source = 1 AND s.direct_proxy = 1 THEN 3
                ELSE 2
            END as priority_order
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        AND s.tmdb_id = %s
        ORDER BY priority_order ASC, s.id ASC
        """
        return self.execute_query(query, (tmdb_id,))

    def get_recommended_deletions(self, tmdb_id: int) -> Dict:
        """Obtener recomendaciones SEGURAS de qué copias eliminar basándose en prioridades

        REGLAS ESTRICTAS:
        1. NUNCA eliminar symlinks automáticamente (gestión manual)
        2. Solo eliminar direct sources cuando hay symlinks disponibles
        3. Preservar variaciones de calidad en symlinks
        4. Mostrar recomendaciones inteligentes pero seguras
        """
        copies = self.get_movie_copies_details(tmdb_id)

        if not copies:
            return {"keep": [], "delete": [], "reason": "No se encontraron copias"}

        # Separar por tipo de prioridad (corregir lógica de direct_sources)
        symlinks = [c for c in copies if c['movie_symlink'] == 1]
        direct_sources = [c for c in copies if (c['direct_source'] == 1 or c['direct_proxy'] == 1) and c['movie_symlink'] != 1]
        others = [c for c in copies if c['movie_symlink'] != 1 and c['direct_source'] != 1 and c['direct_proxy'] != 1]

        keep = []
        delete = []
        reason = ""

        if symlinks:
            # REGLA CRÍTICA: MANTENER TODOS LOS SYMLINKS (gestión manual)
            keep.extend(symlinks)  # ✅ TODOS los symlinks se mantienen

            # Solo eliminar direct sources si hay symlinks disponibles
            delete.extend(direct_sources)
            delete.extend(others)

            # Razón detallada y segura
            if len(symlinks) > 1:
                reason = f"SAFE: Mantener {len(symlinks)} symlinks (manual deletion required), eliminar {len(direct_sources)} direct sources"
            else:
                reason = f"SAFE: Mantener 1 symlink (prioridad máxima), eliminar {len(direct_sources)} direct sources"

        elif direct_sources:
            # Si NO hay symlinks, mantener el direct source más reciente
            direct_sources_sorted = sorted(direct_sources, key=lambda x: x.get('stream_id', 0), reverse=True)
            keep.append(direct_sources_sorted[0])  # Mantener el más reciente

            if len(direct_sources_sorted) > 1:
                delete.extend(direct_sources_sorted[1:])  # Eliminar los demás
            delete.extend(others)

            reason = f"SAFE: Mantener 1 direct source más reciente, eliminar {len(direct_sources)-1} direct duplicados"

        else:
            # Si solo hay "otros", mantener el más reciente
            if others:
                others_sorted = sorted(others, key=lambda x: x.get('stream_id', 0), reverse=True)
                keep.append(others_sorted[0])

                if len(others_sorted) > 1:
                    delete.extend(others_sorted[1:])

                reason = "SAFE: Mantener copia más reciente disponible"
            else:
                # No hay duplicados reales
                keep.extend(copies)
                reason = "SAFE: No hay duplicados para eliminar"

        return {
            "keep": keep,
            "delete": delete,
            "reason": reason,
            "total_copies": len(copies),
            "symlink_count": len(symlinks),
            "direct_count": len(direct_sources),
            "other_count": len(others),
            "safety_level": "HIGH",  # Nuevo: indicador de seguridad
            "symlinks_protected": True,  # Nuevo: confirmación de protección
            "manual_review_required": len(symlinks) > 1  # Nuevo: indica si requiere revisión manual
        }

    def apply_advanced_priority_cleanup(self, tmdb_id: int, auto_confirm: bool = False) -> Dict:
        """Aplicar limpieza SEGURA con prioridades avanzadas:

        REGLAS CRÍTICAS DE SEGURIDAD:
        1. NUNCA eliminar symlinks automáticamente (requiere gestión manual)
        2. Solo eliminar direct sources cuando hay symlinks disponibles
        3. Preservar TODAS las variaciones de calidad en symlinks
        4. Para direct sources: mantener solo el ID más nuevo

        IMPORTANTE: Esta función ahora es SEGURA y no elimina symlinks
        """
        try:
            # Obtener todas las copias
            copies = self.get_movie_copies_by_tmdb(tmdb_id)

            if len(copies) <= 1:
                return {"deleted": 0, "kept": len(copies), "message": "No hay duplicados para limpiar"}

            # Separar por tipos (corregir lógica de direct_sources)
            symlinks = [copy for copy in copies if copy['movie_symlink'] == 1]
            direct_sources = [copy for copy in copies if (copy['direct_source'] == 1 or copy['direct_proxy'] == 1) and copy['movie_symlink'] != 1]
            others = [copy for copy in copies if copy['movie_symlink'] != 1 and copy['direct_source'] != 1 and copy['direct_proxy'] != 1]

            keep_ids = []
            delete_ids = []

            # LÓGICA SEGURA PARA SYMLINKS
            if symlinks:
                # REGLA CRÍTICA: MANTENER TODOS LOS SYMLINKS (gestión manual requerida)
                keep_ids.extend([s['stream_id'] for s in symlinks])

                # Análisis de calidad para información (NO para eliminación automática)
                symlinks_4k = [s for s in symlinks if s.get('detected_quality') == '4K']
                symlinks_60fps = [s for s in symlinks if s.get('detected_quality') == '60FPS']
                symlinks_fhd_hd = [s for s in symlinks if s.get('detected_quality') in ['FHD', 'HD']]

                quality_info = f"4K:{len(symlinks_4k)}, 60FPS:{len(symlinks_60fps)}, FHD/HD:{len(symlinks_fhd_hd)}"

                # Solo eliminar direct sources (NUNCA symlinks)
                delete_ids.extend([d['stream_id'] for d in direct_sources])
                delete_ids.extend([o['stream_id'] for o in others])

                logic_applied = f"SAFE: Mantener {len(symlinks)} symlinks ({quality_info}), eliminar {len(direct_sources)} direct sources"
            else:
                # NO HAY SYMLINKS: Para direct sources, mantener solo el ID más nuevo
                if direct_sources:
                    # Ordenar por stream_id (ID más alto = más nuevo)
                    direct_sources_sorted = sorted(direct_sources, key=lambda x: x.get('stream_id', 0), reverse=True)
                    newest_direct = direct_sources_sorted[0]
                    keep_ids.append(newest_direct['stream_id'])
                    # Eliminar el resto de direct sources
                    delete_ids.extend([d['stream_id'] for d in direct_sources_sorted[1:]])
                    delete_ids.extend([o['stream_id'] for o in others])

                    logic_applied = f"SAFE: Mantener 1 direct source más reciente, eliminar {len(direct_sources)-1} direct duplicados"
                else:
                    # Solo otros: mantener el más reciente
                    if others:
                        others_sorted = sorted(others, key=lambda x: x.get('stream_id', 0), reverse=True)
                        keep_ids.append(others_sorted[0]['stream_id'])
                        delete_ids.extend([o['stream_id'] for o in others_sorted[1:]])
                        logic_applied = "SAFE: Mantener copia más reciente disponible"
                    else:
                        logic_applied = "SAFE: No hay duplicados para eliminar"

            if not auto_confirm:
                return {
                    "deleted": 0,
                    "kept": len(keep_ids),
                    "to_delete": len(delete_ids),
                    "message": "Vista previa de limpieza avanzada SEGURA",
                    "keep_ids": keep_ids,
                    "delete_ids": delete_ids,
                    "logic_applied": logic_applied,
                    "safety_level": "HIGH",
                    "symlinks_protected": True,
                    "manual_review_required": len(symlinks) > 1
                }

            # Aplicar eliminaciones SEGURAS (solo direct sources y others)
            deleted_count = 0
            for stream_id in delete_ids:
                # Verificación adicional: NO eliminar symlinks por error
                stream_info = next((c for c in copies if c['stream_id'] == stream_id), None)
                if stream_info and stream_info.get('movie_symlink') == 1:
                    logging.warning(f"SAFETY CHECK: Skipping symlink deletion (ID: {stream_id})")
                    continue

                if self.delete_stream(stream_id):
                    deleted_count += 1

            return {
                "deleted": deleted_count,
                "kept": len(keep_ids),
                "total_processed": len(copies),
                "message": f"Limpieza avanzada SEGURA aplicada: {deleted_count} eliminados, {len(keep_ids)} mantenidos",
                "logic_applied": logic_applied,
                "safety_level": "HIGH",
                "symlinks_protected": True
            }

        except Exception as e:
            logging.error(f"Error en limpieza avanzada para TMDB {tmdb_id}: {e}")
            return {"deleted": 0, "kept": 0, "message": f"Error: {str(e)}"}

    def _get_cleanup_logic_description(self, symlinks, direct_sources, others):
        """Obtener descripción de la lógica aplicada"""
        if symlinks:
            symlinks_4k = [s for s in symlinks if s['detected_quality'] == '4K']
            symlinks_60fps = [s for s in symlinks if s['detected_quality'] == '60FPS']
            symlinks_fhd_hd = [s for s in symlinks if s['detected_quality'] in ['FHD', 'HD']]

            if symlinks_4k:
                return "🥇 Mantenidos 4K symlinks, eliminados otros"
            elif symlinks_60fps:
                return "🥈 Mantenidos 60FPS symlinks, eliminados otros"
            elif symlinks_fhd_hd:
                return "🥉 Mantenidos FHD/HD symlinks, eliminados otros"
            else:
                return "🔗 Mantenidos todos los symlinks, eliminados direct sources"
        elif direct_sources:
            return "📡 Mantenido direct source más nuevo, eliminados otros"
        else:
            return "⚙️ Mantenidos otros tipos (sin symlinks ni direct sources)"

    def apply_smart_cleanup(self, tmdb_id: int, auto_confirm: bool = False) -> Dict:
        """Aplicar limpieza inteligente basada en prioridades"""
        recommendations = self.get_recommended_deletions(tmdb_id)

        if not recommendations["delete"]:
            return {"deleted": 0, "kept": len(recommendations["keep"]), "message": "No hay duplicados para eliminar"}

        if not auto_confirm:
            # En modo manual, solo devolver las recomendaciones
            return {
                "deleted": 0,
                "kept": len(recommendations["keep"]),
                "message": "Recomendaciones generadas (no aplicadas)",
                "recommendations": recommendations
            }

        # En modo automático, aplicar las eliminaciones
        deleted_count = 0
        delete_ids = [copy['stream_id'] for copy in recommendations["delete"]]

        for stream_id in delete_ids:
            if self.delete_movie(stream_id):
                deleted_count += 1

        return {
            "deleted": deleted_count,
            "kept": len(recommendations["keep"]),
            "message": f"Eliminados {deleted_count}/{len(delete_ids)} duplicados automáticamente",
            "recommendations": recommendations
        }

    def get_mass_cleanup_summary(self, tmdb_ids: List[int]) -> Dict:
        """Obtener resumen de limpieza masiva para múltiples TMDB IDs"""
        total_copies = 0
        total_to_keep = 0
        total_to_delete = 0
        groups_processed = 0

        summary_details = []

        for tmdb_id in tmdb_ids:
            recommendations = self.get_recommended_deletions(tmdb_id)

            if recommendations["total_copies"] > 0:
                groups_processed += 1
                total_copies += recommendations["total_copies"]
                total_to_keep += len(recommendations["keep"])
                total_to_delete += len(recommendations["delete"])

                # Obtener título para el resumen
                copies = self.get_movie_copies_details(tmdb_id)
                title = copies[0]['title'] if copies else f"TMDB {tmdb_id}"

                summary_details.append({
                    'tmdb_id': tmdb_id,
                    'title': title,
                    'total_copies': recommendations["total_copies"],
                    'to_keep': len(recommendations["keep"]),
                    'to_delete': len(recommendations["delete"]),
                    'reason': recommendations["reason"]
                })

        return {
            'groups_processed': groups_processed,
            'total_copies': total_copies,
            'total_to_keep': total_to_keep,
            'total_to_delete': total_to_delete,
            'reduction_percent': (total_to_delete / total_copies * 100) if total_copies > 0 else 0,
            'details': summary_details
        }

    def apply_mass_smart_cleanup(self, tmdb_ids: List[int]) -> Dict:
        """Aplicar limpieza inteligente masiva a múltiples TMDB IDs"""
        results = {
            'processed': 0,
            'total_deleted': 0,
            'total_kept': 0,
            'errors': 0,
            'details': []
        }

        for tmdb_id in tmdb_ids:
            try:
                result = self.apply_smart_cleanup(tmdb_id, auto_confirm=True)

                results['processed'] += 1
                results['total_deleted'] += result['deleted']
                results['total_kept'] += result['kept']

                if result['deleted'] == 0 and 'recommendations' in result:
                    if len(result['recommendations']['delete']) > 0:
                        results['errors'] += 1

                results['details'].append({
                    'tmdb_id': tmdb_id,
                    'deleted': result['deleted'],
                    'kept': result['kept'],
                    'message': result['message']
                })

            except Exception as e:
                results['errors'] += 1
                results['details'].append({
                    'tmdb_id': tmdb_id,
                    'deleted': 0,
                    'kept': 0,
                    'message': f"Error: {str(e)}"
                })
                logging.error(f"Error en limpieza masiva para TMDB {tmdb_id}: {e}")

        return results

    def get_movies_with_multiple_symlinks(self) -> List[Dict]:
        """Obtener películas que tienen múltiples symlinks (diferentes calidades)"""
        query = """
        SELECT
            s.tmdb_id,
            COUNT(*) as symlink_count,
            MAX(s.stream_display_name) as sample_title,
            GROUP_CONCAT(s.id ORDER BY s.id) as stream_ids,
            GROUP_CONCAT(s.stream_display_name ORDER BY s.id SEPARATOR '|||') as all_titles
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        AND s.movie_symlink = 1
        AND s.tmdb_id IS NOT NULL
        AND s.tmdb_id != 0
        GROUP BY s.tmdb_id
        HAVING COUNT(*) > 1
        ORDER BY symlink_count DESC, sample_title
        LIMIT 50
        """
        return self.execute_query(query)

    def get_symlink_details_for_manual_selection(self, tmdb_id: int) -> List[Dict]:
        """Obtener detalles de todos los symlinks de una película para selección manual"""
        query = """
        SELECT
            s.id as stream_id,
            s.stream_display_name as title,
            s.movie_symlink,
            s.direct_source,
            s.direct_proxy,
            s.stream_source,
            s.year,
            s.rating,
            s.target_container,
            s.added,
            CASE
                WHEN s.stream_display_name LIKE '%%4k%%' OR s.stream_display_name LIKE '%%4K%%' THEN '4K'
                WHEN s.stream_display_name LIKE '%%FHD%%' THEN 'FHD'
                WHEN s.stream_display_name LIKE '%%HD%%' THEN 'HD'
                WHEN s.stream_display_name LIKE '%%SD%%' THEN 'SD'
                WHEN s.stream_display_name LIKE '%%60fps%%' OR s.stream_display_name LIKE '%%60FPS%%' THEN '60FPS'
                WHEN s.stream_display_name LIKE '%%EXTENDIDA%%' OR s.stream_display_name LIKE '%%EXTENDED%%' THEN 'EXTENDED'
                ELSE 'STANDARD'
            END as detected_quality,
            CASE
                WHEN s.movie_symlink = 1 THEN 'Symlink (LOCAL)'
                WHEN s.direct_source = 1 AND s.direct_proxy = 1 THEN 'Direct Source/Proxy (REMOTO)'
                ELSE 'Otro'
            END as source_type,
            CASE
                WHEN s.stream_display_name LIKE '%%4k%%' OR s.stream_display_name LIKE '%%4K%%' THEN 1
                WHEN s.stream_display_name LIKE '%%FHD%%' THEN 2
                WHEN s.stream_display_name LIKE '%%60fps%%' OR s.stream_display_name LIKE '%%60FPS%%' THEN 3
                WHEN s.stream_display_name LIKE '%%HD%%' THEN 4
                WHEN s.stream_display_name LIKE '%%EXTENDIDA%%' OR s.stream_display_name LIKE '%%EXTENDED%%' THEN 5
                WHEN s.stream_display_name LIKE '%%SD%%' THEN 6
                ELSE 7
            END as quality_priority
        FROM streams s
        JOIN streams_types st ON s.type = st.type_id
        WHERE st.type_name = 'Movies'
        AND s.tmdb_id = %s
        ORDER BY s.movie_symlink DESC, quality_priority ASC, s.rating DESC, s.id ASC
        """
        return self.execute_query(query, (tmdb_id,))

    def get_manual_selection_recommendations(self, tmdb_id: int) -> Dict:
        """Obtener recomendaciones inteligentes para selección manual"""
        all_copies = self.get_symlink_details_for_manual_selection(tmdb_id)

        if not all_copies:
            return {"symlinks": [], "direct_sources": [], "recommendations": "No se encontraron copias"}

        # Separar symlinks de direct sources
        symlinks = [c for c in all_copies if c['movie_symlink'] == 1]
        direct_sources = [c for c in all_copies if c['direct_source'] == 1 and c['direct_proxy'] == 1]
        others = [c for c in all_copies if c['movie_symlink'] == 0 and c['direct_source'] == 0 and c['direct_proxy'] == 0]

        recommendations = ""

        if len(symlinks) > 1:
            # Múltiples symlinks - necesita selección manual
            qualities = set(s['detected_quality'] for s in symlinks)
            recommendations = f"MÚLTIPLES SYMLINKS DETECTADOS ({len(symlinks)})\n"
            recommendations += f"Calidades encontradas: {', '.join(sorted(qualities))}\n"
            recommendations += f"Recomendación: Revisar y seleccionar manualmente las calidades deseadas\n"
            recommendations += f"Sugerencia: Mantener 4K > FHD > 60FPS > HD > EXTENDED > STANDARD"
        elif len(symlinks) == 1:
            recommendations = f"UN SYMLINK ENCONTRADO\n"
            recommendations += f"Calidad: {symlinks[0]['detected_quality']}\n"
            recommendations += f"Recomendación: Mantener symlink, eliminar {len(direct_sources)} direct sources"
        else:
            recommendations = f"SIN SYMLINKS\n"
            recommendations += f"Recomendación: Mantener mejor direct source, eliminar {len(direct_sources)-1} duplicados"

        return {
            "symlinks": symlinks,
            "direct_sources": direct_sources,
            "others": others,
            "recommendations": recommendations,
            "total_copies": len(all_copies),
            "needs_manual_selection": len(symlinks) > 1
        }

    def get_content_without_tmdb(self, content_type: str = 'all') -> List[Dict]:
        """Obtener contenido sin TMDB ID asignado"""
        if content_type == 'movies':
            query = """
            SELECT
                s.id as stream_id,
                s.stream_display_name as title,
                s.year,
                s.movie_symlink,
                s.direct_source,
                s.direct_proxy,
                s.added,
                CASE
                    WHEN s.movie_symlink = 1 THEN 'HIGH (Symlink)'
                    WHEN s.direct_source = 1 OR s.direct_proxy = 1 THEN 'LOW (Direct)'
                    ELSE 'MEDIUM (Other)'
                END as quality_priority
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies'
            AND (s.tmdb_id IS NULL OR s.tmdb_id = 0)
            ORDER BY
                CASE
                    WHEN s.movie_symlink = 1 THEN 1
                    WHEN s.direct_source = 1 OR s.direct_proxy = 1 THEN 3
                    ELSE 2
                END ASC,
                s.year DESC,
                s.stream_display_name
            """
        elif content_type == 'series':
            query = """
            SELECT
                ss.id as series_id,
                ss.title,
                ss.year,
                ss.genre,
                ss.last_modified,
                COUNT(se.id) as episode_count,
                CASE
                    WHEN COUNT(se.id) = 0 THEN 'No episodes'
                    WHEN COUNT(se.id) < 5 THEN 'Few episodes'
                    ELSE 'Many episodes'
                END as episode_status
            FROM streams_series ss
            LEFT JOIN streams_episodes se ON ss.id = se.series_id
            WHERE (ss.tmdb_id IS NULL OR ss.tmdb_id = 0)
            GROUP BY ss.id, ss.title, ss.year, ss.genre, ss.last_modified
            ORDER BY COUNT(se.id) DESC, ss.year DESC, ss.title
            """
        else:  # all
            query = """
            SELECT
                'movie' as content_type,
                s.id as content_id,
                s.stream_display_name as title,
                s.year,
                s.movie_symlink,
                s.direct_source,
                s.direct_proxy,
                NULL as episode_count
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies'
            AND (s.tmdb_id IS NULL OR s.tmdb_id = 0)

            UNION ALL

            SELECT
                'series' as content_type,
                ss.id as content_id,
                ss.title,
                ss.year,
                NULL as movie_symlink,
                NULL as direct_source,
                NULL as direct_proxy,
                COUNT(se.id) as episode_count
            FROM streams_series ss
            LEFT JOIN streams_episodes se ON ss.id = se.series_id
            WHERE (ss.tmdb_id IS NULL OR ss.tmdb_id = 0)
            GROUP BY ss.id, ss.title, ss.year

            ORDER BY content_type, year DESC, title
            """

        return self.execute_query(query)

    def apply_manual_selection(self, tmdb_id: int, keep_stream_ids: List[int]) -> Dict:
        """Aplicar selección manual - mantener solo los IDs especificados"""
        # Obtener todas las copias
        all_copies = self.get_symlink_details_for_manual_selection(tmdb_id)

        if not all_copies:
            return {"deleted": 0, "kept": 0, "message": "No se encontraron copias"}

        # Identificar qué eliminar
        all_stream_ids = [copy['stream_id'] for copy in all_copies]
        delete_stream_ids = [sid for sid in all_stream_ids if sid not in keep_stream_ids]

        if not delete_stream_ids:
            return {"deleted": 0, "kept": len(keep_stream_ids), "message": "No hay elementos para eliminar"}

        # Eliminar los no seleccionados
        deleted_count = 0
        for stream_id in delete_stream_ids:
            if self.delete_movie(stream_id):
                deleted_count += 1

        return {
            "deleted": deleted_count,
            "kept": len(keep_stream_ids),
            "message": f"Selección manual aplicada: {deleted_count} eliminados, {len(keep_stream_ids)} mantenidos",
            "total_processed": len(all_stream_ids)
        }

    def get_movie_copies_by_tmdb(self, tmdb_id):
        """Obtener todas las copias de una película por TMDB ID con información detallada"""
        try:
            query = """
            SELECT
                s.id as stream_id,
                s.stream_display_name as title,
                s.movie_symlink,
                s.direct_source,
                s.direct_proxy,
                COALESCE(
                    CASE
                        WHEN LOWER(s.stream_display_name) LIKE '%%4k%%' OR LOWER(s.stream_display_name) LIKE '%%2160p%%' THEN '4K'
                        WHEN LOWER(s.stream_display_name) LIKE '%%fhd%%' OR LOWER(s.stream_display_name) LIKE '%%1080p%%' THEN 'FHD'
                        WHEN LOWER(s.stream_display_name) LIKE '%%hd%%' OR LOWER(s.stream_display_name) LIKE '%%720p%%' THEN 'HD'
                        WHEN LOWER(s.stream_display_name) LIKE '%%60fps%%' THEN '60FPS'
                        WHEN LOWER(s.stream_display_name) LIKE '%%extended%%' THEN 'EXTENDED'
                        ELSE 'STANDARD'
                    END, 'STANDARD'
                ) as detected_quality,
                s.tmdb_id as movie_tmdb_id
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies'
            AND s.tmdb_id = %s
            ORDER BY
                s.movie_symlink DESC,
                CASE
                    WHEN LOWER(s.stream_display_name) LIKE '%%4k%%' OR LOWER(s.stream_display_name) LIKE '%%2160p%%' THEN 1
                    WHEN LOWER(s.stream_display_name) LIKE '%%fhd%%' OR LOWER(s.stream_display_name) LIKE '%%1080p%%' THEN 2
                    WHEN LOWER(s.stream_display_name) LIKE '%%hd%%' OR LOWER(s.stream_display_name) LIKE '%%720p%%' THEN 3
                    WHEN LOWER(s.stream_display_name) LIKE '%%60fps%%' THEN 4
                    WHEN LOWER(s.stream_display_name) LIKE '%%extended%%' THEN 5
                    ELSE 6
                END,
                s.stream_display_name
            """

            result = self.execute_query(query, (tmdb_id,))

            if result:
                copies = []
                for row in result:
                    copies.append({
                        'stream_id': row['stream_id'],
                        'title': row['title'] or f"Stream {row['stream_id']}",
                        'movie_symlink': row['movie_symlink'],
                        'direct_source': row['direct_source'],
                        'direct_proxy': row['direct_proxy'],
                        'detected_quality': row['detected_quality'],
                        'movie_tmdb_id': row['movie_tmdb_id']
                    })
                return copies

            return []

        except Exception as e:
            print(f"Error getting movie copies by TMDB: {e}")
            return []

    def get_movie_copies_by_title(self, title):
        """Obtener todas las copias de una película por título con información detallada"""
        try:
            query = """
            SELECT
                s.id as stream_id,
                s.stream_display_name as title,
                s.movie_symlink,
                s.direct_source,
                s.direct_proxy,
                s.added,
                CASE
                    WHEN LOWER(s.stream_display_name) LIKE '%4k%' OR LOWER(s.stream_display_name) LIKE '%2160p%' THEN '4K'
                    WHEN LOWER(s.stream_display_name) LIKE '%60fps%' THEN '60FPS'
                    WHEN LOWER(s.stream_display_name) LIKE '%1080p%' OR LOWER(s.stream_display_name) LIKE '%fhd%' THEN 'FHD'
                    WHEN LOWER(s.stream_display_name) LIKE '%720p%' OR LOWER(s.stream_display_name) LIKE '%hd%' THEN 'HD'
                    WHEN LOWER(s.stream_display_name) LIKE '%480p%' OR LOWER(s.stream_display_name) LIKE '%sd%' THEN 'SD'
                    ELSE 'Unknown'
                END as detected_quality
            FROM streams s
            WHERE s.stream_display_name LIKE %s
            AND s.type = (SELECT type_id FROM streams_types WHERE type_name = 'Movies')
            ORDER BY s.movie_symlink DESC, s.id DESC
            """

            search_pattern = f"%{title}%"
            results = self.execute_query(query, (search_pattern,))

            if results:
                logging.info(f"Encontradas {len(results)} copias para película: {title}")
                return results
            else:
                logging.warning(f"No se encontraron copias para película: {title}")
                return []

        except Exception as e:
            logging.error(f"Error obteniendo copias de película por título '{title}': {e}")
            return []

    def analyze_episode_quality(self, episode_title: str) -> Dict[str, any]:
        """Analizar calidad de episodio basándose en el título"""
        title_lower = episode_title.lower()

        # Detectar resolución
        resolution = 'UNKNOWN'
        if any(x in title_lower for x in ['4k', '2160p', 'uhd']):
            resolution = '4K'
        elif any(x in title_lower for x in ['1080p', 'fhd', 'fullhd']):
            resolution = 'FHD'
        elif any(x in title_lower for x in ['720p', 'hd']):
            resolution = 'HD'
        elif any(x in title_lower for x in ['480p', 'sd']):
            resolution = 'SD'

        # Detectar FPS
        fps = 'STANDARD'
        if any(x in title_lower for x in ['60fps', '60p', '60 fps']):
            fps = '60FPS'
        elif any(x in title_lower for x in ['120fps', '120p']):
            fps = '120FPS'

        # Detectar versión especial
        version = 'STANDARD'
        if any(x in title_lower for x in ['extended', 'director', 'uncut']):
            version = 'EXTENDED'
        elif any(x in title_lower for x in ['remastered', 'restored']):
            version = 'REMASTERED'

        # Calcular score de calidad (mayor = mejor)
        quality_score = 0
        if resolution == '4K':
            quality_score += 100
        elif resolution == 'FHD':
            quality_score += 80
        elif resolution == 'HD':
            quality_score += 60
        elif resolution == 'SD':
            quality_score += 40

        if fps == '120FPS':
            quality_score += 30
        elif fps == '60FPS':
            quality_score += 20

        if version == 'REMASTERED':
            quality_score += 15
        elif version == 'EXTENDED':
            quality_score += 10

        return {
            'resolution': resolution,
            'fps': fps,
            'version': version,
            'quality_score': quality_score,
            'detected_quality': f"{resolution}_{fps}_{version}"
        }

    def get_episode_smart_recommendations(self, series_id: int, season_num: int, episode_num: int) -> Dict:
        """Obtener recomendaciones inteligentes para episodios duplicados"""
        try:
            # Obtener todas las copias del episodio
            copies = self.get_episode_copies_details(series_id, season_num, episode_num)

            if len(copies) <= 1:
                return {"message": "No hay duplicados para analizar", "copies": copies}

            # Analizar cada copia
            analyzed_copies = []
            for copy in copies:
                quality_analysis = self.analyze_episode_quality(copy['episode_title'])
                copy.update(quality_analysis)
                analyzed_copies.append(copy)

            # Separar por tipo de fuente
            symlinks = [c for c in analyzed_copies if c['movie_symlink'] == 1]
            direct_sources = [c for c in analyzed_copies if c['direct_source'] == 1 or c['direct_proxy'] == 1]
            others = [c for c in analyzed_copies if c['movie_symlink'] != 1 and c['direct_source'] != 1 and c['direct_proxy'] != 1]

            # Ordenar por calidad (mejor primero)
            symlinks.sort(key=lambda x: x['quality_score'], reverse=True)
            direct_sources.sort(key=lambda x: x['quality_score'], reverse=True)
            others.sort(key=lambda x: x['quality_score'], reverse=True)

            # Generar recomendaciones
            recommendations = []
            keep_ids = []
            delete_ids = []

            if symlinks:
                # Mantener el mejor symlink
                best_symlink = symlinks[0]
                keep_ids.append(best_symlink['stream_id'])
                recommendations.append(f"KEEP: Symlink {best_symlink['detected_quality']} (ID: {best_symlink['stream_id']})")

                # Marcar otros symlinks para revisión si son de calidad similar
                for symlink in symlinks[1:]:
                    if symlink['quality_score'] >= best_symlink['quality_score'] * 0.8:
                        recommendations.append(f"REVIEW: Symlink {symlink['detected_quality']} (ID: {symlink['stream_id']}) - Similar quality")
                    else:
                        delete_ids.append(symlink['stream_id'])
                        recommendations.append(f"DELETE: Symlink {symlink['detected_quality']} (ID: {symlink['stream_id']}) - Lower quality")

                # Marcar todos los direct sources para eliminación
                for direct in direct_sources:
                    delete_ids.append(direct['stream_id'])
                    recommendations.append(f"DELETE: Direct {direct['detected_quality']} (ID: {direct['stream_id']}) - Symlink available")
            else:
                # Sin symlinks, mantener el mejor direct source
                if direct_sources:
                    best_direct = direct_sources[0]
                    keep_ids.append(best_direct['stream_id'])
                    recommendations.append(f"KEEP: Direct {best_direct['detected_quality']} (ID: {best_direct['stream_id']}) - Best available")

                    for direct in direct_sources[1:]:
                        delete_ids.append(direct['stream_id'])
                        recommendations.append(f"DELETE: Direct {direct['detected_quality']} (ID: {direct['stream_id']}) - Duplicate")

            # Manejar otros tipos
            for other in others:
                delete_ids.append(other['stream_id'])
                recommendations.append(f"DELETE: Other {other['detected_quality']} (ID: {other['stream_id']}) - Unknown type")

            return {
                "total_copies": len(analyzed_copies),
                "symlinks": len(symlinks),
                "direct_sources": len(direct_sources),
                "others": len(others),
                "keep_ids": keep_ids,
                "delete_ids": delete_ids,
                "recommendations": recommendations,
                "analyzed_copies": analyzed_copies,
                "auto_action": "safe" if len(keep_ids) == 1 and len(delete_ids) > 0 else "manual_review"
            }

        except Exception as e:
            return {"error": f"Error analyzing episode: {e}", "copies": []}

    def find_problematic_episodes(self, limit: int = 5000) -> List[Dict]:
        """Buscar episodios con problemas de compatibilidad que pueden no mostrarse en apps"""
        try:
            query = """
            SELECT
                s.id as stream_id,
                s.stream_display_name as episode_title,
                s.series_no as series_id,
                ss.title as series_title,
                se.season_num,
                se.episode_num,
                s.target_container,
                s.read_native,
                CASE WHEN s.stream_icon IS NOT NULL THEN 'YES' ELSE 'NO' END as has_episode_icon,
                ss.similar,
                ss.episode_run_time,
                CASE
                    WHEN ss.similar LIKE '[%' THEN 'ARRAY_FORMAT'
                    WHEN ss.similar LIKE '{%' THEN 'OBJECT_FORMAT'
                    ELSE 'UNKNOWN_FORMAT'
                END as similar_format,
                -- Detectar problemas específicos
                CASE
                    WHEN s.target_container IS NULL OR s.target_container = '' THEN 1
                    ELSE 0
                END as missing_container,
                CASE
                    WHEN s.read_native = '1' THEN 1
                    ELSE 0
                END as bad_read_native,
                CASE
                    WHEN s.stream_icon IS NOT NULL THEN 1
                    ELSE 0
                END as has_problematic_icon,
                CASE
                    WHEN ss.similar LIKE '[%' THEN 1
                    ELSE 0
                END as bad_similar_format,
                CASE
                    WHEN ss.episode_run_time IS NOT NULL AND ss.episode_run_time != '0' THEN 1
                    ELSE 0
                END as non_zero_runtime
            FROM streams s
            LEFT JOIN streams_episodes se ON s.id = se.stream_id
            LEFT JOIN streams_series ss ON s.series_no = ss.id
            WHERE s.type = 5  -- Solo episodios
            AND s.series_no IS NOT NULL
            AND ss.id IS NOT NULL
            HAVING (missing_container + bad_read_native + has_problematic_icon + bad_similar_format + non_zero_runtime) > 0
            ORDER BY ss.title, se.season_num, se.episode_num
            LIMIT %s
            """

            results = self.execute_query(query, (limit,))

            if results:
                # Enriquecer resultados con análisis de problemas
                for result in results:
                    problems = []
                    if result['missing_container']:
                        problems.append("Missing target_container")
                    if result['bad_read_native']:
                        problems.append("read_native=1 (should be 0)")
                    if result['has_problematic_icon']:
                        problems.append("Episode has individual icon")
                    if result['bad_similar_format']:
                        problems.append("Similar field uses array format")
                    if result['non_zero_runtime']:
                        problems.append(f"episode_run_time={result['episode_run_time']} (should be 0)")

                    result['problems'] = problems
                    result['problem_count'] = len(problems)

                logging.info(f"Found {len(results)} problematic episodes")
                return results
            else:
                logging.info("No problematic episodes found")
                return []

        except Exception as e:
            logging.error(f"Error finding problematic episodes: {e}")
            return []

    def fix_episode_compatibility(self, stream_id: int) -> Dict:
        """Arreglar problemas de compatibilidad para un episodio específico"""
        try:
            fixes_applied = []

            # Obtener información del episodio
            episode_query = """
            SELECT s.id, s.series_no, s.stream_display_name, ss.title as series_title
            FROM streams s
            LEFT JOIN streams_series ss ON s.series_no = ss.id
            WHERE s.id = %s
            """
            episode_info = self.execute_query(episode_query, (stream_id,))

            if not episode_info:
                return {'success': False, 'error': 'Episode not found'}

            episode = episode_info[0]
            series_id = episode['series_no']

            # 1. Fix target_container
            fix_container_query = """
            UPDATE streams
            SET target_container = 'mp4'
            WHERE id = %s AND (target_container IS NULL OR target_container = '')
            """
            if self.execute_query(fix_container_query, (stream_id,), fetch=False):
                fixes_applied.append("Set target_container to 'mp4'")

            # 2. Fix read_native
            fix_read_native_query = """
            UPDATE streams
            SET read_native = '0'
            WHERE id = %s AND read_native = '1'
            """
            if self.execute_query(fix_read_native_query, (stream_id,), fetch=False):
                fixes_applied.append("Set read_native to '0'")

            # 3. Clear episode stream_icon
            fix_icon_query = """
            UPDATE streams
            SET stream_icon = NULL
            WHERE id = %s AND stream_icon IS NOT NULL
            """
            if self.execute_query(fix_icon_query, (stream_id,), fetch=False):
                fixes_applied.append("Cleared episode stream_icon")

            # 4. Fix series-level issues if needed
            if series_id:
                # Fix similar format
                get_similar_query = "SELECT similar FROM streams_series WHERE id = %s"
                similar_result = self.execute_query(get_similar_query, (series_id,))

                if similar_result and similar_result[0]['similar']:
                    similar_data = similar_result[0]['similar']
                    if similar_data.startswith('[') and similar_data.endswith(']'):
                        try:
                            import json
                            array_data = json.loads(similar_data)
                            object_data = {str(i): val for i, val in enumerate(array_data)}
                            object_json = json.dumps(object_data)

                            fix_similar_query = "UPDATE streams_series SET similar = %s WHERE id = %s"
                            if self.execute_query(fix_similar_query, (object_json, series_id), fetch=False):
                                fixes_applied.append("Fixed series similar format")
                        except:
                            pass

                # Fix episode_run_time
                fix_runtime_query = """
                UPDATE streams_series
                SET episode_run_time = '0'
                WHERE id = %s AND episode_run_time IS NOT NULL AND episode_run_time != '0'
                """
                if self.execute_query(fix_runtime_query, (series_id,), fetch=False):
                    fixes_applied.append("Set episode_run_time to '0'")

            return {
                'success': True,
                'stream_id': stream_id,
                'episode_title': episode['stream_display_name'],
                'series_title': episode['series_title'],
                'fixes_applied': fixes_applied,
                'fix_count': len(fixes_applied)
            }

        except Exception as e:
            logging.error(f"Error fixing episode {stream_id}: {e}")
            return {
                'success': False,
                'stream_id': stream_id,
                'error': str(e)
            }

    def fix_multiple_episodes_compatibility(self, stream_ids: List[int]) -> Dict:
        """Arreglar problemas de compatibilidad para múltiples episodios"""
        try:
            results = {
                'total_processed': len(stream_ids),
                'successful_fixes': 0,
                'failed_fixes': 0,
                'total_fixes_applied': 0,
                'details': [],
                'series_fixed': set()
            }

            for stream_id in stream_ids:
                fix_result = self.fix_episode_compatibility(stream_id)

                if fix_result['success']:
                    results['successful_fixes'] += 1
                    results['total_fixes_applied'] += fix_result['fix_count']
                    if 'series_title' in fix_result:
                        results['series_fixed'].add(fix_result['series_title'])
                else:
                    results['failed_fixes'] += 1

                results['details'].append(fix_result)

            results['series_fixed'] = list(results['series_fixed'])
            results['unique_series_count'] = len(results['series_fixed'])

            logging.info(f"Mass fix completed: {results['successful_fixes']}/{results['total_processed']} episodes fixed")
            return results

        except Exception as e:
            logging.error(f"Error in mass episode fix: {e}")
            return {
                'total_processed': len(stream_ids),
                'successful_fixes': 0,
                'failed_fixes': len(stream_ids),
                'error': str(e)
            }

    def detect_series_compatibility_issues(self) -> List[Dict]:
        """Detectar problemas de compatibilidad en series que pueden causar que no se muestren episodios"""
        try:
            query = """
            SELECT
                ss.id as series_id,
                ss.title as series_title,
                COUNT(se.id) as episode_count,
                GROUP_CONCAT(DISTINCT s.target_container) as containers,
                GROUP_CONCAT(DISTINCT s.read_native) as read_native_values,
                GROUP_CONCAT(DISTINCT CASE WHEN s.stream_icon IS NOT NULL THEN 'HAS_ICON' ELSE 'NO_ICON' END) as icon_status,
                ss.similar,
                CASE
                    WHEN ss.similar LIKE '[%' THEN 'ARRAY_FORMAT'
                    WHEN ss.similar LIKE '{%' THEN 'OBJECT_FORMAT'
                    ELSE 'UNKNOWN_FORMAT'
                END as similar_format,
                ss.episode_run_time
            FROM streams_series ss
            LEFT JOIN streams_episodes se ON ss.id = se.series_id
            LEFT JOIN streams s ON se.stream_id = s.id
            WHERE ss.id IS NOT NULL
            GROUP BY ss.id, ss.title, ss.similar, ss.episode_run_time
            HAVING episode_count > 0
            ORDER BY ss.title
            """

            results = self.execute_query(query)

            if results:
                issues = []
                for row in results:
                    problems = []

                    # Check target_container issues
                    containers = row.get('containers', '')
                    if 'None' in containers or not containers:
                        problems.append("Missing target_container (should be 'mp4')")

                    # Check read_native issues
                    read_native = row.get('read_native_values', '')
                    if '1' in read_native:
                        problems.append("read_native=1 (should be 0 for compatibility)")

                    # Check similar format issues
                    similar_format = row.get('similar_format', '')
                    if similar_format == 'ARRAY_FORMAT':
                        problems.append("similar field uses array format (should be object)")

                    # Check episode_run_time
                    runtime = row.get('episode_run_time')
                    if runtime and str(runtime) != '0':
                        problems.append(f"episode_run_time={runtime} (Lost uses 0)")

                    if problems:
                        issues.append({
                            'series_id': row['series_id'],
                            'series_title': row['series_title'],
                            'episode_count': row['episode_count'],
                            'problems': problems,
                            'containers': containers,
                            'read_native_values': read_native,
                            'similar_format': similar_format,
                            'episode_run_time': runtime
                        })

                logging.info(f"Compatibility analysis completed: {len(issues)} series with issues found")
                return issues
            else:
                logging.info("No series found for compatibility analysis")
                return []

        except Exception as e:
            logging.error(f"Error in series compatibility analysis: {e}")
            return []

    def fix_series_compatibility_issues(self, series_id: int) -> Dict:
        """Corregir problemas de compatibilidad para una serie específica"""
        try:
            fixes_applied = []

            # 1. Fix target_container
            fix_container_query = """
            UPDATE streams
            SET target_container = 'mp4'
            WHERE series_no = %s AND (target_container IS NULL OR target_container = '')
            """
            container_result = self.execute_query(fix_container_query, (series_id,), fetch=False)
            if container_result:
                fixes_applied.append("Set target_container to 'mp4'")

            # 2. Fix read_native
            fix_read_native_query = """
            UPDATE streams
            SET read_native = '0'
            WHERE series_no = %s AND read_native = '1'
            """
            read_native_result = self.execute_query(fix_read_native_query, (series_id,), fetch=False)
            if read_native_result:
                fixes_applied.append("Set read_native to '0'")

            # 3. Clear problematic stream_icons for episodes
            fix_icon_query = """
            UPDATE streams
            SET stream_icon = NULL
            WHERE series_no = %s AND type = 5 AND stream_icon IS NOT NULL
            """
            icon_result = self.execute_query(fix_icon_query, (series_id,), fetch=False)
            if icon_result:
                fixes_applied.append("Cleared episode stream_icons")

            # 4. Fix similar format (convert array to object if needed)
            get_similar_query = "SELECT similar FROM streams_series WHERE id = %s"
            similar_result = self.execute_query(get_similar_query, (series_id,))

            if similar_result and similar_result[0]['similar']:
                similar_data = similar_result[0]['similar']
                if similar_data.startswith('[') and similar_data.endswith(']'):
                    # Convert array format to object format
                    try:
                        import json
                        array_data = json.loads(similar_data)
                        object_data = {str(i): val for i, val in enumerate(array_data)}
                        object_json = json.dumps(object_data)

                        fix_similar_query = "UPDATE streams_series SET similar = %s WHERE id = %s"
                        self.execute_query(fix_similar_query, (object_json, series_id), fetch=False)
                        fixes_applied.append("Converted similar array to object format")
                    except:
                        fixes_applied.append("Failed to convert similar format")

            return {
                'series_id': series_id,
                'fixes_applied': fixes_applied,
                'success': len(fixes_applied) > 0
            }

        except Exception as e:
            logging.error(f"Error fixing compatibility issues for series {series_id}: {e}")
            return {
                'series_id': series_id,
                'fixes_applied': [],
                'success': False,
                'error': str(e)
            }
