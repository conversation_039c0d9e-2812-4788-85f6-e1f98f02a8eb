#!/usr/bin/env python3
"""
Script para explorar información de calidad en la base de datos
"""

import sys
import json
from database import DatabaseManager

def explore_quality_info():
    """Explorar información de calidad en streams"""
    print("=== Exploración de Información de Calidad ===\n")
    
    host = "**************"
    user = "infest84"
    password = "GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP"
    database = "xui"
    port = 3306
    
    db = DatabaseManager()
    
    if db.connect(host, user, password, database, port):
        print("✓ Conexión exitosa!")
        
        # 1. Verificar todas las columnas de streams
        print("\n1. Explorando todas las columnas de streams:")
        try:
            columns = db.execute_query("DESCRIBE streams")
            print("   Columnas disponibles:")
            for col in columns:
                print(f"     {col['Field']:20} | {col['Type']:15} | {col['Null']:5} | {col['Default']}")
                
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        # 2. Buscar películas con múltiples symlinks
        print("\n2. Buscando películas con múltiples symlinks:")
        try:
            multiple_symlinks = db.execute_query("""
            SELECT 
                s.tmdb_id,
                MAX(s.stream_display_name) as title,
                COUNT(*) as symlink_count,
                GROUP_CONCAT(s.id) as stream_ids,
                GROUP_CONCAT(s.stream_display_name) as titles
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies' 
            AND s.movie_symlink = 1
            AND s.tmdb_id IS NOT NULL 
            AND s.tmdb_id != 0
            GROUP BY s.tmdb_id
            HAVING COUNT(*) > 1
            ORDER BY symlink_count DESC
            LIMIT 10
            """)
            
            print(f"   Películas con múltiples symlinks: {len(multiple_symlinks)}")
            
            if multiple_symlinks:
                print("   Top películas con múltiples symlinks:")
                for movie in multiple_symlinks:
                    print(f"     TMDB {movie['tmdb_id']}: '{movie['title']}' - {movie['symlink_count']} symlinks")
                    titles = movie['titles'].split(',')
                    for i, title in enumerate(titles):
                        print(f"       {i+1}. {title}")
                    print()
                    
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        # 3. Analizar información de calidad en títulos
        print("\n3. Analizando información de calidad en títulos:")
        try:
            # Buscar patrones de calidad en los nombres
            quality_patterns = ['4k', '4K', 'UHD', 'HD', 'FHD', '1080p', '720p', '480p', '60fps', '60FPS']
            
            for pattern in quality_patterns:
                count_query = f"""
                SELECT COUNT(*) as count 
                FROM streams s
                JOIN streams_types st ON s.type = st.type_id
                WHERE st.type_name = 'Movies' 
                AND s.movie_symlink = 1
                AND s.stream_display_name LIKE '%{pattern}%'
                """
                
                result = db.execute_query(count_query)
                if result:
                    count = result[0]['count']
                    print(f"   Symlinks con '{pattern}': {count}")
                    
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        # 4. Ejemplos específicos de múltiples symlinks
        print("\n4. Ejemplos específicos de múltiples symlinks:")
        try:
            if multiple_symlinks:
                # Tomar el primer ejemplo
                example = multiple_symlinks[0]
                tmdb_id = example['tmdb_id']
                
                example_details = db.execute_query("""
                SELECT 
                    s.id,
                    s.stream_display_name,
                    s.stream_source,
                    s.movie_properties,
                    s.year,
                    s.rating
                FROM streams s
                JOIN streams_types st ON s.type = st.type_id
                WHERE st.type_name = 'Movies' 
                AND s.movie_symlink = 1
                AND s.tmdb_id = %s
                ORDER BY s.id
                """, (tmdb_id,))
                
                print(f"   Ejemplo detallado: TMDB {tmdb_id}")
                print("   ID       | Título                                    | Rating | Año")
                print("   " + "-" * 80)
                
                for detail in example_details:
                    title = detail['stream_display_name'][:40] if detail['stream_display_name'] else 'Sin título'
                    rating = detail['rating'] if detail['rating'] else 'N/A'
                    year = detail['year'] if detail['year'] else 'N/A'
                    print(f"   {detail['id']:8} | {title:40} | {rating:6} | {year}")
                    
                    # Analizar movie_properties si existe
                    if detail['movie_properties']:
                        try:
                            props = json.loads(detail['movie_properties'])
                            if 'runtime' in props:
                                print(f"            Runtime: {props['runtime']} min")
                            if 'vote_average' in props:
                                print(f"            TMDB Rating: {props['vote_average']}")
                        except:
                            pass
                    print()
                    
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        # 5. Buscar información adicional de calidad
        print("\n5. Buscando información adicional de calidad:")
        try:
            # Verificar si hay columnas relacionadas con calidad
            quality_columns = ['quality', 'resolution', 'bitrate', 'codec', 'container_extension']
            
            for col_info in columns:
                col_name = col_info['Field'].lower()
                if any(quality_term in col_name for quality_term in ['quality', 'resolution', 'bitrate', 'codec', 'container']):
                    print(f"   Columna relacionada con calidad: {col_info['Field']} ({col_info['Type']})")
                    
                    # Obtener algunos valores de ejemplo
                    sample_query = f"SELECT DISTINCT {col_info['Field']} FROM streams WHERE {col_info['Field']} IS NOT NULL LIMIT 10"
                    samples = db.execute_query(sample_query)
                    if samples:
                        values = [str(sample[col_info['Field']]) for sample in samples if sample[col_info['Field']]]
                        print(f"     Valores ejemplo: {', '.join(values[:5])}")
                    
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        # 6. Análisis de stream_source para información de calidad
        print("\n6. Analizando stream_source para información de calidad:")
        try:
            # Obtener algunos ejemplos de stream_source
            source_examples = db.execute_query("""
            SELECT 
                s.id,
                s.stream_display_name,
                s.stream_source
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies' 
            AND s.movie_symlink = 1
            AND s.stream_source IS NOT NULL
            LIMIT 5
            """)
            
            print("   Ejemplos de stream_source:")
            for example in source_examples:
                source = str(example['stream_source'])[:100] + "..." if len(str(example['stream_source'])) > 100 else str(example['stream_source'])
                print(f"     ID {example['id']}: {source}")
                
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        # 7. Recomendaciones para selección manual
        print("\n7. Recomendaciones para selección manual:")
        print("   📋 Criterios sugeridos para selección:")
        print("     1. Información en el título (4K, UHD, FHD, HD, 60fps)")
        print("     2. Rating/puntuación (mayor es mejor)")
        print("     3. Año (más reciente puede ser mejor)")
        print("     4. ID más bajo (más antiguo, más estable)")
        print("     5. Tamaño del archivo (si está disponible)")
        print("     6. Fuente del stream (local vs remoto)")
        
        print("\n   🎯 Estrategia recomendada:")
        print("     - Mostrar TODOS los symlinks de una película")
        print("     - Permitir selección múltiple manual")
        print("     - Destacar información de calidad en el título")
        print("     - Mostrar rating y año como criterios adicionales")
        print("     - Permitir vista previa antes de eliminar")
        
        db.disconnect()
        return True
        
    else:
        print("✗ Error de conexión")
        return False

def main():
    try:
        explore_quality_info()
        input("\nPresiona Enter para salir...")
    except Exception as e:
        print(f"\nError: {e}")

if __name__ == "__main__":
    main()
