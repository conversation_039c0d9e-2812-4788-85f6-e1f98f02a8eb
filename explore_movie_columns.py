#!/usr/bin/env python3
"""
Script para explorar las columnas movie_symlink, direct_source y direct_proxy
"""

import sys
from database import DatabaseManager

def explore_movie_columns():
    """Explorar las columnas de prioridad de películas"""
    print("=== Exploración de Columnas de Prioridad ===\n")
    
    host = "**************"
    user = "infest84"
    password = "GZM6hh12zHKgfg34FghtAQ84WxloPRX]SV$fP"
    database = "xui"
    port = 3306
    
    db = DatabaseManager()
    
    if db.connect(host, user, password, database, port):
        print("✓ Conexión exitosa!")
        
        # 1. Verificar que las columnas existen
        print("\n1. Verificando estructura de tabla streams:")
        try:
            columns = db.execute_query("DESCRIBE streams")
            priority_columns = ['movie_symlink', 'direct_source', 'direct_proxy']
            
            print("   Columnas relacionadas con prioridad:")
            for col in columns:
                if col['Field'] in priority_columns:
                    print(f"     ✓ {col['Field']} ({col['Type']}) - {col['Null']} - Default: {col['Default']}")
            
            # Verificar si existen todas las columnas
            existing_cols = [col['Field'] for col in columns]
            missing_cols = [col for col in priority_columns if col not in existing_cols]
            if missing_cols:
                print(f"   ⚠️ Columnas faltantes: {missing_cols}")
            else:
                print("   ✓ Todas las columnas de prioridad existen")
                
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        # 2. Analizar valores en las columnas de prioridad
        print("\n2. Analizando valores en columnas de prioridad:")
        try:
            priority_stats = db.execute_query("""
            SELECT 
                movie_symlink,
                direct_source,
                direct_proxy,
                COUNT(*) as count
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies'
            GROUP BY movie_symlink, direct_source, direct_proxy
            ORDER BY count DESC
            LIMIT 10
            """)
            
            print("   Combinaciones más comunes (Top 10):")
            print("   movie_symlink | direct_source | direct_proxy | count")
            print("   " + "-" * 55)
            for stat in priority_stats:
                symlink = str(stat['movie_symlink']) if stat['movie_symlink'] is not None else 'NULL'
                direct_src = str(stat['direct_source']) if stat['direct_source'] is not None else 'NULL'
                direct_prx = str(stat['direct_proxy']) if stat['direct_proxy'] is not None else 'NULL'
                count = stat['count']
                print(f"   {symlink:13} | {direct_src:13} | {direct_prx:12} | {count}")
                
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        # 3. Analizar duplicados con diferentes prioridades
        print("\n3. Analizando duplicados con diferentes configuraciones:")
        try:
            # Buscar duplicados por TMDB que tengan diferentes configuraciones
            duplicate_analysis = db.execute_query("""
            SELECT 
                s.tmdb_id,
                s.stream_display_name,
                COUNT(*) as total_copies,
                COUNT(DISTINCT CONCAT(IFNULL(s.movie_symlink, 'NULL'), '-', 
                                    IFNULL(s.direct_source, 'NULL'), '-', 
                                    IFNULL(s.direct_proxy, 'NULL'))) as different_configs,
                GROUP_CONCAT(DISTINCT CONCAT(s.id, ':', 
                                           IFNULL(s.movie_symlink, 'NULL'), ':', 
                                           IFNULL(s.direct_source, 'NULL'), ':', 
                                           IFNULL(s.direct_proxy, 'NULL'))) as configs
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies' 
            AND s.tmdb_id IS NOT NULL 
            AND s.tmdb_id != 0
            GROUP BY s.tmdb_id, s.stream_display_name
            HAVING COUNT(*) > 1 AND different_configs > 1
            ORDER BY total_copies DESC, different_configs DESC
            LIMIT 5
            """)
            
            print(f"   Duplicados con diferentes configuraciones: {len(duplicate_analysis)}")
            
            for dup in duplicate_analysis:
                print(f"\n   TMDB {dup['tmdb_id']}: '{dup['stream_display_name']}'")
                print(f"     Total copias: {dup['total_copies']}")
                print(f"     Configuraciones diferentes: {dup['different_configs']}")
                
                # Mostrar cada configuración
                configs = dup['configs'].split(',')
                print("     Configuraciones:")
                for config in configs[:5]:  # Mostrar solo las primeras 5
                    parts = config.split(':')
                    if len(parts) >= 4:
                        stream_id, symlink, direct_src, direct_prx = parts[0], parts[1], parts[2], parts[3]
                        print(f"       ID {stream_id}: symlink={symlink}, direct_source={direct_src}, direct_proxy={direct_prx}")
                
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        # 4. Estadísticas por tipo de configuración
        print("\n4. Estadísticas por configuración:")
        try:
            config_stats = db.execute_query("""
            SELECT 
                CASE 
                    WHEN movie_symlink = 1 THEN 'Symlink'
                    WHEN direct_source = 1 THEN 'Direct Source'
                    WHEN direct_proxy = 1 THEN 'Direct Proxy'
                    ELSE 'Otro'
                END as config_type,
                COUNT(*) as count,
                ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM streams s2 
                                         JOIN streams_types st2 ON s2.type = st2.type_id 
                                         WHERE st2.type_name = 'Movies'), 2) as percentage
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies'
            GROUP BY config_type
            ORDER BY count DESC
            """)
            
            print("   Distribución por tipo de configuración:")
            for stat in config_stats:
                print(f"     {stat['config_type']}: {stat['count']} películas ({stat['percentage']}%)")
                
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        # 5. Ejemplos específicos para testing
        print("\n5. Ejemplos específicos para testing:")
        try:
            # Buscar "La Lista de Schindler" que sabemos que tiene 8 copias
            schindler_copies = db.execute_query("""
            SELECT 
                s.id,
                s.stream_display_name,
                s.movie_symlink,
                s.direct_source,
                s.direct_proxy,
                s.stream_source
            FROM streams s
            JOIN streams_types st ON s.type = st.type_id
            WHERE st.type_name = 'Movies' 
            AND s.tmdb_id = 424
            ORDER BY s.id
            """)
            
            print(f"   Ejemplo: 'La Lista de Schindler' ({len(schindler_copies)} copias):")
            print("   ID       | symlink | direct_src | direct_prx | source")
            print("   " + "-" * 60)
            for copy in schindler_copies:
                symlink = copy['movie_symlink'] if copy['movie_symlink'] is not None else 'NULL'
                direct_src = copy['direct_source'] if copy['direct_source'] is not None else 'NULL'
                direct_prx = copy['direct_proxy'] if copy['direct_proxy'] is not None else 'NULL'
                source = str(copy['stream_source'])[:20] + "..." if copy['stream_source'] and len(str(copy['stream_source'])) > 20 else str(copy['stream_source'])
                print(f"   {copy['id']:8} | {symlink:7} | {direct_src:10} | {direct_prx:10} | {source}")
                
        except Exception as e:
            print(f"   ✗ Error: {e}")
        
        db.disconnect()
        return True
        
    else:
        print("✗ Error de conexión")
        return False

def main():
    try:
        explore_movie_columns()
        input("\nPresiona Enter para salir...")
    except Exception as e:
        print(f"\nError: {e}")

if __name__ == "__main__":
    main()
