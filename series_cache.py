#!/usr/bin/env python3
"""
Sistema de caché para series de la base de datos
Optimiza búsquedas y análisis M3U vs Database
"""

import json
import os
import time
import hashlib
from typing import Dict, List, Optional, Set
from datetime import datetime

class SeriesCache:
    def __init__(self, cache_file: str = "series_cache.json"):
        self.cache_file = cache_file
        self.series_cache = {}
        self.episodes_cache = {}
        self.search_index = {}
        self.last_update = None
        self.cache_version = "1.0"
        self.is_dirty = False
        
        # Estadísticas de rendimiento
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'searches_performed': 0,
            'last_build_time': 0,
            'total_series': 0,
            'total_episodes': 0
        }
        
        self.load_cache()
    
    def _generate_search_key(self, title: str) -> str:
        """Generar clave de búsqueda normalizada"""
        # Normalizar título para búsqueda
        normalized = title.lower().strip()
        # Remover caracteres especiales comunes
        normalized = normalized.replace('(', '').replace(')', '')
        normalized = normalized.replace('[', '').replace(']', '')
        normalized = normalized.replace(':', '').replace('-', ' ')
        # Remover espacios múltiples
        normalized = ' '.join(normalized.split())
        return normalized
    
    def _generate_cache_hash(self) -> str:
        """Generar hash del contenido del caché para verificar integridad"""
        content = json.dumps(self.series_cache, sort_keys=True)
        return hashlib.md5(content.encode()).hexdigest()
    
    def build_cache(self, db_manager) -> bool:
        """Construir caché completo desde la base de datos"""
        try:
            start_time = time.time()
            print("🔄 Building series cache from database...")
            
            # Limpiar caché existente
            self.series_cache.clear()
            self.episodes_cache.clear()
            self.search_index.clear()
            
            # Obtener todas las series
            print("📊 Loading series from database...")
            db_series = db_manager.get_series_with_episodes()
            
            if not db_series:
                print("⚠️ No series found in database")
                return False
            
            print(f"📺 Processing {len(db_series)} series...")
            
            # Procesar cada serie
            for i, series in enumerate(db_series):
                if i % 50 == 0:
                    print(f"   📺 Processing series {i+1}/{len(db_series)}...")
                
                series_id = series['series_id']
                series_title = series['series_title']
                
                # Almacenar serie en caché
                self.series_cache[series_id] = {
                    'series_id': series_id,
                    'series_title': series_title,
                    'normalized_title': self._generate_search_key(series_title),
                    'episode_count': 0,
                    'seasons': set(),
                    'last_updated': datetime.now().isoformat()
                }
                
                # Obtener episodios de la serie
                episodes = db_manager.get_series_episodes_detailed(series_id)
                episode_list = []
                
                for episode in episodes:
                    episode_data = {
                        'stream_id': episode.get('stream_id'),
                        'series_id': series_id,
                        'season_num': episode.get('season_num'),
                        'episode_num': episode.get('episode_num'),
                        'stream_display_name': episode.get('stream_display_name', ''),
                        'episode_info': f"S{episode.get('season_num', 0):02d}E{episode.get('episode_num', 0):02d}"
                    }
                    episode_list.append(episode_data)
                    
                    # Agregar temporada al conjunto
                    if episode.get('season_num'):
                        self.series_cache[series_id]['seasons'].add(episode.get('season_num'))
                
                # Almacenar episodios
                self.episodes_cache[series_id] = episode_list
                self.series_cache[series_id]['episode_count'] = len(episode_list)
                
                # Convertir set a lista para serialización
                self.series_cache[series_id]['seasons'] = list(self.series_cache[series_id]['seasons'])
                
                # Crear índices de búsqueda
                search_key = self._generate_search_key(series_title)
                if search_key not in self.search_index:
                    self.search_index[search_key] = []
                self.search_index[search_key].append(series_id)
                
                # Índices adicionales (palabras clave)
                words = search_key.split()
                for word in words:
                    if len(word) > 2:  # Solo palabras de 3+ caracteres
                        if word not in self.search_index:
                            self.search_index[word] = []
                        if series_id not in self.search_index[word]:
                            self.search_index[word].append(series_id)
            
            # Actualizar estadísticas
            build_time = time.time() - start_time
            self.stats['last_build_time'] = build_time
            self.stats['total_series'] = len(self.series_cache)
            self.stats['total_episodes'] = sum(len(eps) for eps in self.episodes_cache.values())
            self.last_update = datetime.now().isoformat()
            self.is_dirty = True
            
            print(f"✅ Cache built successfully!")
            print(f"   📺 Series: {self.stats['total_series']}")
            print(f"   📺 Episodes: {self.stats['total_episodes']}")
            print(f"   ⏱️ Build time: {build_time:.2f}s")
            print(f"   🔍 Search index entries: {len(self.search_index)}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error building cache: {e}")
            return False
    
    def find_series_by_title(self, title: str) -> List[Dict]:
        """Buscar series por título usando el caché"""
        self.stats['searches_performed'] += 1
        search_key = self._generate_search_key(title)
        
        # Búsqueda exacta primero
        if search_key in self.search_index:
            self.stats['cache_hits'] += 1
            series_ids = self.search_index[search_key]
            return [self.series_cache[sid] for sid in series_ids if sid in self.series_cache]
        
        # Búsqueda por palabras clave
        words = search_key.split()
        candidate_series = set()
        
        for word in words:
            if word in self.search_index:
                candidate_series.update(self.search_index[word])
        
        if candidate_series:
            self.stats['cache_hits'] += 1
            results = []
            for series_id in candidate_series:
                if series_id in self.series_cache:
                    series = self.series_cache[series_id]
                    # Verificar que el título contenga la mayoría de las palabras
                    series_words = set(series['normalized_title'].split())
                    word_matches = len(set(words) & series_words)
                    if word_matches >= len(words) * 0.6:  # 60% de coincidencia
                        results.append(series)
            return results
        
        self.stats['cache_misses'] += 1
        return []
    
    def get_series_episodes(self, series_id: int) -> List[Dict]:
        """Obtener episodios de una serie desde el caché"""
        if series_id in self.episodes_cache:
            self.stats['cache_hits'] += 1
            return self.episodes_cache[series_id]
        
        self.stats['cache_misses'] += 1
        return []
    
    def add_series(self, series_data: Dict, episodes_data: List[Dict] = None):
        """Agregar nueva serie al caché"""
        series_id = series_data['series_id']
        series_title = series_data['series_title']
        
        # Agregar serie
        self.series_cache[series_id] = {
            'series_id': series_id,
            'series_title': series_title,
            'normalized_title': self._generate_search_key(series_title),
            'episode_count': len(episodes_data) if episodes_data else 0,
            'seasons': [],
            'last_updated': datetime.now().isoformat()
        }
        
        # Agregar episodios si se proporcionan
        if episodes_data:
            self.episodes_cache[series_id] = episodes_data
            seasons = set()
            for episode in episodes_data:
                if episode.get('season_num'):
                    seasons.add(episode.get('season_num'))
            self.series_cache[series_id]['seasons'] = list(seasons)
        
        # Actualizar índice de búsqueda
        search_key = self._generate_search_key(series_title)
        if search_key not in self.search_index:
            self.search_index[search_key] = []
        if series_id not in self.search_index[search_key]:
            self.search_index[search_key].append(series_id)
        
        # Índices por palabras
        words = search_key.split()
        for word in words:
            if len(word) > 2:
                if word not in self.search_index:
                    self.search_index[word] = []
                if series_id not in self.search_index[word]:
                    self.search_index[word].append(series_id)
        
        self.is_dirty = True
        self.stats['total_series'] = len(self.series_cache)
        print(f"✅ Added series to cache: {series_title}")
    
    def update_series(self, series_id: int, series_data: Dict, episodes_data: List[Dict] = None):
        """Actualizar serie existente en el caché"""
        if series_id in self.series_cache:
            old_title = self.series_cache[series_id]['series_title']
            new_title = series_data['series_title']
            
            # Actualizar datos de la serie
            self.series_cache[series_id].update({
                'series_title': new_title,
                'normalized_title': self._generate_search_key(new_title),
                'last_updated': datetime.now().isoformat()
            })
            
            # Actualizar episodios si se proporcionan
            if episodes_data:
                self.episodes_cache[series_id] = episodes_data
                seasons = set()
                for episode in episodes_data:
                    if episode.get('season_num'):
                        seasons.add(episode.get('season_num'))
                self.series_cache[series_id]['seasons'] = list(seasons)
                self.series_cache[series_id]['episode_count'] = len(episodes_data)
            
            # Actualizar índice si el título cambió
            if old_title != new_title:
                self._rebuild_search_index_for_series(series_id, new_title)
            
            self.is_dirty = True
            print(f"✅ Updated series in cache: {new_title}")
    
    def _rebuild_search_index_for_series(self, series_id: int, new_title: str):
        """Reconstruir índice de búsqueda para una serie específica"""
        # Remover de índices antiguos
        for key, series_list in self.search_index.items():
            if series_id in series_list:
                series_list.remove(series_id)
        
        # Agregar a nuevos índices
        search_key = self._generate_search_key(new_title)
        if search_key not in self.search_index:
            self.search_index[search_key] = []
        self.search_index[search_key].append(series_id)
        
        words = search_key.split()
        for word in words:
            if len(word) > 2:
                if word not in self.search_index:
                    self.search_index[word] = []
                if series_id not in self.search_index[word]:
                    self.search_index[word].append(series_id)
    
    def save_cache(self) -> bool:
        """Guardar caché en archivo"""
        if not self.is_dirty:
            return True
        
        try:
            cache_data = {
                'version': self.cache_version,
                'last_update': self.last_update,
                'cache_hash': self._generate_cache_hash(),
                'stats': self.stats,
                'series_cache': self.series_cache,
                'episodes_cache': self.episodes_cache,
                'search_index': self.search_index
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
            
            self.is_dirty = False
            print(f"💾 Cache saved to {self.cache_file}")
            return True
            
        except Exception as e:
            print(f"❌ Error saving cache: {e}")
            return False
    
    def load_cache(self) -> bool:
        """Cargar caché desde archivo"""
        if not os.path.exists(self.cache_file):
            print(f"📁 Cache file {self.cache_file} not found, will create new cache")
            return False
        
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # Verificar versión
            if cache_data.get('version') != self.cache_version:
                print(f"⚠️ Cache version mismatch, rebuilding cache")
                return False
            
            # Cargar datos
            self.last_update = cache_data.get('last_update')
            self.stats = cache_data.get('stats', self.stats)
            self.series_cache = cache_data.get('series_cache', {})
            self.episodes_cache = cache_data.get('episodes_cache', {})
            self.search_index = cache_data.get('search_index', {})
            
            # Convertir claves de series_cache a int
            self.series_cache = {int(k): v for k, v in self.series_cache.items()}
            self.episodes_cache = {int(k): v for k, v in self.episodes_cache.items()}
            
            print(f"✅ Cache loaded from {self.cache_file}")
            print(f"   📺 Series: {len(self.series_cache)}")
            print(f"   📺 Episodes: {sum(len(eps) for eps in self.episodes_cache.values())}")
            print(f"   🔍 Search entries: {len(self.search_index)}")
            print(f"   📅 Last update: {self.last_update}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading cache: {e}")
            return False
    
    def get_all_series(self) -> List[Dict]:
        """Obtener todas las series del caché"""
        return list(self.series_cache.values())

    def get_stats(self) -> Dict:
        """Obtener estadísticas del caché"""
        hit_rate = 0
        if self.stats['cache_hits'] + self.stats['cache_misses'] > 0:
            hit_rate = (self.stats['cache_hits'] / (self.stats['cache_hits'] + self.stats['cache_misses'])) * 100

        return {
            **self.stats,
            'cache_hit_rate': hit_rate,
            'cache_size_mb': self._get_cache_size_mb(),
            'is_loaded': len(self.series_cache) > 0
        }
    
    def _get_cache_size_mb(self) -> float:
        """Calcular tamaño del caché en MB"""
        try:
            if os.path.exists(self.cache_file):
                size_bytes = os.path.getsize(self.cache_file)
                return size_bytes / (1024 * 1024)
        except:
            pass
        return 0.0
    
    def clear_cache(self):
        """Limpiar caché completamente"""
        self.series_cache.clear()
        self.episodes_cache.clear()
        self.search_index.clear()
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'searches_performed': 0,
            'last_build_time': 0,
            'total_series': 0,
            'total_episodes': 0
        }
        self.is_dirty = True
        print("🗑️ Cache cleared")
