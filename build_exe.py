#!/usr/bin/env python3
"""
Script para generar el ejecutable de XUI Database Manager
"""

import os
import sys
import subprocess
import shutil

def install_dependencies():
    """Instalar dependencias necesarias"""
    print("Instalando dependencias...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Dependencias instaladas correctamente")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error instalando dependencias: {e}")
        return False

def build_executable():
    """Generar el ejecutable usando PyInstaller"""
    print("Generando ejecutable...")

    # Verificar si existe el archivo .spec
    spec_file = "xui_manager.spec"

    if os.path.exists(spec_file):
        print(f"Usando archivo de configuración: {spec_file}")
        cmd = ["pyinstaller", spec_file]
    else:
        print("Usando configuración por defecto")
        # Comando PyInstaller por defecto
        cmd = [
            "pyinstaller",
            "--onefile",                    # Un solo archivo ejecutable
            "--windowed",                   # Sin ventana de consola (para GUI)
            "--name=XUI_Database_Manager",  # Nombre del ejecutable
            "--add-data=requirements.txt;.", # Incluir archivos adicionales
            "--hidden-import=pymysql",      # Importaciones ocultas
            "--hidden-import=tkinter",
            "--hidden-import=tkinter.ttk",
            "--hidden-import=tkinter.messagebox",
            "--hidden-import=tkinter.simpledialog",
            "main.py"
        ]

        # Agregar icono si existe
        if os.path.exists("icon.ico"):
            cmd.extend(["--icon=icon.ico"])

    try:
        subprocess.check_call(cmd)
        print("✓ Ejecutable generado correctamente")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error generando ejecutable: {e}")
        return False
    except FileNotFoundError:
        print("✗ PyInstaller no encontrado. Instalando...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            subprocess.check_call(cmd)
            print("✓ Ejecutable generado correctamente")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ Error: {e}")
            return False

def create_icon():
    """Crear un icono simple para la aplicación"""
    # Crear un archivo de icono básico si no existe
    if not os.path.exists("icon.ico"):
        print("Icono no encontrado. Se usará el icono por defecto de PyInstaller.")
        # Aquí podrías crear un icono simple o usar uno por defecto

def cleanup():
    """Limpiar archivos temporales"""
    print("Limpiando archivos temporales...")
    
    # Eliminar directorios temporales de PyInstaller
    dirs_to_remove = ["build", "__pycache__"]
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ Eliminado directorio: {dir_name}")
    
    # Eliminar archivos .pyc
    for root, _, files in os.walk("."):
        for file in files:
            if file.endswith(".pyc"):
                os.remove(os.path.join(root, file))

def main():
    """Función principal"""
    print("=== XUI Database Manager - Generador de Ejecutable ===\n")
    
    # Verificar que estamos en el directorio correcto
    required_files = ["main.py", "gui.py", "database.py", "requirements.txt"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"✗ Archivos faltantes: {', '.join(missing_files)}")
        print("Asegúrate de estar en el directorio correcto del proyecto.")
        return False
    
    # Instalar dependencias
    if not install_dependencies():
        return False
    
    # Crear icono
    create_icon()
    
    # Generar ejecutable
    if not build_executable():
        return False
    
    # Limpiar archivos temporales
    cleanup()
    
    print("\n=== Proceso completado ===")
    print("El ejecutable se encuentra en: dist/XUI_Database_Manager.exe")
    print("\nInstrucciones de uso:")
    print("1. Copia el archivo XUI_Database_Manager.exe donde desees")
    print("2. Ejecuta el archivo para abrir la aplicación")
    print("3. Ingresa los datos de conexión a tu base de datos MariaDB")
    print("4. Usa las pestañas para gestionar series, episodios y películas")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
