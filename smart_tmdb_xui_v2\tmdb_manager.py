#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Smart TMDB Manager v2
Gestor avanzado de integración con TMDB API
"""

import requests
import json
import time
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime, timedelta
import threading
from urllib.parse import quote

class SmartTMDBManager:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.themoviedb.org/3"
        self.image_base_url = "https://image.tmdb.org/t/p"
        self.session = requests.Session()
        self.rate_limit_delay = 0.25  # 4 requests per second
        self.last_request_time = 0
        self._lock = threading.Lock()
        
        # Configurar logging
        self.logger = logging.getLogger(__name__)
        
        # Headers para todas las requests
        self.session.headers.update({
            'User-Agent': 'Smart-TMDB-XUI-v2/1.0',
            'Accept': 'application/json'
        })
    
    def _rate_limit(self):
        """Aplicar rate limiting"""
        with self._lock:
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            
            if time_since_last < self.rate_limit_delay:
                sleep_time = self.rate_limit_delay - time_since_last
                time.sleep(sleep_time)
            
            self.last_request_time = time.time()
    
    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """Hacer request con rate limiting y manejo de errores"""
        self._rate_limit()
        
        if not params:
            params = {}
        
        params['api_key'] = self.api_key
        
        try:
            url = f"{self.base_url}/{endpoint}"
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:
                # Rate limit exceeded
                retry_after = int(response.headers.get('Retry-After', 1))
                self.logger.warning(f"Rate limit exceeded, waiting {retry_after}s")
                time.sleep(retry_after)
                return self._make_request(endpoint, params)
            else:
                self.logger.error(f"TMDB API error {response.status_code}: {response.text}")
                return None
                
        except requests.RequestException as e:
            self.logger.error(f"Request error: {e}")
            return None
    
    def search_movie(self, title: str, year: Optional[int] = None) -> List[Dict]:
        """Buscar película por título"""
        params = {
            'query': title,
            'language': 'es-ES',
            'include_adult': False
        }
        
        if year:
            params['year'] = year
        
        result = self._make_request('search/movie', params)
        if result and 'results' in result:
            return result['results']
        return []
    
    def search_tv(self, title: str, year: Optional[int] = None) -> List[Dict]:
        """Buscar serie de TV por título"""
        params = {
            'query': title,
            'language': 'es-ES',
            'include_adult': False
        }
        
        if year:
            params['first_air_date_year'] = year
        
        result = self._make_request('search/tv', params)
        if result and 'results' in result:
            return result['results']
        return []
    
    def get_movie_details(self, movie_id: int) -> Optional[Dict]:
        """Obtener detalles completos de una película"""
        endpoint = f'movie/{movie_id}'
        params = {
            'language': 'es-ES',
            'append_to_response': 'credits,images,videos'
        }
        
        return self._make_request(endpoint, params)
    
    def get_tv_details(self, tv_id: int) -> Optional[Dict]:
        """Obtener detalles completos de una serie"""
        endpoint = f'tv/{tv_id}'
        params = {
            'language': 'es-ES',
            'append_to_response': 'credits,images,videos'
        }
        
        return self._make_request(endpoint, params)
    
    def get_tv_season(self, tv_id: int, season_number: int) -> Optional[Dict]:
        """Obtener información de una temporada específica"""
        endpoint = f'tv/{tv_id}/season/{season_number}'
        params = {'language': 'es-ES'}
        
        return self._make_request(endpoint, params)
    
    def get_image_url(self, image_path: str, size: str = 'w500') -> str:
        """Construir URL completa para imagen"""
        if not image_path:
            return ""
        
        if not image_path.startswith('/'):
            image_path = '/' + image_path
            
        return f"{self.image_base_url}/{size}{image_path}"
    
    def smart_movie_search(self, title: str, year: Optional[int] = None) -> Dict[str, Any]:
        """Búsqueda inteligente de película con scoring"""
        results = self.search_movie(title, year)
        
        if not results:
            return {'found': False, 'candidates': []}
        
        # Scoring inteligente
        scored_results = []
        title_lower = title.lower().strip()
        
        for movie in results:
            score = 0
            movie_title = movie.get('title', '').lower().strip()
            movie_original_title = movie.get('original_title', '').lower().strip()
            
            # Coincidencia exacta del título
            if movie_title == title_lower or movie_original_title == title_lower:
                score += 100
            elif title_lower in movie_title or movie_title in title_lower:
                score += 80
            elif title_lower in movie_original_title or movie_original_title in title_lower:
                score += 70
            
            # Coincidencia de año
            if year and movie.get('release_date'):
                movie_year = int(movie['release_date'][:4]) if movie['release_date'] else 0
                if movie_year == year:
                    score += 50
                elif abs(movie_year - year) <= 1:
                    score += 25
            
            # Popularidad
            popularity = movie.get('popularity', 0)
            if popularity > 50:
                score += 20
            elif popularity > 10:
                score += 10
            
            # Rating
            vote_average = movie.get('vote_average', 0)
            if vote_average > 7:
                score += 15
            elif vote_average > 5:
                score += 5
            
            movie['smart_score'] = score
            scored_results.append(movie)
        
        # Ordenar por score
        scored_results.sort(key=lambda x: x['smart_score'], reverse=True)
        
        best_match = scored_results[0] if scored_results else None
        confidence = 'high' if best_match and best_match['smart_score'] > 80 else 'medium' if best_match and best_match['smart_score'] > 50 else 'low'
        
        return {
            'found': bool(best_match),
            'best_match': best_match,
            'confidence': confidence,
            'candidates': scored_results[:5],  # Top 5 candidates
            'total_results': len(results)
        }
    
    def smart_tv_search(self, title: str, year: Optional[int] = None) -> Dict[str, Any]:
        """Búsqueda inteligente de serie con scoring"""
        results = self.search_tv(title, year)
        
        if not results:
            return {'found': False, 'candidates': []}
        
        # Scoring similar al de películas
        scored_results = []
        title_lower = title.lower().strip()
        
        for tv in results:
            score = 0
            tv_name = tv.get('name', '').lower().strip()
            tv_original_name = tv.get('original_name', '').lower().strip()
            
            # Coincidencia exacta del título
            if tv_name == title_lower or tv_original_name == title_lower:
                score += 100
            elif title_lower in tv_name or tv_name in title_lower:
                score += 80
            elif title_lower in tv_original_name or tv_original_name in title_lower:
                score += 70
            
            # Coincidencia de año
            if year and tv.get('first_air_date'):
                tv_year = int(tv['first_air_date'][:4]) if tv['first_air_date'] else 0
                if tv_year == year:
                    score += 50
                elif abs(tv_year - year) <= 1:
                    score += 25
            
            # Popularidad
            popularity = tv.get('popularity', 0)
            if popularity > 50:
                score += 20
            elif popularity > 10:
                score += 10
            
            # Rating
            vote_average = tv.get('vote_average', 0)
            if vote_average > 7:
                score += 15
            elif vote_average > 5:
                score += 5
            
            tv['smart_score'] = score
            scored_results.append(tv)
        
        # Ordenar por score
        scored_results.sort(key=lambda x: x['smart_score'], reverse=True)
        
        best_match = scored_results[0] if scored_results else None
        confidence = 'high' if best_match and best_match['smart_score'] > 80 else 'medium' if best_match and best_match['smart_score'] > 50 else 'low'
        
        return {
            'found': bool(best_match),
            'best_match': best_match,
            'confidence': confidence,
            'candidates': scored_results[:5],
            'total_results': len(results)
        }
    
    def create_movie_properties_json(self, movie_data: Dict) -> str:
        """Crear JSON optimizado para movie_properties"""
        try:
            # Extraer información clave
            properties = {
                'tmdb_id': movie_data.get('id'),
                'imdb_id': movie_data.get('imdb_id'),
                'title': movie_data.get('title'),
                'original_title': movie_data.get('original_title'),
                'overview': movie_data.get('overview', '')[:500],  # Limitar descripción
                'release_date': movie_data.get('release_date'),
                'runtime': movie_data.get('runtime'),
                'vote_average': movie_data.get('vote_average'),
                'vote_count': movie_data.get('vote_count'),
                'popularity': movie_data.get('popularity'),
                'adult': movie_data.get('adult', False),
                'genre_ids': movie_data.get('genre_ids', []),
                'original_language': movie_data.get('original_language')
            }
            
            # Agregar URLs de imágenes
            if movie_data.get('poster_path'):
                properties['movie_poster'] = self.get_image_url(movie_data['poster_path'], 'w500')
                properties['movie_image'] = self.get_image_url(movie_data['poster_path'], 'w500')
                properties['poster'] = self.get_image_url(movie_data['poster_path'], 'w500')
            
            if movie_data.get('backdrop_path'):
                properties['backdrop'] = self.get_image_url(movie_data['backdrop_path'], 'w1280')
                properties['movie_backdrop'] = self.get_image_url(movie_data['backdrop_path'], 'w1280')
            
            # Limpiar valores None
            properties = {k: v for k, v in properties.items() if v is not None}
            
            return json.dumps(properties, ensure_ascii=False)
            
        except Exception as e:
            self.logger.error(f"Error creating movie properties JSON: {e}")
            return "{}"
    
    def create_series_properties_json(self, tv_data: Dict) -> str:
        """Crear JSON optimizado para series"""
        try:
            properties = {
                'tmdb_id': tv_data.get('id'),
                'name': tv_data.get('name'),
                'original_name': tv_data.get('original_name'),
                'overview': tv_data.get('overview', '')[:500],
                'first_air_date': tv_data.get('first_air_date'),
                'last_air_date': tv_data.get('last_air_date'),
                'number_of_episodes': tv_data.get('number_of_episodes'),
                'number_of_seasons': tv_data.get('number_of_seasons'),
                'vote_average': tv_data.get('vote_average'),
                'vote_count': tv_data.get('vote_count'),
                'popularity': tv_data.get('popularity'),
                'genre_ids': tv_data.get('genre_ids', []),
                'original_language': tv_data.get('original_language'),
                'status': tv_data.get('status')
            }
            
            # Agregar URLs de imágenes
            if tv_data.get('poster_path'):
                properties['poster'] = self.get_image_url(tv_data['poster_path'], 'w500')
                properties['series_image'] = self.get_image_url(tv_data['poster_path'], 'w500')
            
            if tv_data.get('backdrop_path'):
                properties['backdrop'] = self.get_image_url(tv_data['backdrop_path'], 'w1280')
            
            # Limpiar valores None
            properties = {k: v for k, v in properties.items() if v is not None}
            
            return json.dumps(properties, ensure_ascii=False)
            
        except Exception as e:
            self.logger.error(f"Error creating series properties JSON: {e}")
            return "{}"
    
    def batch_movie_search(self, movies: List[Dict[str, Any]], max_workers: int = 5) -> List[Dict[str, Any]]:
        """Búsqueda en lote de películas con threading"""
        import concurrent.futures
        
        results = []
        
        def search_single_movie(movie_info):
            title = movie_info.get('title', '')
            year = movie_info.get('year')
            movie_id = movie_info.get('id')
            
            search_result = self.smart_movie_search(title, year)
            
            return {
                'original_id': movie_id,
                'original_title': title,
                'original_year': year,
                'search_result': search_result,
                'timestamp': datetime.now().isoformat()
            }
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_movie = {executor.submit(search_single_movie, movie): movie for movie in movies}
            
            for future in concurrent.futures.as_completed(future_to_movie):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    movie = future_to_movie[future]
                    self.logger.error(f"Error processing movie {movie.get('title', 'Unknown')}: {e}")
                    results.append({
                        'original_id': movie.get('id'),
                        'original_title': movie.get('title', ''),
                        'error': str(e),
                        'timestamp': datetime.now().isoformat()
                    })
        
        return results
    
    def test_api_connection(self) -> Dict[str, Any]:
        """Probar conexión con la API de TMDB"""
        try:
            result = self._make_request('configuration')
            
            if result:
                return {
                    'status': 'success',
                    'message': 'Conexión exitosa con TMDB API',
                    'api_key_valid': True,
                    'config': result
                }
            else:
                return {
                    'status': 'error',
                    'message': 'No se pudo conectar con TMDB API',
                    'api_key_valid': False
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Error de conexión: {str(e)}',
                'api_key_valid': False
            }
    
    def get_trending_movies(self, time_window: str = 'day') -> List[Dict]:
        """Obtener películas en tendencia"""
        endpoint = f'trending/movie/{time_window}'
        result = self._make_request(endpoint, {'language': 'es-ES'})
        
        if result and 'results' in result:
            return result['results']
        return []
    
    def get_trending_tv(self, time_window: str = 'day') -> List[Dict]:
        """Obtener series en tendencia"""
        endpoint = f'trending/tv/{time_window}'
        result = self._make_request(endpoint, {'language': 'es-ES'})
        
        if result and 'results' in result:
            return result['results']
        return []
