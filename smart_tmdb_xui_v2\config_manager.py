#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration Manager for Smart TMDB XUI v2
Gestor de configuración centralizado
"""

import json
import os
from typing import Dict, Any, Optional, List
import logging
from pathlib import Path

class ConfigManager:
    def __init__(self):
        self.config_dir = Path(__file__).parent / 'config'
        self.config_file = self.config_dir / 'settings.json'
        self.connections_file = self.config_dir / 'connections.json'
        
        # Crear directorio de configuración si no existe
        self.config_dir.mkdir(exist_ok=True)
        
        # Configuración por defecto
        self.default_config = {
            'app': {
                'name': 'Smart TMDB XUI v2',
                'version': '2.0.0',
                'theme': 'gaming_dark',
                'language': 'es',
                'auto_connect': False,
                'auto_update_metrics': True,
                'update_interval': 300  # 5 minutos
            },
            'database': {
                'connection_timeout': 10,
                'query_timeout': 30,
                'auto_reconnect': True,
                'max_retries': 3,
                'batch_size': 1000
            },
            'tmdb': {
                'api_key': '',
                'language': 'es-ES',
                'rate_limit': 0.25,
                'image_size': 'w500',
                'backdrop_size': 'w1280',
                'cache_duration': 3600  # 1 hora
            },
            'corrections': {
                'auto_fix_timestamps': True,
                'auto_fix_containers': True,
                'auto_assign_icons': True,
                'target_timestamp': 1752456887,  # 2025-07-14
                'default_container': 'mkv',
                'default_order': 1
            },
            'ui': {
                'window_width': 1400,
                'window_height': 900,
                'auto_refresh': True,
                'show_debug': False,
                'log_level': 'INFO'
            }
        }
        
        # Cargar configuración
        self.config = self.load_config()
        
        # Configurar logging
        self.setup_logging()
    
    def setup_logging(self):
        """Configurar sistema de logging"""
        log_level = getattr(logging, self.config['ui']['log_level'], logging.INFO)
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config_dir / 'app.log'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def load_config(self) -> Dict[str, Any]:
        """Cargar configuración desde archivo"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                
                # Merge con configuración por defecto
                config = self.default_config.copy()
                self._deep_update(config, loaded_config)
                return config
            else:
                # Crear archivo con configuración por defecto
                self.save_config(self.default_config)
                return self.default_config.copy()
                
        except Exception as e:
            logging.error(f"Error cargando configuración: {e}")
            return self.default_config.copy()
    
    def save_config(self, config: Optional[Dict[str, Any]] = None) -> bool:
        """Guardar configuración en archivo"""
        try:
            config_to_save = config or self.config
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=4, ensure_ascii=False)
            
            if not config:
                self.config = config_to_save
                
            return True
            
        except Exception as e:
            logging.error(f"Error guardando configuración: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """Obtener valor de configuración usando notación punto"""
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """Establecer valor de configuración usando notación punto"""
        keys = key.split('.')
        config = self.config
        
        try:
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            config[keys[-1]] = value
            return self.save_config()
            
        except Exception as e:
            logging.error(f"Error estableciendo configuración {key}: {e}")
            return False
    
    def load_connections(self) -> List[Dict[str, Any]]:
        """Cargar conexiones guardadas"""
        try:
            if self.connections_file.exists():
                with open(self.connections_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
            
        except Exception as e:
            logging.error(f"Error cargando conexiones: {e}")
            return []
    
    def save_connection(self, connection: Dict[str, Any]) -> bool:
        """Guardar nueva conexión"""
        try:
            connections = self.load_connections()
            
            # Buscar si ya existe la conexión
            for i, conn in enumerate(connections):
                if (conn.get('host') == connection.get('host') and 
                    conn.get('database') == connection.get('database') and
                    conn.get('user') == connection.get('user')):
                    connections[i] = connection
                    break
            else:
                connections.append(connection)
            
            with open(self.connections_file, 'w', encoding='utf-8') as f:
                json.dump(connections, f, indent=4, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            logging.error(f"Error guardando conexión: {e}")
            return False
    
    def remove_connection(self, host: str, database: str, user: str) -> bool:
        """Eliminar conexión guardada"""
        try:
            connections = self.load_connections()
            
            connections = [conn for conn in connections 
                         if not (conn.get('host') == host and 
                               conn.get('database') == database and
                               conn.get('user') == user)]
            
            with open(self.connections_file, 'w', encoding='utf-8') as f:
                json.dump(connections, f, indent=4, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            logging.error(f"Error eliminando conexión: {e}")
            return False
    
    def get_tmdb_config(self) -> Dict[str, Any]:
        """Obtener configuración específica de TMDB"""
        return {
            'api_key': self.get('tmdb.api_key', ''),
            'language': self.get('tmdb.language', 'es-ES'),
            'rate_limit': self.get('tmdb.rate_limit', 0.25),
            'image_size': self.get('tmdb.image_size', 'w500'),
            'backdrop_size': self.get('tmdb.backdrop_size', 'w1280'),
            'cache_duration': self.get('tmdb.cache_duration', 3600)
        }
    
    def set_tmdb_api_key(self, api_key: str) -> bool:
        """Establecer API key de TMDB"""
        return self.set('tmdb.api_key', api_key)
    
    def get_database_config(self) -> Dict[str, Any]:
        """Obtener configuración de base de datos"""
        return {
            'connection_timeout': self.get('database.connection_timeout', 10),
            'query_timeout': self.get('database.query_timeout', 30),
            'auto_reconnect': self.get('database.auto_reconnect', True),
            'max_retries': self.get('database.max_retries', 3),
            'batch_size': self.get('database.batch_size', 1000)
        }
    
    def get_corrections_config(self) -> Dict[str, Any]:
        """Obtener configuración de correcciones"""
        return {
            'auto_fix_timestamps': self.get('corrections.auto_fix_timestamps', True),
            'auto_fix_containers': self.get('corrections.auto_fix_containers', True),
            'auto_assign_icons': self.get('corrections.auto_assign_icons', True),
            'target_timestamp': self.get('corrections.target_timestamp', 1752456887),
            'default_container': self.get('corrections.default_container', 'mkv'),
            'default_order': self.get('corrections.default_order', 1)
        }
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict) -> Dict:
        """Actualización profunda de diccionarios"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
        return base_dict
    
    def reset_to_defaults(self) -> bool:
        """Resetear configuración a valores por defecto"""
        try:
            self.config = self.default_config.copy()
            return self.save_config()
        except Exception as e:
            logging.error(f"Error reseteando configuración: {e}")
            return False
    
    def export_config(self, file_path: str) -> bool:
        """Exportar configuración a archivo"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            logging.error(f"Error exportando configuración: {e}")
            return False
    
    def import_config(self, file_path: str) -> bool:
        """Importar configuración desde archivo"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # Validar y merge
            self._deep_update(self.config, imported_config)
            return self.save_config()
            
        except Exception as e:
            logging.error(f"Error importando configuración: {e}")
            return False
