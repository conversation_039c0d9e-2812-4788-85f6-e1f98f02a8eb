#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test del diálogo arreglado
"""

import tkinter as tk
from config_manager import ConfigManager
from connection_dialog import SmartConnectionDialog

def test_fixed_dialog():
    print("🧪 Probando diálogo arreglado...")
    
    root = tk.Tk()
    root.withdraw()  # Ocultar ventana principal
    
    config_manager = ConfigManager()
    
    def on_connect(data):
        print(f"✅ Conectado: {data}")
        root.quit()
    
    # Crear diálogo
    dialog = SmartConnectionDialog(root, config_manager, on_connect)
    
    print("🎯 Diálogo creado. Verifica que ahora SÍ aparezcan los botones:")
    print("   - 🧪 Probar Conexión (izquierda)")
    print("   - 🔌 Conectar (derecha)")
    print("   - ❌ Cancelar (derecha)")
    print("   - Status message (abajo)")
    
    root.mainloop()

if __name__ == "__main__":
    test_fixed_dialog()
