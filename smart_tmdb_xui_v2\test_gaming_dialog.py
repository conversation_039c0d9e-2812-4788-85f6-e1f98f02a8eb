#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test del Gaming Connection Dialog
"""

from simple_dialog import SimpleConnectionDialog
import tkinter as tk

def test_gaming_dialog():
    """Test independiente del diálogo gaming"""
    print("🚀 Testing Gaming Connection Dialog...")
    print("⚡ NVIDIA/ROG/Gemini Theme Active")
    
    # Crear ventana principal
    root = tk.Tk()
    root.withdraw()  # Ocultar ventana principal
    
    # Crear diálogo gaming
    dialog = SimpleConnectionDialog(root)
    result = dialog.show()
    
    if result:
        print("\n✅ ¡GAMING CONNECTION SUCCESSFUL!")
        print("🎮 Connection Data:")
        for key, value in result.items():
            if key == 'password':
                print(f"  🔐 {key}: {'*' * len(value)}")
            else:
                print(f"  {key}: {value}")
    else:
        print("❌ Connection aborted by user")
    
    root.destroy()

if __name__ == "__main__":
    test_gaming_dialog()
