#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Smart TMDB XUI v2 - Launcher
Selector de versión para ejecutar
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import os
import sys

class AppLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 Smart TMDB XUI v2 - Launcher")
        self.root.geometry("600x400")
        self.root.configure(bg='#0d1117')
        self.root.resizable(False, False)
        
        # Centrar ventana
        self.center_window()
        
        self.create_interface()
    
    def center_window(self):
        """Centrar ventana en la pantalla"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_interface(self):
        """Crear interfaz del launcher"""
        
        # Header
        header_frame = tk.Frame(self.root, bg='#0d1117')
        header_frame.pack(fill='x', padx=20, pady=20)
        
        title_label = tk.Label(header_frame, 
                              text="🚀 Smart TMDB XUI v2",
                              bg='#0d1117', 
                              fg='#58a6ff',
                              font=('Segoe UI', 18, 'bold'))
        title_label.pack()
        
        subtitle_label = tk.Label(header_frame,
                                 text="Nueva Generación - Selector de Versión",
                                 bg='#0d1117',
                                 fg='#c9d1d9',
                                 font=('Segoe UI', 11))
        subtitle_label.pack(pady=(5, 0))
        
        # Separador
        separator = tk.Frame(self.root, height=2, bg='#30363d')
        separator.pack(fill='x', padx=20, pady=10)
        
        # Opciones
        options_frame = tk.Frame(self.root, bg='#0d1117')
        options_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Versión Demo
        demo_frame = tk.Frame(options_frame, bg='#161b22', relief='solid', bd=1)
        demo_frame.pack(fill='x', pady=10)
        
        demo_title = tk.Label(demo_frame,
                             text="🎯 Versión de Demostración",
                             bg='#161b22',
                             fg='#3fb950',
                             font=('Segoe UI', 12, 'bold'))
        demo_title.pack(anchor='w', padx=15, pady=(15, 5))
        
        demo_desc = tk.Label(demo_frame,
                            text="• Interfaz moderna con todas las funcionalidades\n• Simulación de correcciones ya implementadas\n• Perfecta para explorar la nueva aplicación\n• ✅ Recomendada para primeros usos",
                            bg='#161b22',
                            fg='#c9d1d9',
                            font=('Segoe UI', 9),
                            justify='left')
        demo_desc.pack(anchor='w', padx=15, pady=(0, 10))
        
        demo_btn = tk.Button(demo_frame,
                           text="🎯 Ejecutar Demo",
                           bg='#3fb950',
                           fg='white',
                           font=('Segoe UI', 10, 'bold'),
                           padx=20,
                           pady=8,
                           relief='flat',
                           cursor='hand2',
                           command=self.launch_demo)
        demo_btn.pack(anchor='w', padx=15, pady=(0, 15))
        
        # Versión Completa
        full_frame = tk.Frame(options_frame, bg='#161b22', relief='solid', bd=1)
        full_frame.pack(fill='x', pady=10)
        
        full_title = tk.Label(full_frame,
                             text="⚡ Versión Completa",
                             bg='#161b22',
                             fg='#58a6ff',
                             font=('Segoe UI', 12, 'bold'))
        full_title.pack(anchor='w', padx=15, pady=(15, 5))
        
        full_desc = tk.Label(full_frame,
                           text="• Aplicación completa con conexión real a BD\n• Todas las herramientas de corrección activas\n• Integración completa con TMDB API\n• ⚠️ Requiere configuración de conexión",
                           bg='#161b22',
                           fg='#c9d1d9',
                           font=('Segoe UI', 9),
                           justify='left')
        full_desc.pack(anchor='w', padx=15, pady=(0, 10))
        
        full_btn = tk.Button(full_frame,
                           text="⚡ Ejecutar Completa",
                           bg='#58a6ff',
                           fg='white',
                           font=('Segoe UI', 10, 'bold'),
                           padx=20,
                           pady=8,
                           relief='flat',
                           cursor='hand2',
                           command=self.launch_full)
        full_btn.pack(anchor='w', padx=15, pady=(0, 15))
        
        # Footer
        footer_frame = tk.Frame(self.root, bg='#0d1117')
        footer_frame.pack(fill='x', padx=20, pady=10)
        
        info_label = tk.Label(footer_frame,
                            text="💡 Todas las correcciones han sido implementadas exitosamente\n🎉 Tu base de datos está 100% optimizada",
                            bg='#0d1117',
                            fg='#8b949e',
                            font=('Segoe UI', 9),
                            justify='center')
        info_label.pack()
        
        # Botón salir
        exit_btn = tk.Button(footer_frame,
                           text="❌ Salir",
                           bg='#f85149',
                           fg='white',
                           font=('Segoe UI', 9),
                           padx=15,
                           pady=5,
                           relief='flat',
                           cursor='hand2',
                           command=self.root.quit)
        exit_btn.pack(pady=(10, 0))
    
    def launch_demo(self):
        """Ejecutar versión demo"""
        try:
            subprocess.Popen([sys.executable, 'demo_app.py'])
            messagebox.showinfo("🎯 Demo Iniciado", 
                               "La versión de demostración se está ejecutando.\n\n✅ Explora todas las funcionalidades implementadas")
            self.root.quit()
        except Exception as e:
            messagebox.showerror("Error", f"No se pudo ejecutar demo_app.py:\n{e}")
    
    def launch_full(self):
        """Ejecutar versión completa"""
        response = messagebox.askyesno("⚡ Versión Completa",
                                      "¿Estás seguro de ejecutar la versión completa?\n\n" +
                                      "Esta versión requiere:\n" +
                                      "• Conexión activa a la base de datos\n" +
                                      "• API key de TMDB configurado\n\n" +
                                      "¿Continuar?")
        if response:
            try:
                # Verificar si existe el main completo
                if os.path.exists('smart_main.py'):
                    subprocess.Popen([sys.executable, 'smart_main.py'])
                else:
                    subprocess.Popen([sys.executable, 'main.py'])
                
                messagebox.showinfo("⚡ Aplicación Iniciada",
                                   "La versión completa se está ejecutando.\n\n" +
                                   "🔌 Configura la conexión a la base de datos\n" +
                                   "🎯 Todas las herramientas están disponibles")
                self.root.quit()
            except Exception as e:
                messagebox.showerror("Error", f"No se pudo ejecutar la aplicación completa:\n{e}")
    
    def run(self):
        """Ejecutar el launcher"""
        self.root.mainloop()

if __name__ == "__main__":
    launcher = AppLauncher()
    launcher.run()
